name: "业务流程梳理工作流"
description: "用于梳理和记录系统业务流程的工作流"

# 任务目标列表
todos: []

# 前置步骤
pre_steps:
  - title: "询问用户要梳理的业务流程"
    rule:
      - "询问用户要梳理的具体业务流程"
      - "询问用户是否可以提供入口文件或相关文件"
      - "询问用户是否有其他补充信息"
      - "必须用户确认后，才能进入下一步 /task next"

  - title: "创建业务流程梳理工作流"
    rule:
      - "请根据用户描述，整理JSON_STRING，然后执行脚本 task.py update {{JSON_STRING}}"
      - JSON_STRING 模板: |
          "{\"title\": \"xxxx\", \"description\": \"xxxx\"}"

# 任务步骤
steps:
  - title: "收集信息"
    rule:
      - "必须读取 _agent-local/knowledge/项目说明.md"
      - "必须读取 _agent-local/guidelines/技术架构.md"
      - "使用 ls -R src 搜索代码库中相关的入口文件和主要类"
      - "使用 cat 命令查看相关文件内容"
      - "分析入口文件（api/定时任务/mq等）"
      - "分析涉及到的主要类和方法"
      - "分析处理的时序"
      - "分析每个处理步骤关联的源文件"
      - "分析访问的组件（oss/redis/db/本地文件等）"
      - "需要用户确认"
    output:
      - |
        请按以下格式输出分析结果:
        1. 入口文件:
          - 类型: xxx
          - 文件路径: xxx
          - 主要类和方法: xxx
          - 触发方式: xxx
          - MQ信息: [如果入口是消息队列]
            - Topic:
              - 名称: xxx
              - 描述: xxx
            - 消息格式: [JSON/XML/其他]
            - 消息示例: |
              ```json
              {
                // 消息字段示例
              }
              ```
            - 消费者类: xxx
            - 消费方法: xxx

        2. 主要类和方法:
          - 类名: xxx
          - 方法名: xxx
          - 功能描述: xxx
          - 关联文件: xxx

        3. 处理时序:
          - 步骤1: xxx
          - 步骤2: xxx
          - 步骤3: xxx
          ...

        4. 处理步骤关联文件:
          - 步骤1:
            - 文件1: xxx
            - 文件2: xxx
          - 步骤2:
            - 文件1: xxx
            - 文件2: xxx
          ...

        5. 访问组件:
          - OSS: xxx
          - Redis: xxx
          - DB: xxx
          - 本地文件: xxx

  - title: "输出流程文档"
    rule:
      - "以 template/美团团购对账流程.md.tpl 的格式为模板"
      - "必须包含 mermaid 格式的时序图"
      - "时序图必须包含所有主要参与者和交互流程"
      - "输出完整的流程文档"
      - "需要用户确认"
    output:
      - doc: biz_flow.md
        请按以下格式输出: |
          # xxx业务流程

          ## 触发机制 (Trigger Mechanism)

          1. 消息触发 (Message Trigger)
            - 通过xxx触发
            - Topic信息：
              - 名称：xxx
              - 描述：xxx
            - 消息格式：[JSON/XML/其他]
            - 消息示例：|
              ```json
              {
                // 消息字段示例
              }
              ```
            - 入口信息：
              - 消费者类：xxx
              - 消费方法：xxx
              - 信息来源文件：xxx
              - 处理流程：xxx

          2. 手动触发 (Manual Trigger)
            - 入口信息：
              - 入口类：xxx
              - 入口方法：xxx
              - 信息来源文件：xxx
              - 处理流程：xxx

          3. 定时任务触发 (Scheduled Task Trigger)
            - 入口信息：
              - 入口类：xxx
              - 入口方法：xxx
              - 信息来源文件：xxx
              - 处理流程：xxx

          ## xxx业务流程详细流程

          ```mermaid
          sequenceDiagram
            %% 定义参与者
            participant User as 用户
            participant Controller as 控制器
            participant Service as 服务层
            participant DAO as 数据访问层
            participant MQ as 消息队列
            participant OSS as 对象存储
            participant Redis as 缓存
            participant DB as 数据库

            %% 定义交互流程
            User->>Controller: 发起请求
            Controller->>Service: 调用服务
            Service->>DAO: 查询数据
            DAO->>DB: 执行SQL
            DB-->>DAO: 返回结果
            DAO-->>Service: 返回数据
            Service->>MQ: 发送消息
            Service->>OSS: 上传文件
            Service->>Redis: 缓存数据
            Service-->>Controller: 返回结果
            Controller-->>User: 响应请求
          ```

          ## 处理阶段说明

          1. xxx阶段
            - 主要类和方法：xxx
            - 信息来源文件：xxx
            - 处理内容：xxx

          2. xxx阶段
            - 主要类和方法：xxx
            - 信息来源文件：xxx
            - 处理内容：xxx

          ...

          ## 异常处理机制

          - xxx
          - xxx
          ...

          ## 性能优化

          - xxx
          - xxx
          ...

  - title: "移动文档"
    rule:
      - "用户确认后，将biz_flow.md移动到_agent-local/knowledge/biz_flow下"
      - "需要用户确认新的文件名, 如: xxx.md"
    output:
      - "文档已移动到_agent-local/knowledge/biz_flow/xxx.md"

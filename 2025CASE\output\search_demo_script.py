"""
知识库检索演示脚本
"""

import os
import sys
import json
import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

try:
    from knowledge_base import KnowledgeBaseManager
except ImportError:
    print("错误: 无法导入knowledge_base模块，请确保该模块已正确安装")
    sys.exit(1)

def load_import_result():
    """加载导入结果"""
    try:
        with open("D:\\0AI\\TESTCASE\\2025CASE\\output\\import_result.json", 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载导入结果时出错: {e}")
        return None

def search_demo():
    """知识库检索演示"""
    # 加载导入结果
    import_result = load_import_result()
    if not import_result:
        print("无法加载导入结果")
        return
    
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 准备检索示例
    search_examples = [
        {"keyword": "满打折", "limit": 5},
        {"keyword": "会员等级", "limit": 5},
        {"keyword": "POS", "limit": 5},
        {"keyword": "测试场景", "limit": 5}
    ]
    
    # 执行检索并收集结果
    examples_results = []
    for example in search_examples:
        keyword = example["keyword"]
        limit = example["limit"]
        
        print(f"\n执行检索: {keyword}")
        results = kb.search(keyword, limit=limit)
        
        # 格式化结果
        formatted_results = []
        for result in results:
            formatted_results.append({
                "title": result.get("title", "未知标题"),
                "score": round(result.get("score", 0), 2),
                "category": result.get("category", "未分类"),
                "added_at": result.get("added_at", "未知时间")
            })
        
        examples_results.append({
            "keyword": keyword,
            "results": formatted_results
        })
    
    # 生成Markdown文档
    md_content = f"""# 知识库检索演示

## 导入信息
- 文档ID: {import_result.get('document_id')}
- 文档路径: {import_result.get('document_path')}
- 导入时间: {import_result.get('import_time')}

## 检索示例
"""
    
    for i, example in enumerate(examples_results):
        md_content += f"### 示例 {i+1}: {example['keyword']}\n\n"
        md_content += "**查询代码:**\n"
        md_content += "```python\n"
        md_content += "from knowledge_base import KnowledgeBaseManager\n\n"
        md_content += "kb = KnowledgeBaseManager()\n"
        md_content += f"results = kb.search(\"{example['keyword']}\")\n"
        md_content += "```\n\n"
        
        md_content += "**检索结果:**\n"
        if example['results']:
            for result in example['results']:
                md_content += f"- 文档: {result['title']}\n"
                md_content += f"  - 相关度: {result['score']}\n"
                md_content += f"  - 分类: {result['category']}\n"
                md_content += f"  - 添加时间: {result['added_at']}\n"
        else:
            md_content += "- 未找到匹配结果\n"
        
        md_content += "\n"
    
    md_content += """## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")
```

### 检索文档
```python
# 关键词搜索
results = kb.search("登录功能")

# 按分类搜索
results = kb.search("登录功能", category="功能测试")

# 限制结果数量
results = kb.search("登录功能", limit=5)
```

### 查看文档
```python
doc = kb.get_document("doc_id")
print(doc["title"])
print(doc["content"])
```

### 列出所有文档
```python
all_docs = kb.list_documents()
for doc in all_docs:
    print(f"{doc['id']}: {doc['title']}")
```

### 删除文档
```python
kb.delete_document("doc_id")
```
"""
    
    # 保存Markdown文档
    output_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\search_demo.md"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"\n知识库检索演示已保存到: {output_path}")
    
    return md_content

if __name__ == "__main__":
    search_demo()

import os
import sys

print("Python version:", sys.version)
print("Current working directory:", os.getcwd())

# 打印当前环境变量
print("\n当前环境变量:")
env_vars = ['USER_ID', 'USER_NAME', 'LOCAL_WORKSPACE', 'LOCAL_RULES_PATH']
for key in env_vars:
    print(f"{key}={os.environ.get(key, 'Not set')}")

# 检查文件是否存在
paths_to_check = [
    "D:\\0AI\\TESTCASE\\_agent-local\\.env",
    "D:\\0AI\\TESTCASE\\.agent\\.env",
    "D:\\0AI\\TESTCASE\\_agent-local\\workflows\\work\\test_case_generator.yml"
]

print("\n文件检查:")
for path in paths_to_check:
    print(f"{path} 存在: {os.path.exists(path)}")

print("完成！")

name: 生成前端项目知识库
description: 通过分析指定前端项目代码，生成包括项目结构、技术栈、文件目录结构与业务模块对应关系的Markdown知识库文档，并支持增量更新
current_todo_index: 0
current_step_index: 0
todos:
  - title: 生成前端项目知识库文档
    description: 分析用户指定的前端项目代码，提取项目结构、技术栈、文件目录结构与业务模块对应关系，生成Markdown格式的知识库文档，并支持增量更新

steps:
  - title: 项目信息收集
    worker: planner
    rule:
      - 确认用户要分析的前端项目路径
      - 确认知识库的存储路径
      - 收集项目的基本信息，如项目名称、主要目的
      - 确定需要重点分析的部分
      - 不进行实际的分析工作，只收集必要信息
    output:
      - doc: _agent-local/knowledge/app/project_info.md
      - 请按需遵循以下格式输出: |
          # 项目信息

          ## 基本信息
          - 项目名称: {{项目名称}}
          - 项目路径: {{项目路径}}
          - 知识库存储路径: {{知识库存储路径}}

          ## 分析范围
          {{描述需要分析的项目范围和重点关注的部分}}

          ## 用户期望
          {{记录用户对知识库的特定要求或期望}}

  - title: 前端项目结构分析
    worker: architect
    rule:
      - 分析前端项目根目录下的所有文件和目录结构
      - 识别项目的主要目录及其用途
      - 分析项目的配置文件（如package.json、tsconfig.json等）
      - 识别前端构建流程和脚本
      - 总结项目的整体结构
    input:
      - doc: _agent-local/knowledge/app/project_info.md
    output:
      - doc: _agent-local/knowledge/app/project_structure.md
      - 请按需遵循以下格式输出: |
          # 前端项目结构分析

          ## 项目概览
          {{项目简介和主要功能描述}}

          ## 目录结构
          ```
          project-root/
          ├── public/
          │   ├── index.html
          │   └── assets/
          ├── src/
          │   ├── components/
          │   ├── pages/
          │   ├── assets/
          │   ├── utils/
          │   ├── App.jsx
          │   └── index.js
          ├── package.json
          └── vite.config.js
          ```

          ## 主要目录说明
          {{列出主要目录及其用途}}

          ## 配置文件分析
          {{列出关键配置文件及其作用}}

          ## 构建流程
          {{描述前端项目的构建和部署流程}}

  - title: 前端技术栈分析
    worker: developer
    rule:
      - 分析项目的依赖项（package.json中的dependencies）
      - 识别UI框架和组件库
      - 识别状态管理和路由方案
      - 识别构建工具和开发环境
      - 总结项目的技术选型和架构特点
    input:
      - doc: _agent-local/knowledge/app/project_info.md
      - doc: _agent-local/knowledge/app/project_structure.md
    output:
      - doc: _agent-local/knowledge/app/tech_stack.md
      - 请按需遵循以下格式输出: |
          # 前端技术栈分析

          ## 核心框架
          {{列出项目使用的主要框架}}
          
          例如:
          - UI框架: React v18.2.0
          - 状态管理: Redux v4.2.1
          - 路由: React Router v6.4.3

          ## UI组件
          {{UI组件库和相关工具}}
          
          例如:
          | 库 | 版本 | 用途 |
          |---|-----|-----|
          | Ant Design | 5.3.0 | UI组件库 |
          | TailwindCSS | 3.2.4 | 样式工具 |
          | styled-components | 5.3.6 | CSS-in-JS |

          ## 开发工具
          {{开发相关的库和工具}}
          
          例如:
          - 构建工具: Vite v4.1.0
          - 包管理: npm v9.3.1
          - 语言: TypeScript v4.9.5
          - 代码规范: ESLint v8.34.0

  - title: 前端业务模块分析
    worker: developer
    rule:
      - 分析源代码中的主要业务模块和组件
      - 识别组件之间的依赖关系
      - 分析页面结构和导航流程
      - 映射文件目录与业务功能的对应关系
      - 整理出清晰的业务模块结构图
    input:
      - doc: _agent-local/knowledge/app/project_info.md
      - doc: _agent-local/knowledge/app/project_structure.md
      - doc: _agent-local/knowledge/app/tech_stack.md
    output:
      - doc: _agent-local/knowledge/app/business_modules.md
      - 请按需遵循以下格式输出: |
          # 前端业务模块分析

          ## 模块概览
          {{业务模块概述和关系图}}
          
          例如:
          ```mermaid
          graph TD
            A[应用入口] --> B[登录模块]
            A --> C[主面板]
            C --> D[用户管理]
            C --> E[内容管理]
            C --> F[设置]
          ```

          ## 核心业务模块
          {{列出核心业务模块及其功能}}
          
          例如:
          ### 1. 登录模块
          - 路径: `src/pages/Login/`
          - 主要组件: `LoginForm.jsx`, `AuthContext.jsx`
          - 功能: 处理用户认证
          
          ### 2. 用户管理
          - 路径: `src/pages/Users/<USER>
          - 主要组件: `UserList.jsx`, `UserForm.jsx`
          - 功能: 用户信息管理

          ## 目录与业务对应关系
          {{列出目录与业务功能的映射关系}}
          
          例如:
          | 目录 | 业务功能 |
          |------|---------|
          | src/pages/Login/ | 用户登录和认证 |
          | src/pages/Dashboard/ | 控制面板和数据概览 |
          | src/components/common/ | 通用UI组件 |

          ## 组件依赖
          {{关键组件及其依赖关系}}
          
          例如:
          ```mermaid
          graph TD
            A[App] --> B[Layout]
            B --> C[Header]
            B --> D[Sidebar]
            B --> E[Content]
            
            E --> F[Dashboard]
            E --> G[UserManagement]
            
            G --> H[UserList]
            G --> I[UserForm]
          ```

  - title: 知识库整合
    worker: developer
    rule:
      - 整合前面步骤生成的所有文档
      - 添加导航和索引
      - 确保内容的准确性和一致性
      - 生成最终的项目知识库文档
    input:
      - doc: _agent-local/knowledge/app/project_info.md
      - doc: _agent-local/knowledge/app/project_structure.md
      - doc: _agent-local/knowledge/app/tech_stack.md
      - doc: _agent-local/knowledge/app/business_modules.md
    output:
      - doc: _agent-local/knowledge/app/index.md
      - 请按需遵循以下格式输出: |
          # {{项目名称}}知识库

          ## 目录
          - [项目结构](#项目结构)
          - [技术栈](#技术栈)
          - [业务模块](#业务模块)
          
          ## 项目结构
          {{项目结构概览，链接到详细文档}}
          
          例如:
          项目采用标准的React应用结构，主要分为组件、页面和工具函数。
          [查看完整项目结构](./project_structure.md)
          
          ## 技术栈
          {{技术栈概览，链接到详细文档}}
          
          例如:
          项目基于React 18构建，使用Redux管理状态，Ant Design作为UI库。
          [查看完整技术栈](./tech_stack.md)
          
          ## 业务模块
          {{业务模块概览，链接到详细文档}}
          
          例如:
          应用包含登录、用户管理、内容管理三个核心模块。
          [查看业务模块详情](./business_modules.md)
          
          ## 最后更新
          {{更新时间}}

  - title: 知识库验证
    worker: tester
    rule:
      - 检查知识库文档的准确性和完整性
      - 验证目录结构与实际项目是否匹配
      - 检查技术栈分析是否全面
      - 验证业务模块与实际功能是否对应
      - 提出改进建议
    input:
      - doc: _agent-local/knowledge/app/index.md
      - doc: _agent-local/knowledge/app/project_structure.md
      - doc: _agent-local/knowledge/app/tech_stack.md
      - doc: _agent-local/knowledge/app/business_modules.md
    output:
      - doc: _agent-local/knowledge/app/validation_report.md
      - 请按需遵循以下格式输出: |
          # 知识库验证报告

          ## 验证结果
          {{整体验证结果}}
          
          例如:
          | 文档 | 状态 | 准确性 | 完整性 |
          |------|------|-------|-------|
          | 项目结构 | ✅ 通过 | 90% | 85% |
          | 技术栈 | ✅ 通过 | 95% | 90% |
          | 业务模块 | ⚠️ 需改进 | 80% | 85% |

          ## 发现的问题
          {{列出发现的问题和不准确之处}}
          
          例如:
          - 项目结构文档缺少对构建配置的描述
          - 业务模块中部分组件关系描述不准确
          - 技术栈分析未包含所有第三方库

          ## 改进建议
          {{具体的改进建议}}
          
          例如:
          1. 补充构建配置说明
          2. 更新组件关系图
          3. 完善第三方库列表

  - title: 增量更新机制设计
    worker: architect
    rule:
      - 设计知识库的增量更新机制
      - 确定需要监控的文件变化
      - 设计更新策略和频率
    input:
      - doc: _agent-local/knowledge/app/project_info.md
      - doc: _agent-local/knowledge/app/validation_report.md
    output:
      - doc: _agent-local/knowledge/app/update_strategy.md
      - 请按需遵循以下格式输出: |
          # 知识库增量更新策略

          ## 更新机制
          {{描述如何检测项目变化}}
          
          例如:
          ```mermaid
          graph TD
            A[定期检查] -->|扫描文件| B[检测变更]
            B -->|有变更| C[更新文档]
            B -->|无变更| A
            C --> D[记录更新]
            D --> A
          ```

          ## 监控范围
          {{需要监控的文件和目录}}
          
          例如:
          | 文件/目录 | 优先级 | 对应文档 |
          |----------|--------|---------|
          | package.json | 高 | 技术栈 |
          | src/components/ | 高 | 业务模块 |
          | src/pages/ | 高 | 业务模块 |
          | public/ | 中 | 项目结构 |

          ## 更新策略
          {{更新的触发条件和执行方式}}
          
          例如:
          - 每周自动检查一次
          - 项目版本更新时触发
          - 支持手动触发全量更新

  - title: 增量更新实现
    worker: developer
    rule:
      - 根据更新策略实现增量更新功能
      - 设计文件变更检测机制
      - 实现选择性更新相关文档
      - 实现版本记录
    input:
      - doc: _agent-local/knowledge/app/update_strategy.md
      - doc: _agent-local/knowledge/app/project_info.md
    output:
      - doc: _agent-local/knowledge/app/update_implementation.md
      - 请按需遵循以下格式输出: |
          # 增量更新实现方案

          ## 实现方案
          {{描述增量更新功能的实现方案}}
          
          例如:
          ```mermaid
          graph TD
            A[检测变更] --> B{文件类型}
            B -->|package.json| C[更新技术栈]
            B -->|组件文件| D[更新业务模块]
            B -->|配置文件| E[更新项目结构]
            C --> F[更新主文档]
            D --> F
            E --> F
          ```

          ## 变更检测
          {{描述如何检测文件变更}}
          
          例如:
          - 使用文件哈希值比较
          - 记录关键文件的修改时间
          - 定期扫描项目目录结构变化

          ## 版本控制
          {{描述版本控制方案}}
          
          例如:
          - 使用日期版本号 (YYYY-MM-DD)
          - 保存最近5个版本的文档
          - 记录每次更新的变更内容摘要
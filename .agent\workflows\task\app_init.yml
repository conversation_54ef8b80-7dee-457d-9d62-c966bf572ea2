name: 前端项目快速初始化任务流
description: 适合新手的前端项目环境配置与启动流程，包含基础环境检查与安装
todos: []

steps:
  - title: 项目技术栈识别
    worker: developer
    rule:
      - 识别项目类型(前端/后端/全栈)
      - 检查项目使用的主要框架
      - 识别包管理工具(npm/yarn/pnpm)
      - 识别所需的Node.js版本
      - 检查特殊的环境要求
      - 无需确认，自动进行下一步骤

  - title: 基础环境检查、配置与依赖安装
    worker: developer
    rule:
      - 检查操作系统类型和版本
      - 检查相关配置文件(package.json、package-lock.json、pnpm-lock.yaml、yarn.lock)、构建工具版本、依赖包版本等，选择最合适的包管理工具(npm/yarn/pnpm)
      - 检查是否安装了必要的基础工具
        - Git
        - Node.js和包管理工具(npm/yarn/pnpm)
        - nvm (Node版本管理器)
        - Python(如果需要)
      - 检查环境变量配置
      - 检查并配置 Node.js 环境
        - 确认 nvm 是否正确安装
        - 检查 .nvmrc 文件是否存在
        - 分析项目所需node版本(要考虑兼容下、稳定性)
        - 使用 nvm install 安装所需版本(如果未安装)
        - 执行 nvm use 切换到项目所需版本
        - 验证 node -v 输出是否符合预期
      - 判断是否需要开启包管理工具的加速镜像
      - 安装项目依赖前的准备工作
        - 清理缓存(如果需要)
        - 优先使用官方镜像
      - 安装依赖
        - 如果是比较新的项目使用pnpm安装
        - 如果是比较老的项目使用npm安装(npm config set strict-ssl false && npm install --legacy-peer-deps)
      - 如果安装失败，则通过切换镜像源、安装方式(npm/yarn)、关闭SSL等方式
      - 验证依赖安装结果
      - 完成此步骤后，自动进行下一步骤

  - title: 项目启动与验证
    worker: developer
    rule:
      - 阅读文档，按文档中步骤启动项目，并向用户说明启动步骤，让用户进行必要的验证
      - 验证通过后，创建或更新 .nvmrc 文件
    input:
      - _agent-local/knowledge/project-startup.md
      - readme.md
      - README.md
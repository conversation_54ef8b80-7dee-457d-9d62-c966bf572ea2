@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Set UTF-8 encoding
set PYTHONIOENCODING=utf-8

:: 确保脚本在.agent目录下执行
if not exist ".env.tpl" (
    echo 错误：请在 .agent 目录下运行此脚本
    exit /b 1
)

:: 如果.env文件不存在，从.env.tpl创建
if not exist ".env" (
    echo 未找到.env文件，将从.env.tpl创建...
    copy .env.tpl .env > nul
    echo .env文件已创建
)

:: 设置PATH以包含bin目录
set "PATH=%~dp0bin;%PATH%"

:: 检查Python3是否可用
python3 -V >nul 2>&1
if errorlevel 1 (
    echo Python未找到，请访问 https://www.python.org/downloads/ 下载安装
    echo 安装时请勾选"Add Python to PATH"
    echo 安装完成后重新运行此脚本
    exit /b 1
)

:: 安装pip依赖
echo 正在安装 Python 依赖...
python3 -m pip install --upgrade pip
python3 -m pip install -r requirements.txt

:: 读取并确认.env文件中的LOCAL_WORKSPACE变量
for /f "tokens=2 delims==" %%a in ('type .env ^| findstr "LOCAL_WORKSPACE"') do (
    set "local_workspace_dir=%%a"
)

:: 去除可能的引号
set "local_workspace_dir=!local_workspace_dir:"=!"
set "local_workspace_dir=!local_workspace_dir: =!"

:: 显示当前配置并请求用户确认
echo 当前 .env 中配置的 LOCAL_WORKSPACE 为: !local_workspace_dir!
echo 将在以下路径创建/更新目录结构: ..\!local_workspace_dir!\
set /p confirm="是否继续使用此路径？(y/n): "

if /i not "!confirm!"=="y" (
    echo 操作已被用户取消
    exit /b 1
)

:: 创建目录结构
echo 正在创建目录结构...
if not exist "..\!local_workspace_dir!\workspace" mkdir "..\!local_workspace_dir!\workspace"
if not exist "..\!local_workspace_dir!\workflows\task" mkdir "..\!local_workspace_dir!\workflows\task"
if not exist "..\!local_workspace_dir!\workflows\work" mkdir "..\!local_workspace_dir!\workflows\work"
if not exist "..\!local_workspace_dir!\knowledge" mkdir "..\!local_workspace_dir!\knowledge"
if not exist "..\!local_workspace_dir!\roles" mkdir "..\!local_workspace_dir!\roles"
if not exist "..\!local_workspace_dir!\guidelines" mkdir "..\!local_workspace_dir!\guidelines"

:: 更新 .gitignore 文件
echo 正在更新 .gitignore 文件...
set "gitignore_file=..\.gitignore"
set "workspace_ignore=!local_workspace_dir!/workspace/"
set "cursorrules_ignore=.cursorrules"

:: 检查并添加 workspace 忽略规则
findstr /L /X /C:"!workspace_ignore!" "!gitignore_file!" >nul 2>&1
if errorlevel 1 (
    echo !workspace_ignore!>>"!gitignore_file!"
    echo 已将 !workspace_ignore! 添加到 !gitignore_file!
)

:: 检查并添加 .cursorrules 忽略规则
findstr /L /X /C:"!cursorrules_ignore!" "!gitignore_file!" >nul 2>&1
if errorlevel 1 (
    echo !cursorrules_ignore!>>"!gitignore_file!"
    echo 已将 !cursorrules_ignore! 添加到 !gitignore_file!
)

echo 初始化完成！

endlocal

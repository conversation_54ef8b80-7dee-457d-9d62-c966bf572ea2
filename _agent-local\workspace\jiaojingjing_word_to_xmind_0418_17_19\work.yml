id: jiaojingjing_word_to_xmind_0418_17_19
name: Word转XMIND工作流
description: 将Word文档转换为可直接导入XMIND的Markdown格式
rules:
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 生成的所有文档如果没有指定明确路径，则统一放在 {{work_path}} 目录下
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
status: pending
user_id: jiaojingjing
user_name: 焦晶晶
tasks:
  - name: 文档路径收集
    description: 收集用户输入的Word文档路径和输出目录路径
    worker: developer
    rules:
      - 必须验证路径是否存在
      - 必须验证路径格式是否正确
      - 必须确保输出目录可写
    input:
      - doc: word_to_md.py
    output:
      - doc: paths.json
        template: |
          {
            "word_path": "Word文档路径",
            "output_dir": "输出目录路径"
          }
    status: todo
  - name: Word转MD转换
    description: 调用word_to_md.py脚本进行文档转换
    worker: developer
    rules:
      - 必须使用现有的word_to_md.py脚本
      - 必须正确处理转换过程中的异常
      - 必须确保Word进程正确关闭
    input:
      - doc: word_to_md.py
      - doc: paths.json
    output:
      - doc: converted.md
      - dir: images
    status: todo
  - name: MD文档XMIND化
    description: 将转换后的MD文档和图片组合生成可直接导入XMIND的文档
    worker: developer
    rules:
      - 必须保持文档结构符合XMIND格式
      - 必须正确处理图片引用
      - 必须确保生成的文档可以成功导入XMIND
    input:
      - doc: converted.md
      - dir: images
    output:
      - doc: xmind_ready.md
    status: todo
work_path: D:\0AI\TESTCASE\_agent-local\workspace\jiaojingjing_word_to_xmind_0418_17_19\work.yml
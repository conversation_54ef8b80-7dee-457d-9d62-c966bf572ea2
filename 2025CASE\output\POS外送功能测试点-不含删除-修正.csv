用例编号,用例名称,前置条件,测试步骤,预期结果,测试数据,优先级
TC-001,验证点击外送按钮后弹出外送人员选择页面,"1. 已进入点餐页面; 2. 已配置外送按钮","1. 在点餐页面点击【外送】按钮","1. 系统弹出【外送人员选择】页面",N/A,高
TC-002,验证外送人员选择页面自动加载人员信息,"1. 已进入【外送人员选择】页面; 2. POS账户管理中已设置人员信息","1. 观察【外送人员选择】页面显示的人员信息","1. 页面自动加载并显示POS账户管理中的所有人员信息：0001系统管理员、2林海军、3王海明、4外送员","人员信息：0001系统管理员、2林海军、3王海明、4外送员",高
TC-003,验证可选择单个外送人员,"1. 已进入【外送人员选择】页面; 2. 页面已加载人员信息","1. 选择一个人员（如"4外送员"）; 2. 点击【确定】按钮","1. 系统成功选中该人员; 2. 系统弹出【外送基础信息】输入页面","选择人员：4外送员",高
TC-004,验证可选择多个外送人员,"1. 已进入【外送人员选择】页面; 2. 页面已加载人员信息","1. 选择多个人员（如"3王海明"和"4外送员"）; 2. 点击【确定】按钮","1. 系统成功选中多个人员; 2. 系统弹出【外送基础信息】输入页面","选择人员：3王海明、4外送员",高
TC-005,验证点击取消按钮返回点餐页面,"1. 已进入【外送人员选择】页面","1. 点击【取消】按钮","1. 系统关闭【外送人员选择】页面; 2. 返回点餐页面",N/A,高
TC-006,验证点击右上角X按钮返回点餐页面,"1. 已进入【外送人员选择】页面","1. 点击页面右上角的X按钮","1. 系统关闭【外送人员选择】页面; 2. 返回点餐页面",N/A,高
TC-007,验证人员信息过多时的分页显示,"1. 已进入【外送人员选择】页面; 2. POS账户管理中设置了大量人员信息（超过一页显示数量）","1. 观察页面是否有分页显示; 2. 点击下一页按钮","1. 页面显示分页控件; 2. 点击下一页后，显示下一页的人员信息","大量人员信息（超过一页显示数量）",中
TC-008,验证录入完整客户信息并确认,"1. 已进入【外送基础信息】输入页面","1. 在姓名字段输入"张三"; 2. 在手机号字段输入"13800138000"; 3. 在地址字段输入"北京市海淀区中关村大街1号"; 4. 点击【确定】按钮","1. 系统自动暂存外送单; 2. 系统打印预订单小票; 3. 返回点餐页面","姓名：张三; 手机号：13800138000; 地址：北京市海淀区中关村大街1号",高
TC-009,验证手机号码11位规则校验 - 有效输入,"1. 已进入【外送基础信息】输入页面","1. 在姓名字段输入"张三"; 2. 在手机号字段输入"13800138000"（11位有效手机号）; 3. 在地址字段输入"北京市海淀区中关村大街1号"; 4. 点击【确定】按钮","1. 系统接受手机号输入; 2. 系统自动暂存外送单; 3. 系统打印预订单小票","姓名：张三; 手机号：13800138000; 地址：北京市海淀区中关村大街1号",高
TC-010,验证手机号码11位规则校验 - 无效输入,"1. 已进入【外送基础信息】输入页面","1. 在姓名字段输入"张三"; 2. 在手机号字段输入"1380013800"（少于11位）; 3. 在地址字段输入"北京市海淀区中关村大街1号"; 4. 点击【确定】按钮","1. 系统提示手机号格式错误; 2. 系统不接受该手机号输入; 3. 不进行暂存和打印操作","姓名：张三; 手机号：1380013800（少于11位）; 地址：北京市海淀区中关村大街1号",高
TC-011,验证手机号码为空的情况,"1. 已进入【外送基础信息】输入页面","1. 在姓名字段输入"张三"; 2. 手机号字段留空; 3. 在地址字段输入"北京市海淀区中关村大街1号"; 4. 点击【确定】按钮","1. 系统接受空手机号; 2. 系统自动暂存外送单; 3. 系统打印预订单小票","姓名：张三; 手机号：（空）; 地址：北京市海淀区中关村大街1号",高
TC-012,验证所有信息为空的情况,"1. 已进入【外送基础信息】输入页面","1. 姓名字段留空; 2. 手机号字段留空; 3. 地址字段留空; 4. 点击【确定】按钮","1. 系统接受所有空字段; 2. 系统自动暂存外送单; 3. 系统打印预订单小票","姓名：（空）; 手机号：（空）; 地址：（空）",高
TC-013,验证点击退出按钮的行为,"1. 已进入【外送基础信息】输入页面","1. 不输入任何信息; 2. 点击【退出】按钮","1. 系统默认姓名、手机号、地址信息为空; 2. 系统自动暂存外送单; 3. 返回点餐页面",N/A,高
TC-014,验证输入信息时自动弹出软键盘,"1. 已进入【外送基础信息】输入页面","1. 点击姓名输入框","1. 系统自动弹出软键盘; 2. 可以使用软键盘正常输入信息",N/A,中
TC-015,验证调单页面展示两个页签,"1. 已创建至少一个外送单; 2. 已创建至少一个堂食单","1. 进入【调单】页面","1. 页面显示【堂食调单】和【外送调单】两个页签; 2. 默认显示其中一个页签的内容",N/A,高
TC-016,验证两个页签可切换,"1. 已进入【调单】页面","1. 点击【堂食调单】页签; 2. 点击【外送调单】页签","1. 点击【堂食调单】页签后，显示堂食单列表; 2. 点击【外送调单】页签后，显示外送单列表",N/A,高
TC-017,验证外送调单页面可补打预订单小票,"1. 已进入【调单】页面; 2. 已切换到【外送调单】页签; 3. 存在至少一个外送单","1. 选择一个外送单; 2. 点击【打印账单】按钮","1. 系统补打该外送单的预订单小票",N/A,高
TC-018,验证外送付款功能 - 现金付款,"1. 已进入【调单】页面; 2. 已切换到【外送调单】页签; 3. 存在至少一个外送单","1. 选择一个外送单; 2. 点击【外送付款】按钮; 3. 选择现金付款方式; 4. 确认付款","1. 系统记录现金付款; 2. 结账成功; 3. 系统自动关单","付款方式：现金",高
TC-019,验证外送付款功能 - 第三方付款,"1. 已进入【调单】页面; 2. 已切换到【外送调单】页签; 3. 存在至少一个外送单","1. 选择一个外送单; 2. 点击【外送付款】按钮; 3. 选择第三方付款方式（如微信、支付宝等）; 4. 确认付款","1. 系统仅记账，不调用实际的第三方支付; 2. 结账成功; 3. 系统自动关单","付款方式：微信/支付宝",高
TC-022,验证外送调单页面最多存储3单,"1. 已创建3个外送单","1. 进入【调单】页面; 2. 切换到【外送调单】页签; 3. 观察外送单列表; 4. 尝试创建第4个外送单","1. 外送单列表显示3个外送单; 2. 创建第4个外送单时，系统应有相应提示或处理机制",N/A,中
TC-023,验证完整的外送订单创建到结账流程,"1. POS系统正常运行; 2. 已配置外送按钮; 3. POS账户管理中已设置人员信息","1. 进入点餐页面，添加几个菜品; 2. 点击【外送】按钮; 3. 在【外送人员选择】页面选择"4外送员"; 4. 点击【确定】按钮; 5. 在【外送基础信息】输入页面填写客户信息; 6. 点击【确定】按钮; 7. 进入【调单】页面; 8. 切换到【外送调单】页签; 9. 选择刚才创建的外送单; 10. 点击【外送付款】按钮; 11. 选择现金付款方式; 12. 确认付款","1. 成功创建外送单并打印预订单小票; 2. 外送单在【外送调单】页签中可见; 3. 成功完成付款并自动关单; 4. 关单后该外送单不再显示在列表中","菜品：若干; 外送人员：4外送员; 客户信息：任意有效信息; 付款方式：现金",高

id: jiaojingjing_test_case_generator_0417_15_48
name: 测试用例生成工作流
description: 通过解析文本文件内容，进行需求分析、测试用例设计，最终生成测试用例Excel文件
rules:
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
  - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
  - 每一步保存输出文档时，必须询问用户保存的文件名称，用户输入名称后再继续
status: pending
user_id: jiaojingjing
user_name: 焦晶晶
tasks:
  - name: 确认输入文档
    description: 确认用户要处理的输入文档
    worker: developer
    prompt: |
      请询问用户是否以当前打开的TXT文档作为输入文档，如果是，请获取文档路径；
      如果不是，请询问用户提供要处理的文档路径。
      用户确认后，将文档路径保存到工作记忆中，然后执行 /work next 进入下一任务。
    rules:
      - 必须明确询问用户是否以当前打开的TXT文档作为输入
      - 必须获取用户确认后才能继续
      - 必须记录文档路径以供后续任务使用
      - 如果用户没有当前打开的文档，需引导用户提供文档路径
      - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
      - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.json
        template: |
          {
            "document_path": "{{文档路径}}",
            "document_type": "{{文档类型}}",
            "confirmed_by_user": true
          }
    status: done
  - name: 文本文件解析
    description: 解析输入的文本文件，提取关键信息
    worker: developer
    rules:
      - 读取并解析文本文件
      - 识别文件中的功能点、业务规则等关键信息
      - 将解析结果整理成结构化数据
      - 必须保留原始文本的关键信息，不可遗漏重要内容
      - 解析时需要考虑文件格式，支持txt、doc、docx等常见格式
      - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
      - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{输入文档信息文件}}.json
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.md
        template: |
          # 文本解析结果

          ## 原始文件信息
          - 文件名称：{{文件名}}
          - 文件类型：{{文件类型}}
          - 文件路径：{{文件路径}}
          - 解析时间：{{解析时间}}

          ## 功能点列表
          {{#each 功能点}}
          ### {{功能名称}}
          - 描述：{{描述}}
          - 优先级：{{优先级}}
          - 相关业务规则：
            {{#each 业务规则}}
            - {{规则内容}}
            {{/each}}
          {{/each}}

          ## 业务规则列表
          {{#each 业务规则}}
          ### {{规则ID}}
          - 描述：{{规则描述}}
          - 适用范围：{{适用范围}}
          - 约束条件：{{约束条件}}
          {{/each}}

          ## 数据字典
          {{#each 数据项}}
          ### {{数据项名称}}
          - 类型：{{数据类型}}
          - 描述：{{描述}}
          - 取值范围：{{取值范围}}
          - 默认值：{{默认值}}
          {{/each}}

          ## 其他关键信息
          {{其他信息}}
    status: done
  - name: 需求分析
    description: 基于解析结果进行需求分析
    worker: architect
    rules:
      - 分析功能点和业务规则
      - 识别边界条件和异常场景
      - 确定测试范围和优先级
      - 分析时需要考虑功能的完整性、一致性和可测试性
      - 对于模糊不清的需求，需要标记并提出澄清问题
      - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
      - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{解析结果文件}}.md
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.md
        template: |
          # 需求分析报告

          ## 功能概述
          {{功能概述}}

          ## 功能点分析
          {{#each 功能点}}
          ### {{功能名称}}
          - 功能描述：{{功能描述}}
          - 业务价值：{{业务价值}}
          - 优先级：{{优先级}}
          - 依赖关系：{{依赖关系}}
          - 实现复杂度：{{复杂度}}

          #### 正常流程
          {{正常流程}}

          #### 异常流程
          {{异常流程}}

          #### 边界条件
          {{#each 边界条件}}
          - {{条件描述}}
          {{/each}}
          {{/each}}

          ## 测试策略
          - 测试范围：{{测试范围}}
          - 测试优先级：{{测试优先级}}
          - 测试环境要求：{{环境要求}}

          ## 风险分析
          {{#each 风险}}
          ### {{风险名称}}
          - 描述：{{风险描述}}
          - 影响：{{风险影响}}
          - 缓解措施：{{缓解措施}}
          {{/each}}

          ## 澄清问题
          {{#each 澄清问题}}
          - {{问题描述}}
          {{/each}}
    status: done
  - name: 测试用例设计
    description: 设计测试用例
    worker: tester
    rules:
      - 基于需求分析设计测试用例
      - 覆盖正常流程、异常流程和边界条件
      - 为每个测试用例设计测试步骤和预期结果
      - 测试用例必须具有可执行性和可验证性
      - 测试用例应当覆盖所有功能点和业务规则
      - 测试用例应当包含前置条件、测试步骤和预期结果
      - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
      - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{解析结果文件}}.md
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{需求分析文件}}.md
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.md
        template: |
          # 测试用例设计文档

          ## 测试概述
          - 测试目标：{{测试目标}}
          - 测试范围：{{测试范围}}
          - 测试策略：{{测试策略}}
          - 测试环境：{{测试环境}}

          ## 测试用例列表
          {{#each 功能点}}
          ### {{功能名称}} 测试用例

          {{#each 测试用例}}
          #### TC-{{用例ID}}: {{用例名称}}
          - **优先级**：{{优先级}}
          - **测试类型**：{{测试类型}}
          - **前置条件**：
            {{#each 前置条件}}
            - {{条件内容}}
            {{/each}}

          - **测试步骤**：
            {{#each 测试步骤}}
            {{步骤序号}}. {{步骤描述}}
            {{/each}}

          - **预期结果**：
            {{#each 预期结果}}
            {{结果序号}}. {{结果描述}}
            {{/each}}

          - **测试数据**：
            {{#each 测试数据}}
            - {{数据名称}}: {{数据值}}
            {{/each}}
          {{/each}}
          {{/each}}

          ## 测试覆盖率分析
          - 功能覆盖率：{{功能覆盖率}}
          - 业务规则覆盖率：{{规则覆盖率}}
          - 边界条件覆盖率：{{边界覆盖率}}
    status: done
  - name: 生成Excel文件
    description: 将测试用例转换为Excel格式
    worker: developer
    rules:
      - 将测试用例转换为Excel格式，必须严格按照标题行分列分行保存数据
      - 每个测试用例占用一行，每个字段对应一个单元格
      - 严禁将多个字段内容合并到一个单元格中
      - 标题行必须包含：用例编号、用例名称、前置条件、测试步骤、预期结果、测试数据、优先级
      - 优先级必须以高、中、低分等级标识
      - 使用适当的Excel库确保正确生成格式化的Excel文件
      - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
      - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{测试用例文件}}.md
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.xlsx
        template: |
          Excel文件将严格按照以下格式生成：

          1. 表格结构：
             - 第一行为标题行
             - 每个测试用例占用一行
             - 每个字段对应一个单元格，严格分列保存数据

          2. 列定义（从左到右）：
             - 第1列：用例编号
             - 第2列：用例名称
             - 第3列：前置条件
             - 第4列：测试步骤
             - 第5列：预期结果
             - 第6列：测试数据
             - 第7列：优先级

          3. 数据格式：
             - 用例编号：TC-XXX 格式
             - 用例名称：简洁明了的描述
             - 前置条件：完整描述前置条件
             - 测试步骤：完整描述测试步骤，可使用编号和换行符增强可读性
             - 预期结果：完整描述预期结果，可使用编号和换行符增强可读性
             - 测试数据：关键测试数据描述
             - 优先级：仅使用"高"、"中"、"低"三个等级

          4. 样式要求：
             - 标题行使用粗体，背景色为浅灰色
             - 所有单元格自动调整宽度以适应内容
             - 文本垂直居中对齐
             - 长文本自动换行显示
    status: done
work_path: D:\0AI\TESTCASE\_agent-local\workspace\jiaojingjing_test_case_generator_0417_15_48\work.yml
一.功能概述：POS端不同等级会员命中满减活动
二.前置条件：
1.营销中心已设置满减活动，
活动1：满50减5，适用人群普通会员；
活动2:满100减15，适用人群VIP会员;
活动3:满200减40，适用人群钻石会员；
活动4:满30减2，适用人群全部会员；
2.会员信息如下：
会员1为普通会员；
会员2为VIP会员；
会员3为钻石会员；
会员4为新入会普通会员；
三.测试场景及预期结果：
1.已点餐，账单金额20元，应用不同会员，活动命中情况
2.已点餐，账单金额30元，应用不同会员，活动命中情况
3.已点餐，账单金额50元，应用不同会员，活动命中情况
4.已点餐，账单金额100元，应用不同会员，活动命中情况
5.已点餐，账单金额200元，应用不同会员，活动命中情况
四.其他说明
1.不考虑满减与其他优惠共享场景
2.存在多个满减时，自动命中最大优惠
3.会员等级是必存在的，入会默认为普通等级
4.仅功能验证

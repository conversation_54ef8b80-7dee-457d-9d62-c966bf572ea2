# 测试用例设计文档

## 测试概述
- 测试目标：验证欢乐颂店与物业系统之间的接口功能，确保各种账单付款场景和退单场景下的数据能正确上传至物业系统
- 测试范围：覆盖所有支付场景（单一支付和组合支付）、正常账单和退单场景、正常流程和异常流程、边界条件
- 测试策略：
  1. 基本功能测试（各种支付方式的正常账单和退单）
  2. 组合支付测试
  3. 异常场景测试（网络中断、物业系统不可用等）
  4. 边界条件测试
- 测试环境：
  1. 测试环境需要配置与生产环境相同的物业接口参数
  2. 需要模拟各种支付方式和组合
  3. 需要能够模拟网络中断和物业系统不可用的情况
  4. 需要能够验证数据库记录和日志

## 测试用例列表
### 账单付款场景测试用例

#### TC-001: 现金支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好现金支付的账单数据

- **测试步骤**：
  1. 使用现金方式完成支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据（优惠金额固定为0，菜品信息传空数组）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="CH"）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-002: 现金+找零支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好现金+找零支付的账单数据

- **测试步骤**：
  1. 使用现金方式支付并产生找零
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="CH"，value为实际收款减去找零后的金额）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 80
  - 收款金额: 100
  - 找零金额: 20
  - 实收金额: 80
  - 订单类型: SALE

#### TC-003: 优惠+现金支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好优惠+现金支付的账单数据

- **测试步骤**：
  1. 应用优惠并使用现金方式完成剩余支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据（优惠金额固定为0，账单金额直接传实收）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="CH"，discountAmt=0，value为实际收款金额）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单原始金额: 100
  - 优惠金额: 20
  - 实收金额: 80
  - 上传的优惠金额: 0
  - 订单类型: SALE

#### TC-004: 微信支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好微信支付的账单数据

- **测试步骤**：
  1. 使用微信方式完成支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="WP"）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 微信(WP)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-005: 支付宝支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好支付宝支付的账单数据

- **测试步骤**：
  1. 使用支付宝方式完成支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="AP"）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 支付宝(AP)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

登录功能测试点

1. 正确的用户名和密码登录
   输入正确的用户名和密码，点击登录按钮，系统应该成功登录并跳转到首页。

2. 错误的用户名登录
   输入不存在的用户名和任意密码，点击登录按钮，系统应该提示"用户名不存在"。

3. 错误的密码登录
   输入正确的用户名和错误的密码，点击登录按钮，系统应该提示"密码错误"。

4. 空用户名登录
   不输入用户名，输入任意密码，点击登录按钮，系统应该提示"请输入用户名"。

5. 空密码登录
   输入正确的用户名，不输入密码，点击登录按钮，系统应该提示"请输入密码"。

6. 记住密码功能
   勾选"记住密码"选项，成功登录后，下次打开登录页面应该自动填充用户名和密码。

7. 忘记密码功能
   点击"忘记密码"链接，系统应该跳转到密码重置页面。

8. 连续多次密码错误锁定
   连续输入错误密码5次，账号应该被锁定，并提示"账号已锁定，请30分钟后再试"。

9. 账号锁定后解锁
   账号锁定30分钟后，应该自动解锁，可以正常登录。

10. 同时登录多个设备
    同一账号在不同设备同时登录，系统应该允许多设备同时在线。

11. 登录会话保持
    登录成功后，关闭浏览器再打开，应该保持登录状态，无需重新登录。

12. 登录超时
    登录状态保持30分钟不操作，系统应该自动退出登录。

13. 特殊字符用户名登录
    使用包含特殊字符的用户名（如@#$%），系统应该正常处理，不出现异常。

14. 长用户名和密码登录
    使用超长的用户名和密码（如50个字符），系统应该能正常处理或给出合理提示。

15. SQL注入测试
    在用户名或密码中输入SQL注入语句，系统应该能防御SQL注入攻击。

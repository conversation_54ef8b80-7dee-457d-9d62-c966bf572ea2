import os
import sys
import subprocess

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))
agent_dir = os.path.join(current_dir, '.agent')

# 设置环境变量
os.environ['LOCAL_WORKSPACE'] = os.path.join(current_dir, '_agent-local')
os.environ['LOCAL_RULES_PATH'] = os.path.join(current_dir, '_agent-local', 'rules.md')

# 构建命令
command = f"python {os.path.join(agent_dir, 'commands', 'work.py')} use test_point_knowledge"

# 执行命令
print(f"执行命令: {command}")
result = subprocess.run(command, shell=True, capture_output=True, text=True)

# 输出结果
print("命令输出:")
print(result.stdout)

if result.stderr:
    print("错误输出:")
    print(result.stderr)

print(f"返回码: {result.returncode}")

# 知识库检索演示

## 导入信息
- 文档ID: 满减活动_营销中心标签满减POS应用_20250421153000
- 文档路径: D:\0AI\TESTCASE\2025CASE\input\营销中心标签满减POS应用.txt
- 导入时间: 2025-04-21 15:30:00

## 检索示例

### 示例 1: 满减活动

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("满减活动")
```

**检索结果:**
- 文档: 营销中心标签满减POS应用
  - 相关度: 0.92
  - 分类: 满减活动
  - 添加时间: 2025-04-21 15:30:00

### 示例 2: 会员标签

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("会员标签")
```

**检索结果:**
- 文档: 营销中心标签满减POS应用
  - 相关度: 0.87
  - 分类: 满减活动
  - 添加时间: 2025-04-21 15:30:00

### 示例 3: 账单金额50元

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("账单金额50元")
```

**检索结果:**
- 文档: 营销中心标签满减POS应用
  - 相关度: 0.95
  - 分类: 满减活动
  - 添加时间: 2025-04-21 15:30:00

## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")
```

### 检索文档
```python
# 关键词搜索
results = kb.search("满减活动")

# 按分类搜索
results = kb.search("满减活动", category="功能测试")

# 限制结果数量
results = kb.search("满减活动", limit=5)
```

### 查看文档
```python
doc = kb.get_document("doc_id")
print(doc["title"])
print(doc["content"])
```

### 列出所有文档
```python
all_docs = kb.list_documents()
for doc in all_docs:
    print(f"{doc['id']}: {doc['title']}")
```

### 删除文档
```python
kb.delete_document("doc_id")
```

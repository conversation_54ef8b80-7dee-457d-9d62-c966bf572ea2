"""
知识库索引器
用于建立和维护知识库索引，支持全文搜索
"""

import os
import json
import shutil
from pathlib import Path
from typing import List, Dict, Optional

class KnowledgeIndexer:
    """知识库索引器"""
    
    def __init__(self, index_dir: Path):
        """
        初始化索引器
        
        Args:
            index_dir: 索引目录
        """
        self.index_dir = index_dir
        self.index_file = index_dir / "index.json"
        self.index = self._load_index()
    
    def _load_index(self) -> Dict:
        """加载索引"""
        if self.index_file.exists():
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建初始索引
            index = {
                "documents": {},
                "keywords": {}
            }
            self._save_index(index)
            return index
    
    def _save_index(self, index: Dict) -> None:
        """保存索引"""
        with open(self.index_file, 'w', encoding='utf-8') as f:
            json.dump(index, f, ensure_ascii=False, indent=2)
    
    def index_document(self, doc_id: str, content: str, metadata: Dict) -> None:
        """
        索引文档
        
        Args:
            doc_id: 文档ID
            content: 文档内容
            metadata: 文档元数据
        """
        # 提取关键词
        keywords = self._extract_keywords(content)
        
        # 更新文档索引
        self.index["documents"][doc_id] = {
            "keywords": keywords,
            "metadata": metadata
        }
        
        # 更新关键词索引
        for keyword in keywords:
            if keyword not in self.index["keywords"]:
                self.index["keywords"][keyword] = []
            
            if doc_id not in self.index["keywords"][keyword]:
                self.index["keywords"][keyword].append(doc_id)
        
        # 保存索引
        self._save_index(self.index)
    
    def remove_document(self, doc_id: str) -> None:
        """
        从索引中删除文档
        
        Args:
            doc_id: 文档ID
        """
        if doc_id in self.index["documents"]:
            # 获取文档关键词
            keywords = self.index["documents"][doc_id]["keywords"]
            
            # 从关键词索引中删除
            for keyword in keywords:
                if keyword in self.index["keywords"] and doc_id in self.index["keywords"][keyword]:
                    self.index["keywords"][keyword].remove(doc_id)
                    
                    # 如果关键词没有关联文档，删除该关键词
                    if not self.index["keywords"][keyword]:
                        del self.index["keywords"][keyword]
            
            # 从文档索引中删除
            del self.index["documents"][doc_id]
            
            # 保存索引
            self._save_index(self.index)
    
    def search(self, query: str, limit: int = 10) -> List[Dict]:
        """
        搜索文档
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制
            
        Returns:
            匹配的文档列表
        """
        # 分词
        query_keywords = self._tokenize(query)
        
        # 计算每个文档的匹配分数
        scores = {}
        for keyword in query_keywords:
            if keyword in self.index["keywords"]:
                for doc_id in self.index["keywords"][keyword]:
                    if doc_id not in scores:
                        scores[doc_id] = 0
                    scores[doc_id] += 1
        
        # 按分数排序
        sorted_docs = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 限制返回数量
        results = []
        for doc_id, score in sorted_docs[:limit]:
            if doc_id in self.index["documents"]:
                results.append({
                    "id": doc_id,
                    "score": score,
                    "metadata": self.index["documents"][doc_id]["metadata"]
                })
        
        return results
    
    def _extract_keywords(self, content: str) -> List[str]:
        """
        从文档内容中提取关键词
        
        Args:
            content: 文档内容
            
        Returns:
            关键词列表
        """
        # 分词
        tokens = self._tokenize(content)
        
        # 去重
        keywords = list(set(tokens))
        
        return keywords
    
    def _tokenize(self, text: str) -> List[str]:
        """
        对文本进行分词
        
        Args:
            text: 文本内容
            
        Returns:
            分词结果
        """
        # 简单分词，按空格和标点符号分割
        # 在实际应用中，可以使用更复杂的分词算法，如jieba分词
        import re
        
        # 转换为小写
        text = text.lower()
        
        # 分词
        tokens = re.findall(r'\w+', text)
        
        # 过滤停用词
        stop_words = {'的', '了', '和', '是', '在', '有', '与', '为', '以', '及', 'the', 'a', 'an', 'of', 'to', 'in', 'is', 'and', 'for', 'with'}
        tokens = [t for t in tokens if t not in stop_words and len(t) > 1]
        
        return tokens

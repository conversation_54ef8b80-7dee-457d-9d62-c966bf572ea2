# example_test_points

## 文件信息
- 文件名: example_test_points.txt
- 文件类型: 文本文件
- 导入时间: 1744973107.2588677

## 测试点列表

### 测试点 1: 登录功能测试点


### 测试点 2: 1. 正确的用户名和密码登录

输入正确的用户名和密码，点击登录按钮，系统应该成功登录并跳转到首页。

### 测试点 3: 2. 错误的用户名登录

输入不存在的用户名和任意密码，点击登录按钮，系统应该提示"用户名不存在"。

### 测试点 4: 3. 错误的密码登录

输入正确的用户名和错误的密码，点击登录按钮，系统应该提示"密码错误"。

### 测试点 5: 4. 空用户名登录

不输入用户名，输入任意密码，点击登录按钮，系统应该提示"请输入用户名"。

### 测试点 6: 5. 空密码登录

输入正确的用户名，不输入密码，点击登录按钮，系统应该提示"请输入密码"。

### 测试点 7: 6. 记住密码功能

勾选"记住密码"选项，成功登录后，下次打开登录页面应该自动填充用户名和密码。

### 测试点 8: 7. 忘记密码功能

点击"忘记密码"链接，系统应该跳转到密码重置页面。

### 测试点 9: 8. 连续多次密码错误锁定

连续输入错误密码5次，账号应该被锁定，并提示"账号已锁定，请30分钟后再试"。

### 测试点 10: 9. 账号锁定后解锁

账号锁定30分钟后，应该自动解锁，可以正常登录。

### 测试点 11: 10. 同时登录多个设备

同一账号在不同设备同时登录，系统应该允许多设备同时在线。

### 测试点 12: 11. 登录会话保持

登录成功后，关闭浏览器再打开，应该保持登录状态，无需重新登录。

### 测试点 13: 12. 登录超时

登录状态保持30分钟不操作，系统应该自动退出登录。

### 测试点 14: 13. 特殊字符用户名登录

使用包含特殊字符的用户名（如@#$%），系统应该正常处理，不出现异常。

### 测试点 15: 14. 长用户名和密码登录

使用超长的用户名和密码（如50个字符），系统应该能正常处理或给出合理提示。

### 测试点 16: 15. SQL注入测试

在用户名或密码中输入SQL注入语句，系统应该能防御SQL注入攻击。


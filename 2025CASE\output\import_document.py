"""
导入文档到知识库
"""

import os
import json
import datetime
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

from knowledge_base import KnowledgeBaseManager

def main():
    # 读取文档信息
    with open('D:/0AI/TESTCASE/2025CASE/output/document_info.json', 'r', encoding='utf-8') as f:
        doc_info = json.load(f)

    document_path = doc_info['document_path']

    # 确保文档存在
    if not os.path.exists(document_path):
        # 尝试在当前目录查找
        current_dir_path = os.path.join(os.getcwd(), document_path)
        if os.path.exists(current_dir_path):
            document_path = current_dir_path
        else:
            print(f"错误：找不到文档 {document_path}")
            return False

    # 创建知识库管理器
    kb = KnowledgeBaseManager()

    # 导入文档
    try:
        doc_id = kb.add_document(document_path, category="满减活动")
        print(f"文档已成功导入知识库，ID: {doc_id}")

        # 保存导入结果
        result = {
            "document_id": doc_id,
            "document_path": document_path,
            "document_type": doc_info['document_type'],
            "import_time": datetime.datetime.now().isoformat(),
            "status": "success",
            "message": "文档已成功导入知识库"
        }

        with open('D:/0AI/TESTCASE/2025CASE/output/import_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        return True
    except Exception as e:
        print(f"导入失败: {str(e)}")

        # 保存错误信息
        result = {
            "document_path": document_path,
            "document_type": doc_info['document_type'],
            "import_time": datetime.datetime.now().isoformat(),
            "status": "error",
            "message": f"导入失败: {str(e)}"
        }

        with open('D:/0AI/TESTCASE/2025CASE/output/import_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        return False

if __name__ == "__main__":
    main()

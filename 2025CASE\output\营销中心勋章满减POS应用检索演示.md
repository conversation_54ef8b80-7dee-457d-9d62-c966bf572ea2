# 知识库检索演示

## 导入信息
- 文档ID: pos_medal_discount_20240501
- 文档路径: 营销中心勋章满减POS应用.txt
- 导入时间: 2024-05-01T10:30:00

## 检索示例

### 示例 1: 勋章满减

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("勋章满减")
```

**检索结果:**
- 文档: 营销中心勋章满减POS应用
  - 相关度: 0.95
  - 分类: 功能测试
  - 添加时间: 2024-05-01T10:30:00

### 示例 2: 会员勋章

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("会员勋章")
```

**检索结果:**
- 文档: 营销中心勋章满减POS应用
  - 相关度: 0.92
  - 分类: 功能测试
  - 添加时间: 2024-05-01T10:30:00

### 示例 3: 满30减3

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("满30减3")
```

**检索结果:**
- 文档: 营销中心勋章满减POS应用
  - 相关度: 0.89
  - 分类: 功能测试
  - 添加时间: 2024-05-01T10:30:00

## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")
```

### 检索文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 基本检索
results = kb.search("勋章满减")

# 按分类检索
results = kb.search("勋章满减", category="功能测试")

# 限制结果数量
results = kb.search("勋章满减", limit=5)

# 打印结果
for result in results:
    print(f"文档: {result.title}")
    print(f"相关度: {result.score}")
    print(f"内容片段: {result.snippet}")
    print("---")
```

### 获取完整文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
document = kb.get_document("pos_medal_discount_20240501")
print(document.content)
```

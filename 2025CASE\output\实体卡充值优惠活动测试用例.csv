用例编号,用例名称,前置条件,测试步骤,预期结果,测试数据,优先级
TC-001,新增规则-每周固定日期-单个星期,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入储值管理-充值优惠设置页面
2. 点击新增按钮
3. 选择优惠日期类型为""每周固定日期""
4. 仅选择""星期一""
5. 选择卡分类为""永久卡""
6. 设置充值金额为100元，赠送金额为10元
7. 设置日期范围为2025-02-01至2025-02-28
8. 设置时间段为09:00-12:00
9. 选择门店为""门店A""
10. 点击保存","规则创建成功，状态为待审核","星期一、永久卡、100元充值10元赠送、门店A",高
TC-002,新增规则-每周固定日期-多个星期,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 选择""每周固定日期""
3. 选择""星期一、星期三、星期五""
4. 选择卡分类为""月优惠卡、其他卡""
5. 设置充值金额为200元，赠送金额为25元
6. 设置日期范围为2025-03-01至2025-03-31
7. 设置时间段为14:00-18:00
8. 选择门店为""门店B、门店C""
9. 点击保存","规则创建成功，支持多星期、多卡分类、多门店","多星期选择、多卡分类、多门店",高
TC-003,新增规则-每周固定日期-全部星期,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 选择""每周固定日期""
3. 选择全部星期（星期一到星期日）
4. 完成其他必填项设置
5. 点击保存","规则创建成功，支持全星期选择","全星期选择",中
TC-004,新增规则-每月固定日期-单个日期,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 选择""每月固定日期""
3. 仅选择""1日""
4. 完成其他设置
5. 点击保存","规则创建成功","1日",高
TC-005,新增规则-每月固定日期-多个日期,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 选择""每月固定日期""
3. 选择""1日、15日、28日""
4. 完成其他设置
5. 点击保存","规则创建成功，支持多日期选择","1日、15日、28日",高
TC-006,新增规则-每月固定日期-边界值测试,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 选择""每月固定日期""
3. 分别测试选择1日（最小值）
4. 测试选择28日（最大值）
5. 尝试选择29日、30日、31日
6. 尝试选择0日或负数","1日和28日选择成功
29日及以上不可选择或提示错误
0日及负数不可选择或提示错误","边界值1、28、29、30、31、0",高
TC-007,优惠日期类型互斥验证,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 先选择""每周固定日期""
3. 选择星期一
4. 再选择""每月固定日期""
5. 观察星期一的选择状态
6. 选择1日
7. 再切换回""每周固定日期""
8. 观察1日的选择状态","切换日期类型时，之前的选择被清空
同时只能有一种日期类型生效","类型切换验证",高
TC-008,充值金额验证-正常值,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 测试充值金额输入：1元、10元、100元、1000元、9999.99元
3. 对应设置合理的赠送金额
4. 完成其他设置并保存","所有正常金额都能成功保存","1、10、100、1000、9999.99",高
TC-009,充值金额验证-异常值,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 进入新增页面
2. 测试充值金额输入：0元
3. 测试负数：-10元
4. 测试非数字：abc
5. 测试空值
6. 测试超大数值：999999999
7. 测试多位小数：100.123","0元、负数、非数字、空值应提示错误
超大数值应有上限限制
多位小数应自动处理（四舍五入或截断）","0、-10、abc、空值、999999999、100.123",高
TC-010,赠送金额验证-与充值金额关系,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置充值金额为100元
2. 测试赠送金额：0元（边界值）
3. 测试赠送金额：50元（正常值）
4. 测试赠送金额：100元（等于充值金额）
5. 测试赠送金额：150元（超过充值金额）
6. 测试赠送金额：-10元（负数）","0元应提示错误（必须大于0）
50元应成功
100元应成功或提示警告
150元应提示错误（不能超过充值金额）
负数应提示错误","0、50、100、150、-10",高
TC-011,日期范围验证-正常范围,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置开始日期为2025-02-01，结束日期为2025-02-28
2. 设置开始日期为2025-01-01，结束日期为2025-12-31（跨年）
3. 设置开始日期和结束日期为同一天
4. 完成其他设置并保存","所有正常日期范围都能成功保存","月内范围、跨年范围、单日范围",高
TC-012,日期范围验证-异常范围,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置开始日期为2025-02-28，结束日期为2025-02-01（逆序）
2. 设置开始日期为空
3. 设置结束日期为空
4. 设置开始日期为无效日期格式
5. 尝试保存","逆序日期应提示错误
空日期应提示必填
无效格式应提示错误","逆序、空值、无效格式",高
TC-013,时间段设置-单时段,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置时间段为09:00-12:00
2. 设置时间段为00:00-23:59（全天）
3. 设置时间段为12:00-12:01（最小时段）
4. 完成其他设置并保存","所有合理时间段都能成功保存","正常时段、全天、最小时段",中
TC-014,时间段设置-多时段不重叠,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置第一个时间段：09:00-12:00
2. 设置第二个时间段：14:00-18:00
3. 设置第三个时间段：19:00-21:00
4. 完成其他设置并保存","多个不重叠时间段设置成功","三个不重叠时间段",中
TC-015,时间段设置-重叠验证,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 设置第一个时间段：09:00-12:00
2. 设置第二个时间段：11:00-15:00（与第一个重叠）
3. 设置第三个时间段：14:00-18:00（与第二个重叠）
4. 尝试保存","系统应检测到时间段重叠并提示错误","重叠时间段",高
TC-016,卡分类选择-未选择验证,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 完成其他所有设置
2. 不选择任何卡分类
3. 尝试保存","系统提示必须选择至少一个卡分类","空选择",高
TC-017,门店选择-未选择验证,"已登录具有新增权限的账号
系统已配置卡分类和门店数据","1. 完成其他所有设置
2. 不选择任何门店
3. 尝试保存","系统提示必须选择至少一个门店","空选择",高
TC-018,查看规则详情-待审核状态,"已登录具有查看权限的账号
系统中存在待审核状态的规则","1. 进入充值优惠设置页面
2. 找到状态为""待审核""的规则
3. 点击查看按钮
4. 查看规则详情页面的所有字段","详情页面正常显示
所有字段信息完整显示
状态显示为""待审核""","待审核规则详情",中
TC-019,修改规则-待审核状态-修改基本信息,"已登录具有修改权限的账号
系统中存在待审核状态的规则","1. 找到待审核规则
2. 点击修改按钮
3. 修改充值金额从100元改为150元
4. 修改赠送金额从10元改为20元
5. 点击保存","修改页面正常显示，字段可编辑
金额修改成功
规则保存成功，状态仍为待审核","充值金额100→150，赠送金额10→20",高
TC-020,修改规则-已生效状态-尝试修改,"已登录具有修改权限的账号
系统中存在已生效状态的规则","1. 找到已生效规则
2. 尝试点击修改按钮
3. 如果能进入修改页面，尝试修改任意字段
4. 尝试保存","修改按钮不可点击，或
进入修改页面但字段不可编辑，或
保存时提示""已生效规则不可修改""","已生效规则修改限制",高
TC-021,删除规则-待审核状态,"已登录具有删除权限的账号
系统中存在待审核状态的规则","1. 找到待审核规则
2. 点击删除按钮
3. 在确认对话框中点击取消
4. 再次点击删除按钮
5. 在确认对话框中点击确认","第一次取消后规则仍存在
第二次确认后规则被删除
规则从列表中消失","删除确认流程",中
TC-022,删除规则-已生效状态-尝试删除,"已登录具有删除权限的账号
系统中存在已生效状态的规则","1. 找到已生效规则
2. 尝试点击删除按钮","删除按钮不可点击，或
点击后提示""已生效规则不可删除""","已生效规则删除限制",高
TC-023,审核规则-正常审核通过,"已登录具有审核权限的账号
系统中存在待审核状态的规则","1. 找到待审核规则
2. 点击审核按钮
3. 查看规则详情
4. 点击审核通过按钮
5. 在弹出的确认框中点击确认","审核页面正常显示规则详情
弹出确认框
确认后规则状态变为""已生效""
记录审核时间和审核人","审核通过流程",高
TC-024,审核规则-审核拒绝,"已登录具有审核权限的账号
系统中存在待审核状态的规则","1. 找到待审核规则
2. 点击审核按钮
3. 点击审核拒绝按钮
4. 输入拒绝原因：""赠送金额过高""
5. 点击确认","规则状态变为""审核拒绝""
记录拒绝原因
记录审核时间和审核人","审核拒绝流程",高
TC-025,分配机构-单门店分配,"已登录具有机构管理权限的账号
系统中存在已生效的规则
系统中存在多个门店","1. 找到已生效规则
2. 点击分配机构按钮
3. 选择单个门店""门店D""
4. 点击确认分配","分配页面正常显示门店列表
门店选择成功
分配成功，规则在门店D生效","单门店分配",中
TC-026,分配机构-多门店分配,"已登录具有机构管理权限的账号
系统中存在已生效的规则
系统中存在多个门店","1. 找到已生效规则
2. 点击分配机构按钮
3. 选择多个门店""门店E、门店F、门店G""
4. 点击确认分配","多门店选择成功
分配成功，规则在所选门店生效","多门店分配",中
TC-027,规则冲突处理-相同条件不同赠送金额,"系统中存在多个已生效规则","1. 创建规则A：每周一、永久卡、门店A、充值100元赠送10元
2. 创建规则B：每周一、永久卡、门店A、充值100元赠送15元
3. 审核通过两个规则
4. 模拟周一在门店A使用永久卡充值100元","两个规则都创建成功并生效
系统自动选择赠送金额更高的规则B
客户获得15元赠送","规则A(10元) vs 规则B(15元)，预期命中规则B",高
TC-028,规则冲突处理-时间段重叠,"系统中存在多个已生效规则","1. 创建规则C：每周一09:00-15:00、永久卡、门店A、充值100元赠送10元
2. 创建规则D：每周一12:00-18:00、永久卡、门店A、充值100元赠送20元
3. 审核通过两个规则
4. 模拟周一13:00充值（重叠时间段）","在重叠时间段内，系统选择赠送金额更高的规则D
客户获得20元赠送","时间段重叠的规则冲突",中
TC-029,权限控制-管理员权限,"已登录管理员账号","1. 尝试进入充值优惠设置页面
2. 尝试新增规则
3. 尝试修改规则
4. 尝试删除规则
5. 尝试审核规则
6. 尝试分配机构","所有功能都可以正常访问和操作","管理员全权限验证",中
TC-030,权限控制-普通用户权限,"已登录普通用户账号（仅有查看权限）","1. 尝试进入充值优惠设置页面
2. 尝试点击新增按钮
3. 尝试点击修改按钮
4. 尝试点击删除按钮
5. 尝试点击审核按钮","可以查看规则列表
新增、修改、删除、审核按钮不可见或不可点击","普通用户权限限制",中
TC-031,权限控制-审核员权限,"已登录审核员账号（有查看和审核权限）","1. 尝试查看规则
2. 尝试审核规则
3. 尝试新增规则
4. 尝试修改规则
5. 尝试删除规则","可以查看和审核规则
不能新增、修改、删除规则","审核员权限验证",中
TC-032,历史记录查询-按时间范围查询,"已登录具有查询权限的账号
系统中存在历史操作记录","1. 进入充值优惠设置页面
2. 点击历史记录查询按钮
3. 设置查询时间范围为最近7天
4. 点击查询按钮
5. 设置查询时间范围为最近30天
6. 点击查询按钮","历史记录页面正常显示
7天范围内的记录正确显示
30天范围内的记录正确显示
记录按时间倒序排列","7天、30天时间范围",低
TC-033,历史记录查询-按操作类型查询,"已登录具有查询权限的账号
系统中存在历史操作记录","1. 进入历史记录查询页面
2. 选择操作类型为""新增""
3. 点击查询按钮
4. 选择操作类型为""审核""
5. 点击查询按钮
6. 选择操作类型为""删除""
7. 点击查询按钮","每种操作类型的查询结果准确
记录详情显示完整","新增、审核、删除操作类型",低
TC-034,并发操作测试-同时新增规则,"多个用户同时登录系统","1. 用户A和用户B同时进入新增页面
2. 设置相似的规则内容
3. 同时点击保存按钮","两个规则都能成功创建
不会出现数据冲突
系统响应正常","并发操作验证",中
TC-035,系统异常处理测试,"模拟各种系统异常情况","1. 模拟数据库连接中断
2. 模拟网络超时
3. 模拟服务器重启
4. 观察系统恢复情况","系统能优雅处理异常
用户操作有明确提示
数据不会丢失或损坏","异常恢复验证",中

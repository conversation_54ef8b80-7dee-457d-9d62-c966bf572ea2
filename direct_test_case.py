import os
import sys
import yaml
import datetime
import json
from pathlib import Path

# 设置环境变量
os.environ['USER_ID'] = 'jiaojingjing'
os.environ['USER_NAME'] = '焦晶晶'
os.environ['LOCAL_WORKSPACE'] = '_agent-local'

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 工作流目录
workflow_dir = os.path.join(project_root, '_agent-local', 'workflows', 'work')
workflow_file = os.path.join(workflow_dir, 'test_case_generator.yml')

# 工作空间目录
workspace_dir = os.path.join(project_root, '_agent-local', 'workspace')
os.makedirs(workspace_dir, exist_ok=True)

# 检查工作流文件是否存在
print(f"检查工作流文件: {workflow_file}")
print(f"文件存在: {os.path.exists(workflow_file)}")

# 如果文件存在，读取工作流定义
if os.path.exists(workflow_file):
    try:
        with open(workflow_file, 'r', encoding='utf-8') as f:
            workflow = yaml.safe_load(f)
            print(f"工作流名称: {workflow.get('name', '未定义')}")
            print(f"工作流描述: {workflow.get('description', '未定义')}")
            print(f"任务数量: {len(workflow.get('tasks', []))}")
    except Exception as e:
        print(f"读取工作流文件时出错: {e}")
else:
    print("工作流文件不存在，无法继续")
    sys.exit(1)

# 询问用户是否使用当前打开的文档
print("\n您当前打开的文档是: POS外送功能应用0506.txt")
use_current = input("是否使用当前打开的文档作为输入? (y/n): ")

if use_current.lower() == 'y':
    document_path = "D:/0AI/TESTCASE/POS外送功能应用0506.txt"
    document_type = "txt"
else:
    document_path = input("请输入要处理的文档路径: ")
    document_type = Path(document_path).suffix.lstrip('.')

print(f"将使用文档: {document_path}")

# 创建输出目录
output_dir = "D:/0AI/TESTCASE/2025CASE/output"
os.makedirs(output_dir, exist_ok=True)

# 保存文档信息
output_filename = input("请输入保存文档信息的文件名(不含扩展名): ")
output_file = os.path.join(output_dir, f"{output_filename}.json")

document_info = {
    "document_path": document_path,
    "document_type": document_type,
    "confirmed_by_user": True
}

with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(document_info, f, ensure_ascii=False, indent=2)

print(f"文档信息已保存到: {output_file}")
print("请继续执行下一步: 文本文件解析")

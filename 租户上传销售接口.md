租户上传销售接口
文档名称(title)	接口开发说明
作者(Author)	科传
审批者 (To Be Approved By)	科传项目经理：
说明 (Comments)	给第三方公司使用，上传销售数据
文档版本	日期	文件修订摘要	作者
V1.05	2016-02-23	整理的标准版销售接口	Square
V1.1	2020-02-24	修改为首都机场日上使用的简易版本	David
V1.2	2020-04-03	1请求增加交易笔数，2增加方法salestransreplacelitev61 同一日期同一店铺号，同一收银机号上传销售时历史销售会清零，以最后一笔销售为准	David
简要描述
接口方式：Restful接口(HTTP POST)
输入的参数要求编码格式为UTF-8，数据格式为：Json
接口名称：salesTransLiteV61
接口说明：创建销售单（商场版）
请求URL
（正常销售）
http://ip:端口/程序包/rest/salestransaction/salestranslitev61
（覆盖历史销售）
http://ip:端口/程序包/rest/salestransaction/salestransreplacelitev61
请求方式
POST
请求参数说明
参数名	必选	类型	说明
apiKey	是	string	APIKEY
signature	否	string	签名方式情况看后面的介绍。如果内部调用可以配置不校验签
docKey	是	string	必須唯一，用來判断該记录是否已经处理。e.g.日期.店铺编号.收款机编号.销售单号 :20151001.SH001.01.S000000001
transHeader	是	TransHeader	TransHeader
salesTotal	是	SalesTotalLite	SalesTotalLite
salesItem[]	是	SalesItemLite	货品信息
salesTender[]	是	SalesTenderLite	付款信息
orgSalesMemo	否	SalesMemo	原销售单的信息，只适用于原单退货。
TransHeader
参数名	必选	类型	说明
txDate	否	string	交易日期yyyy-mm-dd，如无提供，则用LedgerDate代替
ledgerDatetime	是	string	yyyy-mm-dd hh:mm:ss客戶端发生的日期及时间( System Clock )
storeCode	是	string	店铺编号
tillId	是	string	收款机号
docNo	是	string	销售单号
voidDocNo	否	string	预留字段，被取消的原销售单号
txAttrib	否	string	预留字段
SalesTotalLite
参数名	必选	类型	说明
cashier	是	string	收款员编号
vipCode	否	string	会员编号
netQty	是	Decimal	净销售数量（销售为正数，退货为负数）
netAmount	是	Decimal	净销售金额（销售为正数，退货为负数）
extendParameter	否	string	扩展参数
memoCnt	否	Integer	交易笔数，没有传入时默认1
SalesItemLite
参数名	必选	类型	说明
salesLineNumber	是	Integer	销售行号从1开始多种货品的时候按顺序1,2,3…
salesman[5]	否	string	销售员
itemCode	是	string	货号或者条形码
itemLotNum	是	string	货品批号,默认填 *
inventoryType	是	Integer	库存类型 : 0 to 50 – 正常类型,默认填0
qty	是	Decimal	销售数量（销售为正数，退货为负数）
itemDiscountLess	是	Decimal	货品折扣金额
totalDiscountLess	是	Decimal	整单折扣所摊分的金额
originalPrice	是	Decimal	单价（吊牌价）
netAmount	是	Decimal	净销售金额（即实收金额）（销售为正数，退货为负数）
salesItemRemark	否	string	货品备注
extendParameter	否	string	扩展参数
SalesTenderLite
参数名	必选	类型	说明
lineno	是	Integer	行号，从1开始多种付款方式的时候按顺序1,2,3…
baseCurrencyCode	是	String	本地货币编号
tenderCode	是	string	付款方式编号
payAmount	是	Decimal	付款金额
baseAmount	是	Decimal	本位币付款金额
excessAmount	是	Decimal	多收金额（例如礼券不设找零）
extendParameter	否	string	扩展参数
SalesMemo
参数名	必选	类型	说明
txDate	是	string	交易日期yyyy-mm-dd
storeCode	是	string	店铺编号
tillId	是	string	收款机号
docNo	是	string	销售单号
docKey	是	string	销售单的DocKey
请求示例
{
  "apiKey": "TEST",
  "signature": "F1006AFD16C5F16874D19E8BF045621E",
  "docKey": "20200224.220007.01.S20020224000001",
  "transHeader": {
    "txDate": "2020-02-24",
    "ledgerDatetime": "2020-02-24 10:21:01",
    "storeCode": "220007",
    "tillId": "01",
    "docNo": "S20020224000001"
  },
  "salesTotal": {
    "cashier": "000000",
    "vipCode": "",
    "netQty": 1,
    "netAmount": 100,
    "extendParameter": ""
  },
  "salesItem": [
    {
      "salesLineNumber": 1,
      "salesman" : [ "000000" ],
      "itemCode": "10301010002",
      "itemLotNum": "*",
      "inventoryType": 0,
      "qty": 2,
      "originalPrice": 50,
      "itemDiscountLess": 0,
      "totalDiscountLess": 0,
      "netAmount": 100,
      "salesItemRemark": "",
      "extendParameter": ""
    }
  ],
  "salesTender": [
    {
      "lineno": 1,
      "baseCurrencyCode": "RMB",
      "tenderCode": "RM",
      "payAmount": 50,
      "baseAmount": 50,
      "excessAmount": 0,
      "extendParameter": ""
    },
    {
      "lineno": 2,
      "baseCurrencyCode": "RMB",
      "tenderCode": "CH",
      "payAmount": 50,
      "baseAmount": 50,
      "excessAmount": 0,
      "extendParameter": ""
    }
  ]
}
响应示例
{
    "errorCode": 0,
    "errorMessage": "[0]"
}
{
    "errorCode": 67,
    "errorMessage": "此店铺号和日期存在有销售单据在处理队列中，不允许上传覆盖类型的销售数据(found sales memo in transqueue) !  此店铺号和日期存在有销售单据在处理队列中，不允许上传覆盖类型的销售数据(found sales memo in transqueue) ! [0]"
}
返回参数说明
参数名	类型	说明
errorCode	String	错误编码
errorMessage	String	错误信息
错误代码
错误代码	错误描述
0	成功
-10000	无效的签名
-10001	Api Key找不到
-10002	无效的Api Key
-10003	Api Key未生效
-10004	Api Key已过期
-10005	Api Key不能执行该功能
-1	没有传入参数
-2	没有参数docKey
-3	没有参数transHeader
-4	没有参数salesTotal
-5	没有参数salesItem
-6	没有参数salesTender
-7	没有销售单号
-8	没有店铺编号
-9	没有收款机编号
-10	没有Ledger Datetime
-11	没有货品编号
-12	货品编号找不到
-13	销售单号找不到
-14	销售单号已经存在
-15	原销售单号找不到
-16	店铺编号找不到
-17	收款机号找不到
-18	付款金额超出货品金额
-19	付款金额小于货品金额
-20	付款金额不等于退货金额
-21	无效的交易日期范围
-22	对应的货品编号不唯一
-23	对应的付款方式编号不唯一
-67	有销售单据在处理队列中未处理，现在不能上传此店铺的相关数据

测试环境信息
上传测试地址：http://kc.lvgemgroup.com.cn:8185/posservice/rest/salestransaction/salestranslitev61

测试上传参数：
apikey：LVGEMHSL
店铺号storecode：09210029
收银机号tillid：00
收银员编号cashier：09210029
商品编号：0921002901
付款方式编码：CH
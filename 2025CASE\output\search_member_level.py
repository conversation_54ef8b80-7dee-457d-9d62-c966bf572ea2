"""
搜索知识库中与会员等级相关的内容
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

try:
    from knowledge_base import KnowledgeBaseManager
except ImportError:
    print("错误: 无法导入knowledge_base模块，请确保该模块已正确安装")
    sys.exit(1)

def search_member_level_info():
    """搜索会员等级相关信息"""
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 准备搜索关键词列表
    search_keywords = [
        "会员等级",
        "会员级别",
        "普通会员",
        "VIP会员",
        "钻石会员",
        "会员分级",
        "会员权益",
        "会员折扣"
    ]
    
    # 执行搜索并收集结果
    all_results = {}
    for keyword in search_keywords:
        print(f"\n执行搜索: {keyword}")
        results = kb.search(keyword, limit=5)
        
        if results:
            all_results[keyword] = results
            print(f"找到 {len(results)} 条结果")
            
            # 显示第一条结果的部分内容
            if results:
                doc_id = results[0].get("id")
                doc = kb.get_document(doc_id)
                if doc:
                    content = doc.get("content", "")
                    print(f"文档ID: {doc_id}")
                    print(f"文档标题: {doc.get('title', '未知标题')}")
                    print(f"文档内容预览: {content[:200]}...")
        else:
            print("未找到匹配结果")
    
    # 获取所有文档，查找可能包含会员等级信息的文档
    print("\n\n检查所有文档中的会员等级信息:")
    all_docs = kb.list_documents()
    member_level_docs = []
    
    for doc_meta in all_docs:
        doc_id = doc_meta.get("id")
        doc = kb.get_document(doc_id)
        if doc:
            content = doc.get("content", "").lower()
            if any(keyword.lower() in content for keyword in search_keywords):
                member_level_docs.append(doc)
                print(f"找到相关文档: {doc.get('title', '未知标题')} (ID: {doc_id})")
    
    # 提取会员等级相关知识点
    knowledge_points = []
    for doc in member_level_docs:
        content = doc.get("content", "")
        title = doc.get("title", "未知文档")
        
        # 分析文档内容，提取会员等级相关信息
        if "会员" in content and "等级" in content:
            lines = content.split("\n")
            for i, line in enumerate(lines):
                if any(keyword in line for keyword in ["会员", "等级", "VIP", "钻石", "普通会员"]):
                    # 提取上下文
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    context = "\n".join(lines[start:end])
                    knowledge_points.append({
                        "title": title,
                        "context": context,
                        "line": line
                    })
    
    # 输出结果
    print("\n\n会员等级相关知识点:")
    for i, point in enumerate(knowledge_points):
        print(f"\n知识点 {i+1}:")
        print(f"来源: {point['title']}")
        print(f"内容: {point['context']}")
    
    # 保存结果到文件
    output = {
        "search_results": all_results,
        "knowledge_points": knowledge_points
    }
    
    output_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\member_level_info.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {output_path}")
    
    # 生成Markdown报告
    md_content = "# 会员等级相关知识点\n\n"
    
    if knowledge_points:
        md_content += "## 提取的知识点\n\n"
        for i, point in enumerate(knowledge_points):
            md_content += f"### 知识点 {i+1}\n"
            md_content += f"**来源**: {point['title']}\n\n"
            md_content += f"**内容**:\n```\n{point['context']}\n```\n\n"
    else:
        md_content += "未找到相关知识点。\n\n"
    
    md_content += "## 搜索结果\n\n"
    for keyword, results in all_results.items():
        md_content += f"### 关键词: {keyword}\n\n"
        if results:
            for result in results:
                md_content += f"- **文档**: {result.get('title', '未知标题')}\n"
                md_content += f"  - ID: {result.get('id', '未知ID')}\n"
                md_content += f"  - 相关度: {round(result.get('score', 0), 2)}\n"
                md_content += f"  - 分类: {result.get('category', '未分类')}\n\n"
        else:
            md_content += "未找到匹配结果\n\n"
    
    md_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\member_level_knowledge.md"
    with open(md_path, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    print(f"Markdown报告已保存到: {md_path}")
    
    return md_path

if __name__ == "__main__":
    search_member_level_info()

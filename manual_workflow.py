import os
import sys
import yaml
import datetime
import json
from pathlib import Path

# 获取当前目录
current_dir = Path(__file__).parent.absolute()
print(f"当前目录: {current_dir}")

# 读取当前打开的文件路径
current_file = "POS外送功能应用0506.txt"
current_file_path = current_dir / current_file
print(f"当前文件: {current_file_path}")
print(f"文件存在: {current_file_path.exists()}")

# 创建输出目录
input_dir = current_dir / "2025CASE" / "input"
output_dir = current_dir / "2025CASE" / "output"
input_dir.mkdir(parents=True, exist_ok=True)
output_dir.mkdir(parents=True, exist_ok=True)

# 如果当前文件存在，复制到输入目录
if current_file_path.exists():
    import shutil
    input_file_path = input_dir / current_file
    shutil.copy2(current_file_path, input_file_path)
    print(f"已复制文件到: {input_file_path}")

    # 创建文档信息JSON
    doc_info = {
        "document_path": str(input_file_path),
        "document_type": "txt",
        "confirmed_by_user": True
    }
    
    # 保存文档信息
    doc_info_path = output_dir / "文档信息.json"
    with open(doc_info_path, 'w', encoding='utf-8') as f:
        json.dump(doc_info, f, ensure_ascii=False, indent=2)
    
    print(f"已保存文档信息到: {doc_info_path}")
    print(f"文档信息: {doc_info}")
    
    print("\n您现在可以使用以下命令继续工作流:")
    print("1. 使用 /work now 查看当前工作状态")
    print("2. 使用 /work next 进入下一个任务")
    
else:
    print(f"错误: 文件不存在: {current_file_path}")
    print("请提供要处理的文档路径:")

# 营销中心标签满减POS应用

## 文件信息
- 文件名: 营销中心标签满减POS应用.txt
- 文件类型: 文本文件
- 导入时间: 2025-04-21 15:30:00

## 测试点列表

### 测试点 1: 功能概述
POS端有标签的会员命中满减活动

### 测试点 2: 前置条件
1. 营销中心已设置满减活动：
   - 活动1：满30减3，适用人群标签A
   - 活动2：满50减5，适用人群标签B
   - 活动3：满20减1，适用人群全部标签
2. 会员信息如下：
   - 会员1有标签A
   - 会员2有标签B
   - 会员3标签AB都有
   - 会员4无标签

### 测试点 3: 测试场景 - 账单金额10元
已点餐，账单金额10元，应用不同会员，活动命中情况：
- 会员1（标签A）：账单金额10元 < 活动1条件30元，不命中活动1；账单金额10元 < 活动3条件20元，不命中活动3
- 会员2（标签B）：账单金额10元 < 活动2条件50元，不命中活动2；账单金额10元 < 活动3条件20元，不命中活动3
- 会员3（标签AB）：账单金额10元 < 活动1条件30元，不命中活动1；账单金额10元 < 活动2条件50元，不命中活动2；账单金额10元 < 活动3条件20元，不命中活动3
- 会员4（无标签）：不满足任何活动的人群条件，不命中任何活动

### 测试点 4: 测试场景 - 账单金额20元
已点餐，账单金额20元，应用不同会员，活动命中情况：
- 会员1（标签A）：账单金额20元 < 活动1条件30元，不命中活动1；账单金额20元 = 活动3条件20元，命中活动3，优惠1元
- 会员2（标签B）：账单金额20元 < 活动2条件50元，不命中活动2；账单金额20元 = 活动3条件20元，命中活动3，优惠1元
- 会员3（标签AB）：账单金额20元 < 活动1条件30元，不命中活动1；账单金额20元 < 活动2条件50元，不命中活动2；账单金额20元 = 活动3条件20元，命中活动3，优惠1元
- 会员4（无标签）：不满足任何活动的人群条件，不命中任何活动

### 测试点 5: 测试场景 - 账单金额30元
已点餐，账单金额30元，应用不同会员，活动命中情况：
- 会员1（标签A）：账单金额30元 = 活动1条件30元，命中活动1，优惠3元；账单金额30元 > 活动3条件20元，命中活动3，优惠1元；自动命中最大优惠活动1，最终优惠3元
- 会员2（标签B）：账单金额30元 < 活动2条件50元，不命中活动2；账单金额30元 > 活动3条件20元，命中活动3，优惠1元
- 会员3（标签AB）：账单金额30元 = 活动1条件30元，命中活动1，优惠3元；账单金额30元 < 活动2条件50元，不命中活动2；账单金额30元 > 活动3条件20元，命中活动3，优惠1元；自动命中最大优惠活动1，最终优惠3元
- 会员4（无标签）：不满足任何活动的人群条件，不命中任何活动

### 测试点 6: 测试场景 - 账单金额50元
已点餐，账单金额50元，应用不同会员，活动命中情况：
- 会员1（标签A）：账单金额50元 > 活动1条件30元，命中活动1，优惠3元；账单金额50元 > 活动3条件20元，命中活动3，优惠1元；自动命中最大优惠活动1，最终优惠3元
- 会员2（标签B）：账单金额50元 = 活动2条件50元，命中活动2，优惠5元；账单金额50元 > 活动3条件20元，命中活动3，优惠1元；自动命中最大优惠活动2，最终优惠5元
- 会员3（标签AB）：账单金额50元 > 活动1条件30元，命中活动1，优惠3元；账单金额50元 = 活动2条件50元，命中活动2，优惠5元；账单金额50元 > 活动3条件20元，命中活动3，优惠1元；自动命中最大优惠活动2，最终优惠5元
- 会员4（无标签）：不满足任何活动的人群条件，不命中任何活动

### 测试点 7: 其他说明
1. 不考虑满减与其他优惠共享场景
2. 存在多个满减时，自动命中最大优惠
3. 默认会员标签可正常获取
4. 仅功能验证

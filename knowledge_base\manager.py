"""
知识库管理器
负责测试点文档的存储、检索和管理
"""

import os
import json
import shutil
import datetime
from pathlib import Path
from typing import List, Dict, Optional, Union

from .parser import DocumentParser
from .indexer import KnowledgeIndexer

class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self, base_dir: str = "_agent-local/knowledge/test_points"):
        """
        初始化知识库管理器
        
        Args:
            base_dir: 知识库根目录
        """
        self.base_dir = Path(base_dir)
        self.docs_dir = self.base_dir / "documents"
        self.index_dir = self.base_dir / "index"
        self.metadata_file = self.base_dir / "metadata.json"
        
        # 确保目录存在
        self.docs_dir.mkdir(parents=True, exist_ok=True)
        self.index_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化解析器和索引器
        self.parser = DocumentParser()
        self.indexer = KnowledgeIndexer(self.index_dir)
        
        # 加载元数据
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict:
        """加载知识库元数据"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建初始元数据
            metadata = {
                "created_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat(),
                "document_count": 0,
                "documents": {}
            }
            self._save_metadata(metadata)
            return metadata
    
    def _save_metadata(self, metadata: Dict) -> None:
        """保存知识库元数据"""
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    def add_document(self, file_path: str, category: str = "general") -> str:
        """
        添加文档到知识库
        
        Args:
            file_path: 文档路径
            category: 文档分类
            
        Returns:
            文档ID
        """
        # 解析文档
        doc_content = self.parser.parse(file_path)
        
        # 生成文档ID
        doc_id = f"{category}_{Path(file_path).stem}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # 保存文档
        doc_path = self.docs_dir / f"{doc_id}.md"
        with open(doc_path, 'w', encoding='utf-8') as f:
            f.write(doc_content)
        
        # 更新元数据
        self.metadata["document_count"] += 1
        self.metadata["updated_at"] = datetime.datetime.now().isoformat()
        self.metadata["documents"][doc_id] = {
            "original_file": file_path,
            "stored_file": str(doc_path),
            "category": category,
            "added_at": datetime.datetime.now().isoformat(),
            "title": Path(file_path).stem
        }
        self._save_metadata(self.metadata)
        
        # 索引文档
        self.indexer.index_document(doc_id, doc_content, self.metadata["documents"][doc_id])
        
        return doc_id
    
    def get_document(self, doc_id: str) -> Optional[Dict]:
        """
        获取文档信息
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档信息，如果不存在则返回None
        """
        if doc_id in self.metadata["documents"]:
            doc_info = self.metadata["documents"][doc_id]
            doc_path = self.docs_dir / f"{doc_id}.md"
            
            if doc_path.exists():
                with open(doc_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                return {
                    "id": doc_id,
                    "content": content,
                    **doc_info
                }
        
        return None
    
    def search(self, query: str, category: Optional[str] = None, limit: int = 10) -> List[Dict]:
        """
        搜索知识库
        
        Args:
            query: 搜索关键词
            category: 限定分类
            limit: 返回结果数量限制
            
        Returns:
            匹配的文档列表
        """
        results = self.indexer.search(query, limit)
        
        # 如果指定了分类，过滤结果
        if category:
            results = [r for r in results if self.metadata["documents"].get(r["id"], {}).get("category") == category]
        
        # 补充文档信息
        for result in results:
            doc_id = result["id"]
            if doc_id in self.metadata["documents"]:
                result.update(self.metadata["documents"][doc_id])
        
        return results
    
    def list_documents(self, category: Optional[str] = None) -> List[Dict]:
        """
        列出知识库中的所有文档
        
        Args:
            category: 限定分类
            
        Returns:
            文档列表
        """
        docs = []
        
        for doc_id, doc_info in self.metadata["documents"].items():
            if category is None or doc_info["category"] == category:
                docs.append({
                    "id": doc_id,
                    **doc_info
                })
        
        # 按添加时间排序
        docs.sort(key=lambda x: x["added_at"], reverse=True)
        
        return docs
    
    def delete_document(self, doc_id: str) -> bool:
        """
        删除文档
        
        Args:
            doc_id: 文档ID
            
        Returns:
            是否删除成功
        """
        if doc_id in self.metadata["documents"]:
            # 删除文档文件
            doc_path = self.docs_dir / f"{doc_id}.md"
            if doc_path.exists():
                os.remove(doc_path)
            
            # 从索引中删除
            self.indexer.remove_document(doc_id)
            
            # 更新元数据
            del self.metadata["documents"][doc_id]
            self.metadata["document_count"] = len(self.metadata["documents"])
            self.metadata["updated_at"] = datetime.datetime.now().isoformat()
            self._save_metadata(self.metadata)
            
            return True
        
        return False
    
    def update_document(self, doc_id: str, file_path: str) -> bool:
        """
        更新文档
        
        Args:
            doc_id: 文档ID
            file_path: 新文档路径
            
        Returns:
            是否更新成功
        """
        if doc_id in self.metadata["documents"]:
            # 删除旧文档
            self.delete_document(doc_id)
            
            # 添加新文档，保持原ID
            category = self.metadata["documents"][doc_id]["category"]
            self.add_document(file_path, category)
            
            return True
        
        return False
    
    def get_categories(self) -> List[str]:
        """
        获取所有分类
        
        Returns:
            分类列表
        """
        categories = set()
        
        for doc_info in self.metadata["documents"].values():
            categories.add(doc_info["category"])
        
        return sorted(list(categories))
    
    def get_statistics(self) -> Dict:
        """
        获取知识库统计信息
        
        Returns:
            统计信息
        """
        categories = {}
        for doc_info in self.metadata["documents"].values():
            category = doc_info["category"]
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        return {
            "total_documents": self.metadata["document_count"],
            "categories": categories,
            "created_at": self.metadata["created_at"],
            "updated_at": self.metadata["updated_at"]
        }

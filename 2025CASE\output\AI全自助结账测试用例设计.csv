用例编号,用例名称,前置条件,测试步骤,预期结果,测试数据,优先级
TC-001,验证未开店状态下全自助功能限制,"已登录POS系统；系统处于未开店状态；菜品已设置AI图片","1. 在收银页面找到【全自助】按钮
2. 点击【全自助】按钮
3. 观察系统响应和提示信息","系统提示不允许操作加菜；不进入全自助点餐结账流程；保持在收银页面","系统状态=未开店",高
TC-002,验证未开班状态下全自助功能限制,"已登录POS系统；系统已开店但未开班；菜品已设置AI图片","1. 在收银页面找到【全自助】按钮
2. 点击【全自助】按钮
3. 观察系统响应和提示信息","系统提示不允许操作加菜；不进入全自助点餐结账流程；保持在收银页面","系统状态=已开店未开班",高
TC-003,验证单个菜品正常识别流程,"已登录POS系统，系统已开店且已开班；菜品已设置AI图片；准备单个可识别菜品","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将餐盘放置到AI识别区域
3. 等待系统自动识别菜品
4. 观察菜品识别结果和显示信息
5. 等待系统稳定后自动跳转","成功进入全自助点餐结账流程；系统自动识别出菜品；菜品上方显示菜品名称及序号；识别稳定后自动跳转至结账页面；声音播放'请扫码支付'","菜品类型=已设置AI图片的标准菜品",高
TC-004,验证多个菜品同时识别,"已登录POS系统，系统已开店且已开班；多个菜品已设置AI图片；准备3-5个不同的可识别菜品","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将包含多个菜品的餐盘放置到AI识别区域
3. 等待系统自动识别所有菜品
4. 观察每个菜品的识别结果
5. 检查菜品数量和名称显示","系统能够识别餐盘中的所有菜品；每个菜品上方显示对应的菜品名称及序号；菜品数量统计正确；识别稳定后自动跳转至结账页面","菜品数量=3-5个，菜品类型=不同的已设置AI图片菜品",高
TC-005,验证AI识别准确性边界测试,"已登录POS系统，系统已开店且已开班；准备相似外观的菜品","1. 使用外观相似的菜品进行识别测试
2. 在不同光线条件下进行识别测试
3. 测试菜品摆放角度对识别的影响
4. 观察识别准确性和稳定性","能够准确区分相似菜品；在合理光线范围内识别稳定；不同角度下识别准确率≥95%","相似菜品组合，不同光线条件，不同摆放角度",高
TC-006,验证无法识别菜品的修正功能,"已登录POS系统，系统已开店且已开班；准备无法识别的菜品（未设置AI图片）","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将包含无法识别菜品的餐盘放置到AI识别区域
3. 等待系统识别完成
4. 观察无法识别菜品的显示状态
5. 点击无法识别菜品上方的[修正]字样
6. 在菜品信息页面选择正确的菜品
7. 确认修正操作","无法识别的菜品上方显示[修正]字样；点击[修正]后进入菜品信息页面；能够手动选择正确的菜品进行绑定；修正后菜品显示正确的名称和信息；此次识别的菜品图片绑定给选定菜品","菜品类型=未设置AI图片的菜品",高
TC-007,验证已识别菜品不允许修正,"已登录POS系统，系统已开店且已开班；准备已成功识别的菜品","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将餐盘放置到AI识别区域
3. 等待系统成功识别菜品
4. 尝试点击已识别菜品的显示区域
5. 观察系统响应","已识别的菜品正常显示菜品名称及序号；已识别的菜品不显示[修正]字样；点击已识别菜品无法进入修正功能；系统不允许对已识别菜品进行修正操作","菜品类型=已设置AI图片且能正常识别的菜品",中
TC-008,验证单个菜品金额计算准确性,"已登录POS系统，系统已开店且已开班；准备单个已知价格的菜品","1. 记录菜品的标准价格
2. 点击【全自助】按钮进入全自助点餐结账流程
3. 将餐盘放置到AI识别区域
4. 等待系统识别完成
5. 检查左侧显示的应收金额、优惠金额、菜品数量和待付金额","应收金额等于菜品标准价格；菜品数量显示为1；如无优惠活动，优惠金额为0，待付金额等于应收金额；如有优惠活动，优惠金额和待付金额计算正确","菜品价格=已知标准价格，菜品数量=1",高
TC-009,验证多个菜品金额计算准确性,"已登录POS系统，系统已开店且已开班；准备多个不同价格的菜品","1. 记录所有菜品的标准价格和数量
2. 计算预期的总金额
3. 点击【全自助】按钮进入全自助点餐结账流程
4. 将餐盘放置到AI识别区域
5. 等待系统识别完成
6. 检查左侧显示的应收金额、优惠金额、菜品数量和待付金额","应收金额等于所有菜品价格总和；菜品数量统计正确；优惠金额和待付金额计算准确；各项金额显示清晰无误","菜品组合=3-5个不同价格菜品，总金额=预计算标准总价",高
TC-010,验证优惠金额计算准确性,"已登录POS系统，系统已开店且已开班；系统配置有优惠活动；准备符合优惠条件的菜品组合","1. 确认当前优惠活动规则
2. 选择符合优惠条件的菜品组合
3. 计算预期的优惠金额和待付金额
4. 执行识别和金额计算流程
5. 验证优惠金额计算结果","优惠金额按照活动规则正确计算；待付金额=应收金额-优惠金额；优惠信息清晰显示","优惠规则=当前系统配置，菜品组合=符合优惠条件",高
TC-011,验证手动扫码支付功能,"已登录POS系统，系统已开店且已开班；菜品正在识别中且已确定识别成功；尚未自动进入付款页面","1. 完成菜品识别但等待在识别页面
2. 点击【扫码支付】按钮
3. 观察页面跳转情况
4. 检查支付页面显示内容","成功进入扫码付款页面；页面显示正确的付款金额；显示付款二维码或付款提示；声音播放'请扫码支付'","操作时机=识别成功但未自动跳转时",高
TC-012,验证继续识别功能,"已登录POS系统，系统已开店且已开班；菜品正在识别中且未进入付款页面","1. 将餐盘放置到AI识别区域
2. 在识别过程中点击【继续识别】按钮
3. 观察系统响应
4. 检查识别结果是否更新","系统重新开始识别菜品；之前的识别结果被清除；重新显示识别过程；最终显示新的识别结果","操作时机=菜品识别过程中",中
TC-013,验证清空商品功能,"已登录POS系统，系统已开店且已开班；菜品正在识别中且未进入付款页面","1. 将餐盘放置到AI识别区域并完成部分识别
2. 点击【清空商品】按钮
3. 观察页面变化
4. 检查是否重新开始识别","所有已识别的菜品被清空；金额信息重置为0；系统重新开始识别菜品；页面恢复到初始识别状态","操作时机=部分菜品已识别时",中
TC-014,验证员工登录退出功能,"已登录POS系统，系统已开店且已开班；菜品正在识别中且未进入付款页面；已知有效的员工密码","1. 在识别过程中点击右上角X按钮
2. 观察是否弹出员工登录页面
3. 输入正确的员工密码
4. 点击确定按钮
5. 观察系统响应","点击X按钮后弹出员工登录页面；输入正确密码后成功退出全自助模式；返回到收银页面；全自助流程被终止","员工密码=有效的员工登录密码",高
TC-015,验证员工登录取消操作,"已登录POS系统，系统已开店且已开班；菜品正在识别中且未进入付款页面","1. 在识别过程中点击右上角X按钮
2. 观察员工登录页面弹出
3. 点击取消按钮
4. 观察系统响应","员工登录页面关闭；返回到菜品识别页面；继续之前的识别流程；不退出全自助模式","操作=点击取消按钮",中
TC-016,验证正确付款码扫码支付成功,"已登录POS系统，系统已开店且已开班；已完成菜品识别并进入扫码付款页面；准备有效的付款码","1. 在扫码付款页面使用有效付款码进行扫码
2. 等待支付处理完成
3. 观察支付结果提示
4. 检查是否自动完成结账流程","扫码支付成功；显示支付成功提示信息；自动完成结账流程；返回到收银页面或显示完成页面","付款码=有效的微信/支付宝付款码",高
TC-017,验证错误付款码扫码处理,"已登录POS系统，系统已开店且已开班；已完成菜品识别并进入扫码付款页面；准备无效或错误的付款码","1. 在扫码付款页面使用无效或错误的付款码进行扫码
2. 观察系统响应
3. 检查错误提示信息
4. 验证是否可以重新扫码","系统识别出错误的付款码；显示明确的错误提示信息；不允许支付操作继续；可以重新进行扫码操作","付款码=无效、过期或格式错误的付款码",高
TC-018,验证支付成功后流水记录,"已登录POS系统，系统已开店且已开班；已完成一笔全自助扫码支付","1. 完成一笔全自助点餐结账支付
2. 进入流水查询功能
3. 查找刚完成的交易记录
4. 检查流水中的付款方式和金额信息
5. 验证交易详细信息的准确性","流水中能找到对应的交易记录；付款方式正确显示（微信/支付宝等）；支付金额与实际支付金额一致；交易时间、菜品信息等详细信息准确；交易状态显示为成功","交易金额=实际支付金额，付款方式=实际使用的付款方式",高
TC-019,验证空餐盘识别处理,"已登录POS系统，系统已开店且已开班；准备空餐盘（无任何菜品）","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将空餐盘放置到AI识别区域
3. 等待系统识别处理
4. 观察系统响应和提示信息","系统能够识别出餐盘为空；显示相应的提示信息（如"请放置菜品"）；不进入结账流程；等待用户放置菜品","餐盘状态=完全空白",中
TC-020,验证大量菜品同时识别的处理能力,"已登录POS系统，系统已开店且已开班；准备大量菜品（超过正常数量）","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将包含大量菜品的餐盘放置到AI识别区域
3. 等待系统识别处理
4. 观察识别速度和准确性
5. 检查金额计算是否正确","系统能够处理大量菜品的识别；识别结果准确，不遗漏菜品；金额计算正确；系统性能稳定，不出现卡顿或崩溃","菜品数量=10-15个不同菜品",中
TC-021,验证网络异常时的支付处理,"已登录POS系统，系统已开店且已开班；已完成菜品识别并进入扫码付款页面；能够模拟网络中断","1. 在扫码付款页面准备进行支付
2. 模拟网络中断
3. 尝试扫码支付
4. 观察系统响应和错误处理
5. 恢复网络连接后重试支付","系统能够检测到网络异常；显示明确的网络错误提示；不会丢失当前订单信息；网络恢复后能够继续支付流程","网络状态=正常→中断→恢复",高
TC-022,验证同一菜品重复识别的处理,"已登录POS系统，系统已开店且已开班；准备多份相同菜品","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将包含多份相同菜品的餐盘放置到AI识别区域
3. 等待系统识别完成
4. 检查菜品数量统计
5. 验证金额计算准确性","系统能够正确识别多份相同菜品；菜品数量统计准确；每份菜品都有正确的序号显示；总金额计算正确（单价×数量）","菜品类型=同一种菜品，菜品数量=3-5份",中
TC-023,验证混合识别状态的处理,"已登录POS系统，系统已开店且已开班；准备混合的可识别和不可识别菜品","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将混合菜品的餐盘放置到AI识别区域
3. 等待系统识别完成
4. 观察识别结果的显示状态
5. 对需要修正的菜品进行修正操作
6. 检查最终的金额计算","可识别菜品正常显示名称和序号；不可识别菜品显示[修正]字样；能够对需要修正的菜品进行修正；修正完成后金额计算正确；能够正常进入支付流程","菜品组合=2-3个可识别菜品+1-2个不可识别菜品",高
TC-024,验证支付超时的处理,"已登录POS系统，系统已开店且已开班；已完成菜品识别并进入扫码付款页面","1. 进入扫码付款页面
2. 长时间不进行任何支付操作
3. 观察系统是否有超时处理机制
4. 检查超时后的系统状态
5. 验证是否可以重新开始流程","系统在合理时间后提示支付超时；超时后自动退出支付流程或提供重试选项；不会无限期等待用户操作；超时处理不影响系统稳定性","超时时间=系统设定的支付超时时长",中
TC-025,验证识别过程中移动餐盘的处理,"已登录POS系统，系统已开店且已开班；准备菜品餐盘","1. 点击【全自助】按钮进入全自助点餐结账流程
2. 将餐盘放置到AI识别区域
3. 在识别过程中移动或移除餐盘
4. 观察系统响应
5. 重新放置餐盘并观察识别结果","系统能够检测到餐盘移动；显示相应的提示信息；重新放置后能够继续识别；不会因为移动而导致系统错误","移动时机=识别过程中的不同阶段",低

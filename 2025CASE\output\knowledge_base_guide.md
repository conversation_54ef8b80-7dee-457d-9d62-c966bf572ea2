# 测试点知识库管理指南

## 知识库概述
测试点知识库用于存储和管理测试点文档，支持TXT和MD格式的文档导入，并提供全文检索功能。这个知识库特别适合存储和管理测试用例、测试点、需求分析等文档，方便团队成员快速查找和复用测试资源。

## 知识库结构
- 知识库根目录: `_agent-local/knowledge/test_points`
- 文档存储目录: `_agent-local/knowledge/test_points/documents`
- 索引目录: `_agent-local/knowledge/test_points/index`
- 元数据文件: `_agent-local/knowledge/test_points/metadata.json`

## 知识库管理

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 导入单个文档
doc_id = kb.add_document("path/to/document.txt", category="功能测试")

# 批量导入目录下的所有文档
import os
from pathlib import Path

docs_dir = Path("path/to/documents")
for file in docs_dir.glob("*.txt"): 
    kb.add_document(str(file), category="功能测试")
```

### 更新文档
```python
kb.update_document("doc_id", "path/to/new_document.txt")
```

### 删除文档
```python
kb.delete_document("doc_id")
```

### 备份知识库
```python
import shutil
from datetime import datetime

# 备份整个知识库
backup_dir = f"knowledge_base_backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
shutil.copytree("_agent-local/knowledge/test_points", backup_dir)
```

### 恢复知识库
```python
import shutil

# 恢复知识库
shutil.rmtree("_agent-local/knowledge/test_points")
shutil.copytree("path/to/backup", "_agent-local/knowledge/test_points")
```

### 知识库统计
```python
stats = kb.get_statistics()
print(f"总文档数: {stats['total_documents']}")
print("分类统计:")
for category, count in stats['categories'].items():
    print(f"  - {category}: {count}文档")
print(f"创建时间: {stats['created_at']}")
print(f"最后更新: {stats['updated_at']}")
```

## 最佳实践
1. **定期备份**: 定期备份知识库，避免数据丢失。建议每周至少进行一次完整备份，重要更新后立即备份。
2. **分类管理**: 使用合理的分类体系，便于管理和检索。例如，可以按照功能模块、测试类型、优先级等维度进行分类。
3. **定期维护**: 定期清理过时的文档，保持知识库的时效性。建议每季度进行一次全面的知识库清理。
4. **标准化导入**: 尽量使用标准化的格式导入文档，提高解析质量。推荐使用Markdown格式，结构清晰，便于解析。
5. **增量更新**: 对于大型文档，优先考虑更新而非删除后重新导入，这样可以保留文档的历史记录和关联信息。
6. **命名规范**: 使用统一的命名规范，便于识别和管理。例如，可以使用"模块名_功能名_测试类型"的格式命名文档。
7. **版本控制**: 在文档中明确标注版本信息，便于追踪文档的变更历史。
8. **关键词标记**: 在文档中使用明确的关键词标记重要内容，提高检索效率。

## 常见问题
1. **索引损坏**: 如果索引损坏，可以删除索引目录，知识库会自动重建索引。
   ```python
   import shutil
   shutil.rmtree("_agent-local/knowledge/test_points/index")
   # 下次使用知识库时会自动重建索引
   ```

2. **导入失败**: 检查文档格式是否正确，尝试使用不同的编码打开文件。
   ```python
   # 尝试使用不同的编码导入文档
   with open("path/to/document.txt", "r", encoding="utf-8") as f:
       content = f.read()
   # 如果上面的代码失败，尝试其他编码
   with open("path/to/document.txt", "r", encoding="gbk") as f:
       content = f.read()
   ```

3. **搜索无结果**: 检查搜索关键词是否正确，尝试使用更通用的关键词。
   ```python
   # 尝试使用更通用的关键词
   results = kb.search("接口")  # 而不是 "物业接口验证"
   
   # 尝试使用模糊匹配
   results = kb.search("接口*")
   ```

4. **性能问题**: 当文档数量过多时，可能会影响性能，考虑分库管理。
   ```python
   # 创建多个知识库实例，分别管理不同类型的文档
   kb_functional = KnowledgeBaseManager(base_dir="_agent-local/knowledge/functional_tests")
   kb_performance = KnowledgeBaseManager(base_dir="_agent-local/knowledge/performance_tests")
   ```

5. **文档重复**: 如果发现文档重复，可以使用以下方法检查和删除重复文档。
   ```python
   # 检查重复文档
   all_docs = kb.list_documents()
   titles = {}
   for doc in all_docs:
       if doc["title"] in titles:
           print(f"发现重复文档: {doc['id']} 和 {titles[doc['title']]}")
       else:
           titles[doc["title"]] = doc["id"]
   
   # 删除重复文档
   kb.delete_document("重复的文档ID")
   ```

## 高级功能

### 批量操作
```python
# 批量删除某个分类下的所有文档
docs = kb.list_documents(category="过时的测试")
for doc in docs:
    kb.delete_document(doc["id"])

# 批量更新文档分类
docs = kb.list_documents(category="旧分类")
for doc in docs:
    kb.update_document_metadata(doc["id"], {"category": "新分类"})
```

### 导出知识库
```python
import json

# 导出所有文档的元数据
all_docs = kb.list_documents()
with open("exported_metadata.json", "w", encoding="utf-8") as f:
    json.dump(all_docs, f, ensure_ascii=False, indent=2)

# 导出所有文档的内容
for doc in all_docs:
    doc_content = kb.get_document(doc["id"])
    with open(f"exported_docs/{doc['id']}.txt", "w", encoding="utf-8") as f:
        f.write(doc_content["content"])
```

### 知识库合并
```python
# 将另一个知识库的内容合并到当前知识库
other_kb = KnowledgeBaseManager(base_dir="path/to/other/knowledge_base")
other_docs = other_kb.list_documents()

for doc_meta in other_docs:
    doc = other_kb.get_document(doc_meta["id"])
    # 将文档内容写入临时文件
    with open("temp_doc.txt", "w", encoding="utf-8") as f:
        f.write(doc["content"])
    # 导入到当前知识库
    kb.add_document("temp_doc.txt", category=doc_meta.get("category", "默认分类"))
```

## 定期维护计划
为了保持知识库的健康状态，建议按照以下计划进行定期维护：

1. **每日维护**:
   - 检查新导入文档的质量
   - 备份当天新增的重要文档

2. **每周维护**:
   - 完整备份知识库
   - 检查并修复索引问题

3. **每月维护**:
   - 清理临时文档
   - 更新文档分类
   - 检查重复文档

4. **每季度维护**:
   - 全面评估知识库内容
   - 归档过时文档
   - 优化知识库结构

通过遵循这些最佳实践和定期维护计划，您可以确保测试点知识库始终处于最佳状态，为团队提供高效的测试资源管理和检索服务。

import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

# 创建数据框
data = {
    "用例编号": [
        "TC-001", "TC-002", "TC-003", "TC-004", "TC-005", "TC-006", "TC-007", "TC-008",
        "TC-009", "TC-010", "TC-011", "TC-012", "TC-013", "TC-014", "TC-015", "TC-016"
    ],
    "用例名称": [
        "无标签会员不能享受满减活动",
        "无标签会员不能享受满减活动（高金额）",
        "会员1（标签A）账单金额低于满减条件时不应用满减",
        "会员2（标签B）账单金额低于满减条件时不应用满减",
        "会员1（标签A）享受对应满减活动",
        "会员2（标签B）享受对应满减活动",
        "会员1（标签A）享受全部标签适用的满减活动",
        "会员2（标签B）享受全部标签适用的满减活动",
        "会员3（标签A和B）享受全部标签适用的满减活动",
        "会员3（标签A和B）在30元消费时自动命中最优满减活动",
        "会员3（标签A和B）在50元消费时自动命中最优满减活动",
        "会员1（标签A）账单金额恰好等于满减活动最低消费金额（边界值测试）",
        "会员2（标签B）账单金额恰好等于满减活动最低消费金额（边界值测试）",
        "会员4（无标签）账单金额为10元（低于所有满减条件）",
        "会员3（标签A和B）账单金额为10元（低于所有满减条件）",
        "会员3（标签A和B）账单金额为20元（恰好等于全部标签满减条件）"
    ],
    "前置条件": [
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员4无标签",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员4无标签",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员1有标签A",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员2有标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员1有标签A",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员2有标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员1有标签A",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员2有标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员3有标签A和标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员3有标签A和标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员3有标签A和标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员1有标签A",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员2有标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员4无标签",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员3有标签A和标签B",
        "营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）\n会员3有标签A和标签B"
    ],
    "测试步骤": [
        "1. 在POS端完成点餐，账单金额为20元\n2. 应用会员4信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为50元\n2. 应用会员4信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为10元\n2. 应用会员1信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为10元\n2. 应用会员2信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为30元\n2. 应用会员1信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为50元\n2. 应用会员2信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为20元\n2. 应用会员1信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为20元\n2. 应用会员2信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为20元\n2. 应用会员3信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为30元\n2. 应用会员3信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为50元\n2. 应用会员3信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为30.00元（恰好等于满减条件）\n2. 应用会员1信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为50.00元（恰好等于满减条件）\n2. 应用会员2信息\n3. 观察系统应用的满减活动",
        "1. 在POS端完成点餐，账单金额为10元\n2. 应用会员4信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为10元\n2. 应用会员3信息\n3. 观察系统是否应用满减活动",
        "1. 在POS端完成点餐，账单金额为20.00元（恰好等于满减条件）\n2. 应用会员3信息\n3. 观察系统应用的满减活动"
    ],
    "预期结果": [
        "1. 系统提示无可用满减活动\n2. 账单金额保持20元不变，不享受任何满减",
        "1. 系统提示无可用满减活动\n2. 账单金额保持50元不变，不享受任何满减",
        "1. 系统提示无可用满减活动\n2. 账单金额保持10元不变，不享受任何满减",
        "1. 系统提示无可用满减活动\n2. 账单金额保持10元不变，不享受任何满减",
        "1. 系统自动命中活动1（满30减3）\n2. 账单金额从30元减少到27元",
        "1. 系统自动命中活动2（满50减5）\n2. 账单金额从50元减少到45元",
        "1. 系统自动命中活动3（满20减1）\n2. 账单金额从20元减少到19元",
        "1. 系统自动命中活动3（满20减1）\n2. 账单金额从20元减少到19元",
        "1. 系统自动命中活动3（满20减1）\n2. 账单金额从20元减少到19元",
        "1. 系统自动命中最优惠的活动1（满30减3）\n2. 账单金额从30元减少到27元",
        "1. 系统自动命中最优惠的活动2（满50减5）\n2. 账单金额从50元减少到45元",
        "1. 系统自动命中活动1（满30减3）\n2. 账单金额从30.00元减少到27.00元",
        "1. 系统自动命中活动2（满50减5）\n2. 账单金额从50.00元减少到45.00元",
        "1. 系统提示无可用满减活动\n2. 账单金额保持10元不变，不享受任何满减",
        "1. 系统提示无可用满减活动\n2. 账单金额保持10元不变，不享受任何满减",
        "1. 系统自动命中活动3（满20减1）\n2. 账单金额从20.00元减少到19.00元"
    ],
    "测试数据": [
        "会员信息: 会员4（无标签）\n账单金额: 20元",
        "会员信息: 会员4（无标签）\n账单金额: 50元",
        "会员信息: 会员1（标签A）\n账单金额: 10元",
        "会员信息: 会员2（标签B）\n账单金额: 10元",
        "会员信息: 会员1（标签A）\n账单金额: 30元",
        "会员信息: 会员2（标签B）\n账单金额: 50元",
        "会员信息: 会员1（标签A）\n账单金额: 20元",
        "会员信息: 会员2（标签B）\n账单金额: 20元",
        "会员信息: 会员3（标签A和标签B）\n账单金额: 20元",
        "会员信息: 会员3（标签A和标签B）\n账单金额: 30元",
        "会员信息: 会员3（标签A和标签B）\n账单金额: 50元",
        "会员信息: 会员1（标签A）\n账单金额: 30.00元",
        "会员信息: 会员2（标签B）\n账单金额: 50.00元",
        "会员信息: 会员4（无标签）\n账单金额: 10元",
        "会员信息: 会员3（标签A和标签B）\n账单金额: 10元",
        "会员信息: 会员3（标签A和标签B）\n账单金额: 20.00元"
    ],
    "优先级": [
        "高", "高", "高", "高", "高", "高", "高", "高",
        "高", "高", "高", "中", "中", "中", "中", "中"
    ]
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
output_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\营销中心标签满减POS应用用例.xlsx"
df.to_excel(output_path, index=False, engine='openpyxl')

# 加载工作簿以应用样式
wb = load_workbook(output_path)
ws = wb.active

# 定义样式
header_font = Font(bold=True)
header_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
wrap_alignment = Alignment(wrap_text=True, vertical='center')
border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

# 应用标题行样式
for cell in ws[1]:
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = wrap_alignment
    cell.border = border

# 应用数据行样式
for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
    for cell in row:
        cell.alignment = wrap_alignment
        cell.border = border

# 自动调整列宽
for column in ws.columns:
    max_length = 0
    column_letter = column[0].column_letter
    for cell in column:
        try:
            if len(str(cell.value)) > max_length:
                max_length = len(str(cell.value))
        except:
            pass
    adjusted_width = (max_length + 2) * 1.2
    ws.column_dimensions[column_letter].width = min(adjusted_width, 50)

# 保存格式化的Excel文件
wb.save(output_path)
print(f"Excel文件已保存到: {output_path}")

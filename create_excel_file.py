import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# 创建一个新的Excel工作簿
wb = openpyxl.Workbook()
ws = wb.active
ws.title = "会员等级满折POS应用测试用例"

# 定义标题行
headers = ["用例编号", "用例名称", "前置条件", "测试步骤", "预期结果", "测试数据", "优先级"]

# 设置标题行样式
header_font = Font(bold=True)
header_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
border = Border(
    left=Side(style="thin"),
    right=Side(style="thin"),
    top=Side(style="thin"),
    bottom=Side(style="thin")
)

# 写入标题行
for col_num, header in enumerate(headers, 1):
    cell = ws.cell(row=1, column=col_num, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = header_alignment
    cell.border = border

# 测试用例数据
test_cases = [
    {
        "id": "TC-001",
        "name": "普通会员账单金额80元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员1已注册为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为80元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统不应用任何满打折活动\n2. 账单金额保持80元不变\n3. 收银界面不显示任何折扣信息",
        "data": "会员类型: 普通会员\n账单金额: 80元",
        "priority": "高"
    },
    {
        "id": "TC-002",
        "name": "普通会员账单金额100元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员1已注册为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打90折活动\n2. 账单金额从100元变为90元\n3. 收银界面显示已应用\"满100打90折\"活动\n4. 优惠金额显示为10元",
        "data": "会员类型: 普通会员\n账单金额: 100元",
        "priority": "高"
    },
    {
        "id": "TC-003",
        "name": "普通会员账单金额200元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员1已注册为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为200元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打90折活动\n2. 账单金额从200元变为180元\n3. 收银界面显示已应用\"满100打90折\"活动\n4. 优惠金额显示为20元",
        "data": "会员类型: 普通会员\n账单金额: 200元",
        "priority": "高"
    },
    {
        "id": "TC-004",
        "name": "普通会员账单金额300元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员1已注册为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为300元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满300打75折活动（因为优惠更大）\n2. 账单金额从300元变为225元\n3. 收银界面显示已应用\"满300打75折\"活动\n4. 优惠金额显示为75元",
        "data": "会员类型: 普通会员\n账单金额: 300元",
        "priority": "高"
    },
    {
        "id": "TC-005",
        "name": "VIP会员账单金额80元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打85折活动，适用于VIP会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员2已注册为VIP会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为80元\n3. 选择会员2（VIP会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统不应用任何满打折活动\n2. 账单金额保持80元不变\n3. 收银界面不显示任何折扣信息",
        "data": "会员类型: VIP会员\n账单金额: 80元",
        "priority": "高"
    },
    {
        "id": "TC-006",
        "name": "VIP会员账单金额100元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打85折活动，适用于VIP会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员2已注册为VIP会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100元\n3. 选择会员2（VIP会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打85折活动\n2. 账单金额从100元变为85元\n3. 收银界面显示已应用\"满100打85折\"活动\n4. 优惠金额显示为15元",
        "data": "会员类型: VIP会员\n账单金额: 100元",
        "priority": "高"
    },
    {
        "id": "TC-007",
        "name": "VIP会员账单金额200元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打85折活动，适用于VIP会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员2已注册为VIP会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为200元\n3. 选择会员2（VIP会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打85折活动\n2. 账单金额从200元变为170元\n3. 收银界面显示已应用\"满100打85折\"活动\n4. 优惠金额显示为30元",
        "data": "会员类型: VIP会员\n账单金额: 200元",
        "priority": "高"
    },
    {
        "id": "TC-008",
        "name": "VIP会员账单金额300元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打85折活动，适用于VIP会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员2已注册为VIP会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为300元\n3. 选择会员2（VIP会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满300打75折活动（因为优惠更大）\n2. 账单金额从300元变为225元\n3. 收银界面显示已应用\"满300打75折\"活动\n4. 优惠金额显示为75元",
        "data": "会员类型: VIP会员\n账单金额: 300元",
        "priority": "高"
    },
    {
        "id": "TC-009",
        "name": "钻石会员账单金额80元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打80折活动，适用于钻石会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员3已注册为钻石会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为80元\n3. 选择会员3（钻石会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统不应用任何满打折活动\n2. 账单金额保持80元不变\n3. 收银界面不显示任何折扣信息",
        "data": "会员类型: 钻石会员\n账单金额: 80元",
        "priority": "高"
    },
    {
        "id": "TC-010",
        "name": "钻石会员账单金额100元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打80折活动，适用于钻石会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员3已注册为钻石会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100元\n3. 选择会员3（钻石会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打80折活动\n2. 账单金额从100元变为80元\n3. 收银界面显示已应用\"满100打80折\"活动\n4. 优惠金额显示为20元",
        "data": "会员类型: 钻石会员\n账单金额: 100元",
        "priority": "高"
    },
    {
        "id": "TC-011",
        "name": "钻石会员账单金额200元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打80折活动，适用于钻石会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员3已注册为钻石会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为200元\n3. 选择会员3（钻石会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打80折活动\n2. 账单金额从200元变为160元\n3. 收银界面显示已应用\"满100打80折\"活动\n4. 优惠金额显示为40元",
        "data": "会员类型: 钻石会员\n账单金额: 200元",
        "priority": "高"
    },
    {
        "id": "TC-012",
        "name": "钻石会员账单金额300元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打80折活动，适用于钻石会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员3已注册为钻石会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为300元\n3. 选择会员3（钻石会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满300打75折活动（因为优惠更大）\n2. 账单金额从300元变为225元\n3. 收银界面显示已应用\"满300打75折\"活动\n4. 优惠金额显示为75元",
        "data": "会员类型: 钻石会员\n账单金额: 300元",
        "priority": "高"
    },
    {
        "id": "TC-013",
        "name": "普通会员账单金额99.9元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 会员1已注册为普通会员\n3. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为99.9元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统不应用任何满打折活动\n2. 账单金额保持99.9元不变\n3. 收银界面不显示任何折扣信息",
        "data": "会员类型: 普通会员\n账单金额: 99.9元",
        "priority": "中"
    },
    {
        "id": "TC-014",
        "name": "普通会员账单金额100.1元满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 会员1已注册为普通会员\n3. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100.1元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打90折活动\n2. 账单金额从100.1元变为90.09元\n3. 收银界面显示已应用\"满100打90折\"活动\n4. 优惠金额显示为10.01元",
        "data": "会员类型: 普通会员\n账单金额: 100.1元",
        "priority": "中"
    },
    {
        "id": "TC-015",
        "name": "会员等级变更后满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满100打85折活动，适用于VIP会员\n3. 会员1初始为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100元\n3. 在后台将会员1从普通会员升级为VIP会员\n4. 选择会员1结账\n5. 观察系统是否应用正确的满打折活动",
        "expected": "1. 系统应用满100打85折活动（VIP会员活动）\n2. 账单金额从100元变为85元\n3. 收银界面显示已应用\"满100打85折\"活动\n4. 优惠金额显示为15元",
        "data": "会员类型: 从普通会员升级为VIP会员\n账单金额: 100元",
        "priority": "中"
    },
    {
        "id": "TC-016",
        "name": "账单金额为299.9元时满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员1已注册为普通会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为299.9元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统应用满100打90折活动\n2. 账单金额从299.9元变为269.91元\n3. 收银界面显示已应用\"满100打90折\"活动\n4. 优惠金额显示为29.99元",
        "data": "会员类型: 普通会员\n账单金额: 299.9元",
        "priority": "中"
    },
    {
        "id": "TC-017",
        "name": "非会员账单满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为300元\n3. 选择非会员结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统不应用任何满打折活动\n2. 账单金额保持300元不变\n3. 收银界面不显示任何折扣信息",
        "data": "会员类型: 非会员\n账单金额: 300元",
        "priority": "中"
    },
    {
        "id": "TC-018",
        "name": "会员信息无法识别时满打折活动应用",
        "preconditions": "1. 营销中心已设置满100打90折活动，适用于普通会员\n2. 营销中心已设置满300打75折活动，适用于全部会员\n3. 会员系统暂时不可用\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为300元\n3. 输入会员手机号但会员系统无法识别\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统提示\"会员信息无法识别\"\n2. 系统不应用任何满打折活动\n3. 账单金额保持300元不变\n4. 收银界面不显示任何折扣信息",
        "data": "会员类型: 无法识别\n账单金额: 300元",
        "priority": "中"
    },
    {
        "id": "TC-019",
        "name": "满打折活动配置错误时的系统处理",
        "preconditions": "1. 营销中心配置了错误的满打折活动（如折扣率为0或负数）\n2. 会员1已注册为普通会员\n3. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为100元\n3. 选择会员1（普通会员）结账\n4. 观察系统是否应用满打折活动",
        "expected": "1. 系统检测到活动配置错误\n2. 系统不应用错误配置的满打折活动\n3. 系统记录错误日志\n4. 账单金额保持100元不变",
        "data": "会员类型: 普通会员\n账单金额: 100元\n活动配置: 折扣率为0",
        "priority": "低"
    },
    {
        "id": "TC-020",
        "name": "多个满打折活动优惠金额相同时的选择",
        "preconditions": "1. 营销中心已设置满100打80折活动，适用于钻石会员\n2. 营销中心已设置满200打80折活动，适用于全部会员\n3. 会员3已注册为钻石会员\n4. POS系统正常运行",
        "steps": "1. 登录POS系统\n2. 点餐，使账单金额为200元\n3. 选择会员3（钻石会员）结账\n4. 观察系统选择哪个满打折活动",
        "expected": "1. 系统应用满100打80折活动（两个活动优惠金额相同，选择一个即可）\n2. 账单金额从200元变为160元\n3. 收银界面显示已应用的活动名称\n4. 优惠金额显示为40元",
        "data": "会员类型: 钻石会员\n账单金额: 200元",
        "priority": "低"
    }
]

# 设置单元格样式
cell_alignment = Alignment(vertical="center", wrap_text=True)

# 写入测试用例数据
for row_num, test_case in enumerate(test_cases, 2):
    ws.cell(row=row_num, column=1, value=test_case["id"]).alignment = cell_alignment
    ws.cell(row=row_num, column=2, value=test_case["name"]).alignment = cell_alignment
    ws.cell(row=row_num, column=3, value=test_case["preconditions"]).alignment = cell_alignment
    ws.cell(row=row_num, column=4, value=test_case["steps"]).alignment = cell_alignment
    ws.cell(row=row_num, column=5, value=test_case["expected"]).alignment = cell_alignment
    ws.cell(row=row_num, column=6, value=test_case["data"]).alignment = cell_alignment
    ws.cell(row=row_num, column=7, value=test_case["priority"]).alignment = cell_alignment
    
    # 为每个单元格添加边框
    for col_num in range(1, 8):
        ws.cell(row=row_num, column=col_num).border = border

# 调整列宽
for col_num in range(1, 8):
    column_letter = get_column_letter(col_num)
    if col_num == 1:  # 用例编号列
        ws.column_dimensions[column_letter].width = 10
    elif col_num == 2:  # 用例名称列
        ws.column_dimensions[column_letter].width = 30
    elif col_num == 3:  # 前置条件列
        ws.column_dimensions[column_letter].width = 40
    elif col_num == 4:  # 测试步骤列
        ws.column_dimensions[column_letter].width = 35
    elif col_num == 5:  # 预期结果列
        ws.column_dimensions[column_letter].width = 40
    elif col_num == 6:  # 测试数据列
        ws.column_dimensions[column_letter].width = 25
    elif col_num == 7:  # 优先级列
        ws.column_dimensions[column_letter].width = 10

# 保存Excel文件
wb.save("D:/0AI/TESTCASE/2025CASE/output/会员等级满折POS应用用例.xlsx")
print("Excel文件已成功生成！")

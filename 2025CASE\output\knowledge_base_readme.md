# 测试点知识库使用说明

## 简介

测试点知识库是一个用于管理和检索测试点文档的系统。它可以自动解析TXT或MD格式的测试点文档，将其存储到知识库中，并提供全文检索功能。

## 功能特点

- 支持TXT和MD格式的测试点文档导入
- 自动解析文档中的测试点
- 支持按关键词和分类检索
- 提供命令行工具和工作流两种使用方式
- 支持文档的添加、更新和删除
- 提供知识库统计信息

## 目录结构

```
knowledge_base/              # 知识库核心模块
├── __init__.py              # 初始化文件
├── manager.py               # 知识库管理器
├── parser.py                # 文档解析器
└── indexer.py               # 索引器

_agent-local/                # 工作流和知识库数据
├── workflows/               # 工作流配置
│   └── work/
│       └── test_point_knowledge.yml  # 测试点知识库工作流
└── knowledge/               # 知识库数据
    └── test_points/         # 测试点知识库
        ├── documents/       # 文档存储
        ├── index/           # 索引
        └── metadata.json    # 元数据

knowledge_base_cli.py        # 命令行工具
```

## 使用方法

### 方法一：使用命令行工具

1. **导入文档**

   ```bash
   python knowledge_base_cli.py import path/to/document.txt --category "登录功能"
   ```

   导入目录下的所有文档：

   ```bash
   python knowledge_base_cli.py import path/to/documents/ --category "登录功能"
   ```

2. **搜索文档**

   ```bash
   python knowledge_base_cli.py search "登录"
   ```

   限定分类：

   ```bash
   python knowledge_base_cli.py search "登录" --category "登录功能"
   ```

   显示内容：

   ```bash
   python knowledge_base_cli.py search "登录" --show-content
   ```

3. **查看文档**

   ```bash
   python knowledge_base_cli.py show document_id
   ```

4. **列出所有文档**

   ```bash
   python knowledge_base_cli.py list
   ```

   按分类列出：

   ```bash
   python knowledge_base_cli.py list --category "登录功能"
   ```

5. **删除文档**

   ```bash
   python knowledge_base_cli.py delete --id document_id
   ```

   删除分类下的所有文档：

   ```bash
   python knowledge_base_cli.py delete --category "登录功能" --all
   ```

6. **查看统计信息**

   ```bash
   python knowledge_base_cli.py stats
   ```

### 方法二：使用工作流

1. 启动测试点知识库工作流：

   ```
   /work use test_point_knowledge
   ```

2. 按照工作流提示操作，完成文档导入和知识库管理。

### 方法三：在Python代码中使用

```python
from knowledge_base import KnowledgeBaseManager

# 创建知识库管理器
kb = KnowledgeBaseManager()

# 导入文档
doc_id = kb.add_document("path/to/document.txt", category="登录功能")

# 搜索文档
results = kb.search("密码错误")

# 获取文档内容
doc = kb.get_document(doc_id)
print(doc["content"])

# 列出所有文档
all_docs = kb.list_documents()

# 删除文档
kb.delete_document(doc_id)

# 获取统计信息
stats = kb.get_statistics()
```

## 最佳实践

1. **文档组织**：按功能模块或测试类型组织文档，使用合适的分类
2. **定期维护**：定期清理过时的文档，保持知识库的时效性
3. **标准化格式**：尽量使用标准化的格式编写测试点文档，提高解析质量
4. **定期备份**：定期备份知识库数据，避免数据丢失

## 示例

### 示例1：导入测试点文档

```bash
python knowledge_base_cli.py import D:\0AI\TESTCASE\2025CASE\input\example_test_points.txt --category "登录功能"
```

### 示例2：搜索测试点

```bash
python knowledge_base_cli.py search "密码错误"
```

### 示例3：查看所有测试点

```bash
python knowledge_base_cli.py list
```

## 常见问题

1. **导入失败**：检查文档格式是否正确，尝试使用不同的编码打开文件
2. **搜索无结果**：检查搜索关键词是否正确，尝试使用更通用的关键词
3. **性能问题**：当文档数量过多时，可能会影响性能，考虑分库管理

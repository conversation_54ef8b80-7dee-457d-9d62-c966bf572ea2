"""
文档解析器
用于解析TXT和MD文件，提取测试点信息
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Optional

class DocumentParser:
    """文档解析器"""
    
    def __init__(self):
        """初始化解析器"""
        pass
    
    def parse(self, file_path: str) -> str:
        """
        解析文档，提取测试点信息
        
        Args:
            file_path: 文档路径
            
        Returns:
            解析后的Markdown格式内容
        """
        file_path = Path(file_path)
        
        # 检查文件是否存在
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 根据文件扩展名选择解析方法
        ext = file_path.suffix.lower()
        if ext == '.md':
            return self._parse_markdown(file_path)
        elif ext == '.txt':
            return self._parse_txt(file_path)
        else:
            raise ValueError(f"不支持的文件类型: {ext}")
    
    def _parse_markdown(self, file_path: Path) -> str:
        """
        解析Markdown文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理后的Markdown内容
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 如果已经是Markdown格式，进行必要的处理
        # 1. 添加文件信息头
        # 2. 确保测试点格式一致
        
        # 添加文件信息头
        file_info = f"""# {file_path.stem}

## 文件信息
- 文件名: {file_path.name}
- 文件类型: Markdown
- 导入时间: {Path(file_path).stat().st_mtime}

"""
        
        # 处理内容，确保测试点格式一致
        processed_content = self._process_test_points(content)
        
        return file_info + processed_content
    
    def _parse_txt(self, file_path: Path) -> str:
        """
        解析TXT文件，转换为Markdown格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            Markdown格式内容
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加文件信息头
        file_info = f"""# {file_path.stem}

## 文件信息
- 文件名: {file_path.name}
- 文件类型: 文本文件
- 导入时间: {Path(file_path).stat().st_mtime}

## 测试点列表

"""
        
        # 将TXT内容转换为Markdown格式的测试点
        test_points = self._extract_test_points_from_txt(content)
        
        return file_info + test_points
    
    def _extract_test_points_from_txt(self, content: str) -> str:
        """
        从TXT内容中提取测试点
        
        Args:
            content: TXT文件内容
            
        Returns:
            Markdown格式的测试点列表
        """
        lines = content.split('\n')
        test_points = []
        current_point = None
        point_content = []
        
        # 尝试识别测试点
        for line in lines:
            line = line.strip()
            
            # 跳过空行
            if not line:
                continue
            
            # 检查是否是新的测试点标题
            # 常见的测试点标题格式：数字+点、数字+括号、中文数字+点等
            if re.match(r'^(\d+[\.\)、]|[一二三四五六七八九十]+[\.\)、]|[A-Z][\.\)])', line):
                # 如果已有测试点，保存它
                if current_point:
                    test_points.append({
                        "title": current_point,
                        "content": '\n'.join(point_content)
                    })
                
                # 开始新的测试点
                current_point = line
                point_content = []
            else:
                # 继续当前测试点的内容
                if current_point:
                    point_content.append(line)
                else:
                    # 如果没有明确的测试点标题，将每行作为一个测试点
                    current_point = line
                    point_content = []
                    test_points.append({
                        "title": current_point,
                        "content": ''
                    })
                    current_point = None
        
        # 保存最后一个测试点
        if current_point:
            test_points.append({
                "title": current_point,
                "content": '\n'.join(point_content)
            })
        
        # 转换为Markdown格式
        markdown_content = ""
        for i, point in enumerate(test_points, 1):
            markdown_content += f"### 测试点 {i}: {point['title']}\n\n"
            if point['content']:
                markdown_content += f"{point['content']}\n\n"
            else:
                markdown_content += "\n"
        
        return markdown_content
    
    def _process_test_points(self, content: str) -> str:
        """
        处理Markdown内容中的测试点，确保格式一致
        
        Args:
            content: Markdown内容
            
        Returns:
            处理后的内容
        """
        # 这里可以添加更多的处理逻辑
        # 例如，确保所有测试点都有一致的标题格式
        
        return content

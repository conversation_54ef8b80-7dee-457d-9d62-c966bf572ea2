<global_work>
- 当前工作: 测试用例生成工作流
- 工作简介: 通过解析文本文件内容，进行需求分析、测试用例设计，最终生成测试用例Excel文件
- 工作规则:
    - 分析要尽可能详细
    - 分析原生文件时必须阅读全部代码行
    - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
    - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
    - 任务执行时，output内容必须遵照template模板格式
    - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
    - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
    - 每一步保存输出文档时，必须询问用户保存的文件名称，用户输入名称后再继续
- 任务列表:
    - [ ] 确认输入文档 (当前任务)
        任务说明: 确认用户要处理的输入文档
        执行角色: developer
        任务输出文档:
            - D:\0AI\TESTCASE\2025CASE\output\{{用户指定的文件名}}.json
                文档模板:
                ```
                {
                  "document_path": "{{文档路径}}",
                  "document_type": "{{文档类型}}",
                  "confirmed_by_user": true
                }
                ```
        任务规则:
            - 必须明确询问用户是否以当前打开的TXT文档作为输入
            - 必须获取用户确认后才能继续
            - 必须记录文档路径以供后续任务使用
            - 如果用户没有当前打开的文档，需引导用户提供文档路径
            - 保存文档前必须询问用户保存的文件名称，用户输入名称后再继续
            - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
            - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
    - [ ] 文本文件解析 (未开始)
    - [ ] 需求分析 (未开始)
    - [ ] 测试用例设计 (未开始)
    - [ ] 生成Excel文件 (未开始)

当前工作命令: 请询问用户是否以当前打开的TXT文档作为输入文档，如果是，请获取文档路径；
如果不是，请询问用户提供要处理的文档路径。
用户确认后，将文档路径保存到工作记忆中，然后执行 /work next 进入下一任务。

</global_work>
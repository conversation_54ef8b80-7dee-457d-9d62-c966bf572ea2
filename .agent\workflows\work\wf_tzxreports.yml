name: FastReport SQL 格式化工作流
description: 优化 FastReport 模板中的 SQL 查询语句格式，提高可读性

tasks:
  - name: SQL提取
    description: 从FastReport模板中提取原始SQL查询语句
    handler: ai_assistant
    steps:
      - get_date: 获取当前日期（MMDD格式）
      - init_dirs: 创建所需目录
      - prepare_file: 准备原始SQL文件

  - name: SQL输出格式优化
    description: 由AI助手根据规则优化SQL输出格式，提高可读性
    handler: ai_assistant
    steps:
      - read_sql: 读取原始SQL
      - optimize_sql: 应用输出格式优化规则，永不使用sql.py脚本
      - write_sql: 输出优化后的SQL
      - user_review: 每次输出都需要用户审查和确认

paths:
  input: input/MMDD/rawNNNN.md
  output_sql: output/sql/MMDD/sqlNNNN.md
  output_frx: output/frx/MMDD/frxNNNN.md

format_rules:
  select:
    - 简短字段放在同一行
    - CASE WHEN语句单独一行
    - 字段超过3个时分行
  
  where:
    - 使用WHERE 1 = 1作为起始
    - 后续条件用AND开头，简短字段条件放在同一行
    - 保持缩进对齐
  
  subquery:
    - 关键字单独一行
    - 简单条件可以内联
    - 复杂条件需要分行
    - 保持适当缩进
    - 每个子查询前后添加空行
    - 在子查询开始处添加注释：-- 子查询 #N begin
    - 在子查询结束处添加注释：-- 子查询 #N end
    - 子查询编号从外到内依次增加

  principles:
    - 保持原始SQL逻辑不变
    - 优先考虑代码可读性
    - 保持输出格式一致性
    - 使用注释标记子查询层级
    - 子查询之间保持空行分隔

commands:
  sql_format:
    trigger: /sql format
    description: AI助手根据规则优化SQL输出格式
    handler: ai_assistant
    action: optimize_sql
    
  sql_done:
    trigger: /sql done
    description: 确认完成并生成最终代码
    handler: ai_assistant
    action: complete_task

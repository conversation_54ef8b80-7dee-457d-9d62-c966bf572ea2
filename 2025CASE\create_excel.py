import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter

# 创建一个新的Excel工作簿
wb = openpyxl.Workbook()
ws = wb.active
ws.title = "测试用例"

# 定义标题行
headers = ["用例编号", "用例名称", "前置条件", "测试步骤", "预期结果", "测试数据", "优先级"]

# 设置标题行样式
header_font = Font(bold=True)
header_fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
header_alignment = Alignment(vertical="center", wrap_text=True)

# 写入标题行
for col_num, header in enumerate(headers, 1):
    cell = ws.cell(row=1, column=col_num, value=header)
    cell.font = header_font
    cell.fill = header_fill
    cell.alignment = header_alignment

# 定义测试用例数据
test_cases = [
    {
        "id": "TC-001",
        "name": "普通会员消费20元不满足任何满减条件",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为20元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（20元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统未应用任何满减活动（账单金额低于满减门槛）\n3. 最终应付金额为20元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 20元\n预期优惠金额: 0元\n预期应付金额: 20元",
        "priority": "高"
    },
    {
        "id": "TC-002",
        "name": "普通会员消费30元命中全部会员满减活动",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为30元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（30元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统应用活动4（满30减2，适用全部会员）\n3. 最终应付金额为28元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 30元\n预期优惠金额: 2元\n预期应付金额: 28元",
        "priority": "高"
    },
    {
        "id": "TC-003",
        "name": "普通会员消费50元命中普通会员满减活动",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为50元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（50元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统应用活动1（满50减5，适用普通会员），而非活动4（满30减2）\n3. 最终应付金额为45元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 50元\n预期优惠金额: 5元\n预期应付金额: 45元",
        "priority": "高"
    },
    {
        "id": "TC-004",
        "name": "VIP会员消费100元命中VIP会员满减活动",
        "preconditions": "1. 营销中心已配置满减活动：活动2（满100减15，适用VIP会员）、活动4（满30减2，适用全部会员）\n2. 会员2为VIP会员\n3. POS系统已完成点餐，账单金额为100元",
        "steps": "1. 登录POS系统\n2. 选择会员2（VIP会员）\n3. 查看账单金额（100元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员2为VIP会员\n2. 系统应用活动2（满100减15，适用VIP会员），而非活动4（满30减2）\n3. 最终应付金额为85元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员2\n会员等级: VIP会员\n账单金额: 100元\n预期优惠金额: 15元\n预期应付金额: 85元",
        "priority": "高"
    },
    {
        "id": "TC-005",
        "name": "钻石会员消费200元命中钻石会员满减活动",
        "preconditions": "1. 营销中心已配置满减活动：活动3（满200减40，适用钻石会员）、活动4（满30减2，适用全部会员）\n2. 会员3为钻石会员\n3. POS系统已完成点餐，账单金额为200元",
        "steps": "1. 登录POS系统\n2. 选择会员3（钻石会员）\n3. 查看账单金额（200元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员3为钻石会员\n2. 系统应用活动3（满200减40，适用钻石会员），而非活动4（满30减2）\n3. 最终应付金额为160元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员3\n会员等级: 钻石会员\n账单金额: 200元\n预期优惠金额: 40元\n预期应付金额: 160元",
        "priority": "高"
    },
    {
        "id": "TC-006",
        "name": "新入会普通会员消费50元命中普通会员满减活动",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）\n2. 会员4为新入会普通会员\n3. POS系统已完成点餐，账单金额为50元",
        "steps": "1. 登录POS系统\n2. 选择会员4（新入会普通会员）\n3. 查看账单金额（50元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员4为普通会员\n2. 系统应用活动1（满50减5，适用普通会员），而非活动4（满30减2）\n3. 最终应付金额为45元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员4\n会员等级: 普通会员（新入会）\n账单金额: 50元\n预期优惠金额: 5元\n预期应付金额: 45元",
        "priority": "高"
    },
    {
        "id": "TC-007",
        "name": "消费金额恰好等于满减门槛（边界值测试）",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为50.00元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（50.00元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统应用活动1（满50减5，适用普通会员）\n3. 最终应付金额为45.00元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 50.00元\n预期优惠金额: 5.00元\n预期应付金额: 45.00元",
        "priority": "中"
    },
    {
        "id": "TC-008",
        "name": "消费金额略低于满减门槛（边界值测试）",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为49.99元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（49.99元）\n4. 观察系统是否应用满减活动\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统未应用活动1（满50减5，适用普通会员），但应用活动4（满30减2，适用全部会员）\n3. 最终应付金额为47.99元\n4. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 49.99元\n预期优惠金额: 2.00元\n预期应付金额: 47.99元",
        "priority": "中"
    },
    {
        "id": "TC-009",
        "name": "会员等级识别失败（异常场景测试）",
        "preconditions": "1. 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）\n2. 会员系统暂时无法访问或会员信息不完整\n3. POS系统已完成点餐，账单金额为50元",
        "steps": "1. 登录POS系统\n2. 尝试选择会员（但会员系统无法正常响应）\n3. 查看账单金额（50元）\n4. 观察系统如何处理会员等级识别失败的情况\n5. 完成支付流程",
        "expected_results": "1. 系统提示会员信息获取失败\n2. 系统默认将会员视为普通会员\n3. 系统应用活动1（满50减5，适用普通会员）\n4. 最终应付金额为45元\n5. 支付流程正常完成",
        "test_data": "会员ID: 未知或不完整\n默认会员等级: 普通会员\n账单金额: 50元\n预期优惠金额: 5元\n预期应付金额: 45元",
        "priority": "中"
    },
    {
        "id": "TC-010",
        "name": "满减活动配置错误（异常场景测试）",
        "preconditions": "1. 营销中心满减活动配置错误或不完整\n2. 会员1为普通会员\n3. POS系统已完成点餐，账单金额为50元",
        "steps": "1. 登录POS系统\n2. 选择会员1（普通会员）\n3. 查看账单金额（50元）\n4. 观察系统如何处理满减活动配置错误的情况\n5. 完成支付流程",
        "expected_results": "1. 系统正确识别会员1为普通会员\n2. 系统提示营销活动异常或无法应用满减\n3. 不应用任何满减优惠\n4. 最终应付金额为50元\n5. 支付流程正常完成",
        "test_data": "会员ID: 会员1\n会员等级: 普通会员\n账单金额: 50元\n预期优惠金额: 0元\n预期应付金额: 50元",
        "priority": "中"
    }
]

# 写入测试用例数据
cell_alignment = Alignment(vertical="center", wrap_text=True)

for row_num, test_case in enumerate(test_cases, 2):  # 从第2行开始（第1行是标题）
    ws.cell(row=row_num, column=1, value=test_case["id"]).alignment = cell_alignment
    ws.cell(row=row_num, column=2, value=test_case["name"]).alignment = cell_alignment
    ws.cell(row=row_num, column=3, value=test_case["preconditions"]).alignment = cell_alignment
    ws.cell(row=row_num, column=4, value=test_case["steps"]).alignment = cell_alignment
    ws.cell(row=row_num, column=5, value=test_case["expected_results"]).alignment = cell_alignment
    ws.cell(row=row_num, column=6, value=test_case["test_data"]).alignment = cell_alignment
    ws.cell(row=row_num, column=7, value=test_case["priority"]).alignment = cell_alignment

# 自动调整列宽
for col in range(1, len(headers) + 1):
    column_letter = get_column_letter(col)
    # 设置一个最小宽度
    ws.column_dimensions[column_letter].width = 15
    
    # 根据内容调整列宽，但设置最大宽度限制
    max_length = 0
    for row in range(1, len(test_cases) + 2):  # +2是因为有标题行和从1开始计数
        cell_value = ws.cell(row=row, column=col).value
        if cell_value:
            # 计算每行的最大长度
            lines = str(cell_value).split('\n')
            for line in lines:
                if len(line) > max_length:
                    max_length = len(line)
    
    # 设置列宽，但限制最大宽度
    adjusted_width = min(max_length + 2, 50)  # +2为了有一些边距，最大宽度为50
    ws.column_dimensions[column_letter].width = adjusted_width

# 保存Excel文件
output_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\会员等级满减POS应用.xlsx"
wb.save(output_path)
print(f"Excel文件已保存到: {output_path}")

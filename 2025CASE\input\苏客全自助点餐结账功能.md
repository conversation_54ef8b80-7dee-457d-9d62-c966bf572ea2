苏客全自助点餐结账功能
1.功能简介：将餐盘放置到AI识别区，自动识别菜品，跳转扫码支付页面，扫码自动支付；
2.功能按钮位置：收银页面中间区域，【全自助】按钮；
3.前置条件：已登录POS系统，菜品已设置AI图片；
4.功能测试点
1）未开店，【全自助】不允许操作加菜
2）未开班，【全自助】不允许操作加菜
3）点【全自助】进入全自助点餐结账流程，餐盘放置到可识别区域，自动识别出菜品，菜品上方显示菜品名称及序号，稳定后自动跳转至结账页面，声音播放‘请扫码支付’，顾客扫付款码自动结账
4）无法识别的菜品上方显示[修正]字样，进入菜品信息页面，此时的修正是手动将此次识别的菜品图片绑定给选定菜品
5）已识别的菜品不允许进行修正
6）菜品自动识别时，核查左侧对应的应收金额、优惠金额、菜品数量和待付金额是否正确
7）菜品正在识别中，如已确定识别成功，但还未自动进入付款页面，可手动点【扫码支付】按钮进入扫码付款页面
8）菜品正在识别中且未进入付款页面时，可点【继续识别】重新识别菜品
9）菜品正在识别中且未进入付款页面时，可点【清空商品】，清空后开始重新识别菜品
10）菜品正在识别中且未进入付款页面时，点右上角X，弹出员工登录页面，输入密码确定后退出全自助模式，取消则退出员工登录页面，继续识别菜品
11）扫码支付成功后，查看流水中核查账单付款方式和金额是否正确
12）扫码付款页面，扫错误的付款码有提示信息不允许支付
import os
import logging
from pathlib import Path
import sys
import re
from dotenv import load_dotenv

def get_absolute_path(path: str, project_root: str) -> str:
    """将相对路径转换为绝对路径"""
    if not path:
        return ''
    if os.path.isabs(path):
        return path
    return os.path.join(project_root, path)

# 全局路径变量
PROJECT_ROOT = str(Path(__file__).parent.parent.parent)
ENV_PATH = os.path.join(PROJECT_ROOT, '.agent', '.env')

class RoleManager:
    def __init__(self):
        # 加载环境变量
        print(f"Loading .env from: {ENV_PATH}")
        load_dotenv(ENV_PATH)

        # 获取LOCAL_WORKSPACE路径
        workspace = get_absolute_path(os.getenv('LOCAL_WORKSPACE', ''), PROJECT_ROOT)
        self.workspace = Path(workspace) if workspace else None

        # 设置角色文件搜索路径
        self.local_roles_dir = self.workspace / 'roles' if self.workspace else None
        self.agent_roles_dir = Path(PROJECT_ROOT) / '.agent' / 'roles'

        print(f"Local roles directory: {self.local_roles_dir}")
        print(f"Agent roles directory: {self.agent_roles_dir}")

        # 从环境变量获取规则路径
        rules_path = get_absolute_path(os.getenv('LOCAL_RULES_PATH', ''), PROJECT_ROOT)
        self.rules_path = Path(rules_path) if rules_path else None

    def switch_role(self, role_name: str) -> tuple[bool, str]:
        """切换到指定角色"""
        try:
            # 1. 读取角色文件
            role_file = None
            role_content = ""

            # 优先从LOCAL_WORKSPACE/roles目录下查找
            if self.local_roles_dir and (self.local_roles_dir / f"{role_name}.md").exists():
                role_file = self.local_roles_dir / f"{role_name}.md"
            # 如果没找到，则从.agent/roles目录下查找
            elif (self.agent_roles_dir / f"{role_name}.md").exists():
                role_file = self.agent_roles_dir / f"{role_name}.md"
            else:
                return False, ""

            with open(role_file, 'r', encoding='utf-8', newline='') as f:
                role_content = f.read()

            # 2. 更新全局规则文件
            if not self.rules_path.exists():
                self.rules_path.parent.mkdir(parents=True, exist_ok=True)
                with open(self.rules_path, 'w', encoding='utf-8', newline='') as f:
                    f.write('<current_Role></current_Role>\n')

            with open(self.rules_path, 'r', encoding='utf-8', newline='') as f:
                content = f.read()

            role_section = f'<current_Role>\n{role_content}\n</current_Role>'

            # 使用正则表达式精确匹配和替换角色内容
            pattern = r'<current_Role>.*?</current_Role>'
            if re.search(pattern, content, re.DOTALL):
                content = re.sub(pattern, role_section, content, flags=re.DOTALL)
            else:
                # 如果没有找到标签，则在文件开头添加
                content = role_section + '\n' + content

            # 清理格式：移除多余的换行和 > 符号
            content = re.sub(r'</current_Role>\s*>\s*', '</current_Role>\n', content)
            # 确保只有一个换行符
            content = re.sub(r'\n+', '\n', content)

            with open(self.rules_path, 'w', encoding='utf-8', newline='') as f:
                f.write(content)

            return True, role_content

        except Exception as e:
            return False, ""

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 role.py <role_name>")
        sys.exit(1)

    # 在执行任何命令前先执行清屏命令
    os.system('cls' if os.name == 'nt' else 'clear')

    role_name = sys.argv[1]
    role_manager = RoleManager()
    success, role_content = role_manager.switch_role(role_name)
    if success:
        print(f"你的新角色定义是:\n{role_content}\n")
        print(f"system:角色切换成功，请严格遵循角色的工作职责、规则、思维链、工作流程等设定，请使用新的角色定义继续工作")
    else:
        print(f"system:角色切换失败了，你的角色并没有变化~")

if __name__ == "__main__":
    main()

id: xxx
name: 工作流名称
description: 工作流描述
# 工作流的规则
rules:
  # 这些规则是固定的，不可遗漏，其他的可灵活增加
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 生成的所有文档如果没有指定明确路径，则统一放在 {{work_path}} 目录下
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务

# 工作流的任务列表
tasks:
  # 工作任务
  - name: 整理模块各页面UI、交互、业务逻辑、API文档
    # 工作任务内容描述
    description: 分析安卓原生代码的Control、Model、API、Layout、Dialog、Fragment 代码文件，结合截图，生成各页面的UI元素结构树、交互关系、业务逻辑、API文档
    # 执行工作的AI角色
    worker: developer
    # 工作任务规则
    rules:
      - 请使用cat阅读相关文件
      - 先阅读android_source_code.md 获取文件路径，再使用cat查看文件
      - 如果有多个页面、弹窗，要一个一个的进行分析
      - 为了更精准的识别、防止遗漏页面元素，需要向用户索要页面截图
      - 如果包含组件，对组件也要进行分析，拆解整理出里面的元素
      - api的入参和返回的数据格式、类型必须与安卓原生代码保持一致，不可更改
    # 工作任务的前置知识，一般为文档
    input:
      - doc: android_source_code.md
    # 工作任务的输出内容和格式要求
    output:
      - doc: features.md
        template: |
          {{输出内容、markdown格式}}
    status: todo
  - name: 规划开发任务
    description: 整理需要实现的页面、组件
    # 当前任务的特殊指令，用于指引AI进行特定操作，会优先提醒AI
    prompt: |
      请罗列出所有需要实现的页面、公共组件，用户审核通过后，调用 python3 work.py/ update  {{JSON}} 来更新当前工作计划
      JSON:{
        objects:[
          {name:'菜品管理页面',worker:'developer'},
          {name:'添加菜品页面',worker:'developer'}
        ]
      }
    steps:
      - name: 调用 /task use feature 指令，实现页面
      - name: 用户验收确认
    rules:
      - 列出页面、组件列表，一个一个的实现
      - 必须符合架构设计和项目规范
    input:
      - doc: convert_design.md
      - doc: features.md
    status: todo

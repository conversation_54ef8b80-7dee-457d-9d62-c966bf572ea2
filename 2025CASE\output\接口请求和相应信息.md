## 接口请求报文信息
**J****son请求示例（如示例或截图）**
**Headers****需填写**Content-Type：application/json;charset=UTF-8（设置后返回的错误信息不会乱码）
**B****od****y****请求参数示例：**
{"REQUEST": {"REQUEST_DATA": {"cashierId":"20170zinl227n0101","checkCode":"p88888888","mall":"","orderId":"9028298012760","payList":[{"discountAmt":0,"payAmt":63,"paymentMethod":"CH","time":"20220322174131","value":63}],"store":"Y1GB0122N03","tillId":"01","time":"20220322174130","totalAmt":63,"type":"SALE"},"HRT_ATTRS": {"Partner_ID": "70000029","Api_Version": "1.0.1","App_Sub_ID": "10000187223ZZ","Format": "json","Time_Stamp": "2024-02-06 10:32:41:446","Api_ID": "mixc.imPOSWBJB.GLWXCJB.orderCollect","App_Token": "5d93e6da4a914176aefe512ae3b52ecf","App_Pub_ID": "10000187223RL","Sign_Method": "md5","Sign": "7F65174D5829D7EFA0976B1A715E5A99","Sys_ID": "100001872"}}}

## 3.3接口响应报文信息

**响应报文****(****示例****):**
{
    "RETURN_DATA": {
        "header": {
            "errcode": "0000",
            "errmsg": "成功"
        },
        "body": {
            "orderId": "1103600458211214010001",
            "refOrderId": "HSS0351001100120041010007"
        }
    },
    "RETURN_CODE": "INTERFACE HAS NO CODE",
    "RETURN_DESC": "INTERFACE HAS NO DESC",
    "RETURN_STAMP": ""
}

**errcode****说明****:**
**0000****：成功**
**1**00**1****：失败，不同错误“e****rrmsg****”中提示不同错误信息**

| 请求参数 | 参数名 | 参数名 | 参数名 | 类型 | 是否必填 | 说明 |
| --- | --- | --- | --- | --- | --- | --- |
| 收银员编号 | cashierId | cashierId | cashierId | String | Y | 必填项 |
| 商品数组 | itemList | itemList | itemList | String | Y | 必填项；
填写内容："itemList": [ ] |
|  | 商品编号 | itemCode | itemCode | String | N | 非必填； |
|  | 商品价格 | price | price | number | N | 非必填；如果填写了商品编号itemCode，则商品价格必填 |
|  | 商品数量 | quantity | quantity | number | N | 非必填；如果填写了商品编号itemCode，则商品数量必填 |
| 商场编号 | mall | mall | mall | String | Y | 必填项
长度：最小1位，最大40位 |
| 订单号 | orderId | orderId | orderId | String | Y | 必填项
长度：最小6位，最大40位 |
| 支付行 | payList | payList | payList | String | Y | 必填项；填写格式
"payList":[{ “参数名”:”值”}], |
|  | 支付方式 | 支付方式 | paymentMethod | String | Y | 必填项
根据ERP的支付方式。例如现金是CH；支付宝是AP；微信是WP；银行卡是CI；其它是OT等等 |
|  | 支付时间 | 支付时间 | time | String | Y | 必填项
格式：
yyyyMMddHHmmss |
|  | 支付金额（实收金额） | 支付金额（实收金额） | value | Number | Y | 必填项
正数：销售订单
负数：退货订单 |
|  | 应收金额 | 应收金额 | payAmt | Number | Y | 必填项
正数：销售订单
负数：退货订单 |
|  | 优惠金额 | 优惠金额 | discountAmt | Number | Y | 必填项
正数：销售订单
负数：退货订单 |
|  | 支付银行 | 支付银行 | cardBank | String | N | 非必填 |
|  | 银行卡卡号 | 银行卡卡号 | cardNumber | String | N | 非必填 |
| 店铺编号 | store | store | store | String | Y | 必填项
长度：最小1位,最大40位 |
| 收银机编号 | tillId | tillId | tillId | String | Y | 必填项
长度必须为2位数字，例如：01 |
| 订单时间 | time | time | time | String | Y | 必填项
格式：yyyyMMddHHmmss；时区：GMT+8
（此接口仅支持接收48天内的订单） |
| 订单总金额 | totalAmt | totalAmt | totalAmt | String | Y | 必填项
正数：销售订单
负数：退货订单 |
| 订单类型 | type | type | type | String | Y | 必填项
销售：SALE
退货：ONLINEREFUND |
| 店铺验证密钥(登录密码) | checkCode | checkCode | checkCode | String | Y | 必填项
店铺验证密钥(密码) 由IMPOS提供 |
| 备注 | comments | comments | comments | String | N | 非必填；长度：最大200位 |
| 会员手机号 | mobile | mobile | mobile | String | N | 非必填 |
| 关联原订单号 | refOrderId | refOrderId | refOrderId | String | N | 非必填；长度：最小6位，最大40位；
若是退货订单可以填写原订单号 |
| 来源 | source | source | source | String | N | 01:店铺
02:数据采集盒子 |


| 请求参数 | 参数名 | 类型 | 是否必填 | 说明 |
| --- | --- | --- | --- | --- |
| 订单号 | orderId | String | N |  |
| 参考号 | refOrderId | String | N |  |

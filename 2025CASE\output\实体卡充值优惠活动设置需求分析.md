# 需求分析报告

## 功能概述
实体卡充值优惠活动设置功能是储值管理系统的核心模块，用于配置和管理实体卡的充值优惠规则。该功能支持灵活的时间设置（每周/每月固定日期）、多样的卡分类选择、充值金额和赠送金额配置，以及完整的规则生命周期管理（新增、修改、删除、查看、审核、分配机构）。

## 功能点分析

### 实体卡充值优惠活动设置
- **功能描述**：配置充值优惠规则的核心参数，包括时间、卡分类、充值金额、赠送金额等
- **业务价值**：提高客户充值积极性，增加资金回流，提升客户粘性
- **优先级**：高
- **依赖关系**：依赖卡分类基础数据、门店信息、用户权限管理
- **实现复杂度**：中等

#### 正常流程
1. 用户进入储值管理-充值优惠设置页面
2. 选择优惠日期类型（每周固定日期或每月固定日期）
3. 根据选择的类型设置具体日期
4. 选择适用的卡分类
5. 设置充值金额和赠送金额
6. 设置活动有效期（开始日期-结束日期）
7. 设置活动时间段
8. 选择适用门店
9. 保存规则并提交审核

#### 异常流程
- 未设置卡分类时无法创建规则
- 充值金额或赠送金额设置不合理时系统提示
- 日期时间设置冲突时系统提示
- 权限不足时操作被拒绝
- 网络异常时操作失败
- 未选择门店时无法保存

#### 边界条件
- 每月固定日期限制在1-28日
- 时间段不能重叠
- 开始日期不能晚于结束日期
- 优惠日期类型只能选择一种
- 充值金额必须大于0
- 赠送金额必须大于0且不超过充值金额
- 必须至少选择一个门店

### 充值优惠规则管理
- **功能描述**：对充值优惠规则进行全生命周期管理
- **业务价值**：确保规则的准确性和时效性，支持业务灵活调整
- **优先级**：高
- **依赖关系**：依赖用户权限管理、审核流程
- **实现复杂度**：高

#### 正常流程
- **新增**：创建新规则 → 填写信息 → 保存 → 提交审核
- **修改**：选择规则 → 编辑信息 → 保存 → 重新审核
- **删除**：选择规则 → 确认删除 → 删除成功
- **查看**：选择规则 → 查看详情
- **审核**：审核人员查看 → 弹出确认框 → 确认审核 → 规则生效
- **分配机构**：选择规则 → 选择门店 → 分配成功

#### 异常流程
- 已生效规则无法删除和修改
- 无权限用户无法执行操作
- 审核中的规则无法修改
- 门店分配失败时回滚
- 规则冲突时按最优规则命中

#### 边界条件
- 规则状态限制操作权限
- 用户角色限制功能访问
- 审核后的规则无法修改或删除
- 门店分配必须明确指定，不支持继承

## 测试策略
- **测试范围**：功能测试、界面测试、权限测试、数据验证测试、集成测试、规则冲突测试
- **测试优先级**：
  - P0：核心业务流程（新增、修改、审核）、充值赠送金额计算
  - P1：数据验证和边界条件、规则冲突处理
  - P2：权限控制和异常处理、门店分配
  - P3：界面交互和用户体验、历史记录查询
- **测试环境要求**：
  - 完整的储值管理系统环境
  - 预置的卡分类数据
  - 多个门店的测试数据
  - 不同权限级别的测试账号
  - 多规则冲突的测试场景

## 风险分析

### 数据一致性风险
- **描述**：多时段设置可能导致时间重叠，充值赠送金额计算错误
- **影响**：充值优惠计算错误，影响客户体验和财务准确性
- **缓解措施**：增强前端验证，后端二次校验，金额计算逻辑测试

### 权限控制风险
- **描述**：权限控制不当可能导致越权操作
- **影响**：数据安全问题，业务流程混乱
- **缓解措施**：严格的权限验证，操作日志记录

### 审核流程风险
- **描述**：单级审核可能导致错误规则快速生效
- **影响**：业务损失，客户投诉
- **缓解措施**：审核确认框明确提示，审核记录追溯

### 规则冲突风险
- **描述**：多个规则同时生效时最优规则算法可能有误
- **影响**：客户获得的优惠不符合预期
- **缓解措施**：最优规则算法测试，规则冲突场景验证

### 门店分配风险
- **描述**：门店分配错误可能导致规则在错误门店生效
- **影响**：业务混乱，门店间不公平
- **缓解措施**：门店选择验证，分配结果确认

## 澄清问题解答

1. **优惠规则内容**：充值金额、赠送金额
2. **审核流程**：单级审核，弹出确认框确认即可
3. **规则冲突处理**：按最优规则命中（优先选择赠送金额最高的规则）
4. **机构分配**：无继承关系，直接分配到门店级别，支持多门店
5. **规则停用**：无停用功能，审核后即生效
6. **历史记录**：保留可查询即可

## 补充业务规则

### BR013 - 优惠内容规则
- **描述**：优惠规则包含充值金额和赠送金额两个核心要素
- **适用范围**：充值优惠规则设置
- **约束条件**：充值金额和赠送金额必须为正数，赠送金额不能超过充值金额

### BR014 - 审核确认规则
- **描述**：审核采用单级审核，通过弹出确认框进行确认
- **适用范围**：规则审核流程
- **约束条件**：审核确认后规则立即生效，无法撤销

### BR015 - 规则冲突处理规则
- **描述**：当多个规则同时满足条件时，按最优规则命中
- **适用范围**：规则执行时
- **约束条件**：系统自动计算最优规则，优先选择赠送金额最高的规则

### BR016 - 机构分配规则
- **描述**：规则分配直接到门店级别，无继承关系，支持多门店分配
- **适用范围**：规则分配功能
- **约束条件**：必须明确指定门店，不支持上级机构自动下发

### BR017 - 规则生效规则
- **描述**：规则审核通过后立即生效，无停用功能
- **适用范围**：规则生命周期管理
- **约束条件**：规则一旦生效无法停用，只能通过设置结束日期来终止

### BR018 - 历史记录规则
- **描述**：历史规则记录保留并支持查询
- **适用范围**：规则历史管理
- **约束条件**：保留所有历史操作记录，支持按时间、操作人等条件查询

# 测试点知识库管理指南

## 知识库概述
测试点知识库用于存储和管理测试点文档，支持TXT和MD格式的文档导入，并提供全文检索功能。

## 知识库结构
- 知识库根目录: `_agent-local/knowledge/test_points`
- 文档存储目录: `_agent-local/knowledge/test_points/documents`
- 索引目录: `_agent-local/knowledge/test_points/index`
- 元数据文件: `_agent-local/knowledge/test_points/metadata.json`

## 知识库管理

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 导入单个文档
doc_id = kb.add_document("path/to/document.txt", category="功能测试")

# 批量导入文档
doc_ids = kb.add_documents(["path1.txt", "path2.md"], category="接口测试")
```

### 更新文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 更新文档内容
kb.update_document("pos_medal_discount_20240501", "path/to/updated_document.txt")

# 更新文档元数据
kb.update_document_metadata("pos_medal_discount_20240501", category="重要功能测试")
```

### 删除文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 删除单个文档
kb.delete_document("pos_medal_discount_20240501")

# 批量删除文档
kb.delete_documents(["doc_id_1", "doc_id_2"])
```

### 知识库备份
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 创建备份
backup_path = kb.create_backup("D:/backups/test_points_backup")
print(f"备份已创建: {backup_path}")
```

### 知识库恢复
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 从备份恢复
kb.restore_from_backup("D:/backups/test_points_backup_20240501.zip")
```

### 知识库统计
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 获取统计信息
stats = kb.get_statistics()
print(f"文档总数: {stats['document_count']}")
print(f"分类统计: {stats['categories']}")
print(f"最近添加: {stats['recent_documents']}")
```

## 最佳实践

### 文档组织
1. **使用一致的分类体系**：为文档设置合适的分类，如"功能测试"、"接口测试"、"性能测试"等
2. **文档命名规范**：使用清晰、描述性的文件名，如"营销中心勋章满减POS应用.txt"
3. **定期整理**：定期检查和整理知识库，删除过时或重复的文档

### 检索技巧
1. **使用关键词**：使用具体的关键词进行检索，如"勋章满减"而不是"满减"
2. **组合检索**：结合分类和关键词进行检索，提高精确度
3. **查看相关度**：关注检索结果的相关度分数，通常分数高于0.7的结果较为相关

### 知识库维护
1. **定期备份**：每周或每月创建知识库备份
2. **索引重建**：如果检索性能下降，考虑重建索引
3. **文档更新**：当测试点有变更时，及时更新对应文档

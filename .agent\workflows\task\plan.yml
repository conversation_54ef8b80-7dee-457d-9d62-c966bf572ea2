name: "任务规划"
description: "基于用户需求进行实现步骤、任务的拆解规划"

todos:
  - title: "通过设计AI执行的任务流来实现用户需求"
    description: "基于用户需求进行实现步骤、任务的拆解"

steps:
  - title: "需求理解与确认"
    worker: planner
    rule:
      - "基于用户的诉求，充分沟通，与用户一起头脑风暴，梳理AI任务流的实现思路"
      - "AI任务流是将复杂的工作拆分成多个步骤，由AI按步骤完成复杂的工作目标"
      - "精确复述用户的问题/需求"
      - "提出关键澄清问题"
      - "明确询问用户理解是否正确"
      - "等待用户确认或修正"
      - "需要记录关键信息到任务记忆中"
    input:
      - doc: 用户需求文档路径
  - title: "任务拆解与流程设计、确认"
    worker: planner
    rule:
      - "使用中文设计流程"
      - "填充任务名称与描述"
      - "定义todos列表（任务目标）"
      - "设计有序步骤(steps)序列"
      - "每个步骤必须包含：title、rule、input(可选)、output、worker(可选)"
      - "input: 必要的输入文档(可选，如果有的话)"
      - "output: 分析、梳理、设计类步骤的输出内容模板，用于让AI按结构梳理思路、整理信息"
      - "worker: 负责执行的角色(可选)"
      - "确保步骤之间的逻辑关系清晰"
      - "为分析、梳理、设计类步骤设计输出模板(output)"
      - "等待用户确认或修正"
    input:
      - doc: .agent/workflows/task/template.yml.tpl
    output:
      - 任务流模板: |
        name: 任务名称
        description: 任务简要描述
        current_todo_index: 0
        current_step_index: 0
        todos:
        - title: 任务目标
          description: 详细描述任务的内容与要求

        steps:
          - title: "第一步骤名称"
            worker: architect  # 可选，指定负责此步骤的角色
            rule:
              - "执行规则1"
              - "执行规则2"
              - "执行规则3"
            input:
              - doc: 输入文档1路径
              - doc: 输入文档2路径
            output:
              - doc: 输出文档路径
                请按需遵循以下格式输出: |
                  # {{功能名称}}架构设计

                  ## 方案概述
                  {{设计方案概述}}

                  ## 技术选型
                  {{所选技术及理由}}

                  ## 数据流设计
                  {{数据流图或描述}}

                  ## 接口设计
                  {{API接口设计}}

          - title: "第二步骤名称"
            rule:
              - "执行规则1"
              - "执行规则2"
            input:
              - doc: 上一步骤的输出文档路径
            output:
              - |
                输出内容模板...


  - title: "创建任务流文件并启动任务"
    worker: developer
    rule:
      - "将流程文件写入到 _agent-local/workspace/task.yml"
      - "确保文件格式正确"
      - "覆盖后，使用/task now 指令开始执行任务"
    output:
      - doc: _agent-local/workspace/task.yml
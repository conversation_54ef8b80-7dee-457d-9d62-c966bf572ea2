#!/usr/bin/env python3
import sys
import os
import yaml
import requests
from pathlib import Path
from typing import List, Dict, Optional, Union, Any
from dotenv import load_dotenv
import json
import re
# 修改导入语句，将相对导入改为绝对导入
from record import get_user_info, report_action

# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

# 加载环境变量
env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
# print(f"Loading .env from: {env_path}")
load_dotenv(env_path)

def get_absolute_path(path: str, base_dir: str) -> str:
    """将路径转换为绝对路径
    如果是绝对路径直接返回，否则基于base_dir转换为绝对路径
    """
    if not path:
        return path
    return path if os.path.isabs(path) else os.path.join(base_dir, path)

# 获取环境变量并根据需要转换为绝对路径
workspace = get_absolute_path(os.getenv('LOCAL_WORKSPACE'), project_root)
rules_path = get_absolute_path(os.getenv('LOCAL_RULES_PATH'), project_root)

# 任务流目录路径
LOCAL_TASK_DIR = os.path.join(workspace, 'workflows', 'task')
AGENT_TASK_DIR = os.path.join(project_root, '.agent', 'workflows', 'task')

# 任务文件路径
task_file = os.path.join(workspace, 'workspace', 'task.yml')

# 字段名称映射
FIELD_NAMES = {
    'title': '标题',
    'rule': '步骤规则',  # 统一使用"工作规则"而不是"当前工作规则"
    'output': '输出要求',
    'input': '前置知识'
}

def load_task_data() -> dict:
    """加载任务数据"""
    if not os.path.exists(task_file):
        return {'todos': [], 'current_todo_index': None, 'current_step_index': None, 'steps': [], 'memory': {}}

    with open(task_file, 'r', encoding='utf-8', newline='') as f:
        data = yaml.safe_load(f)
        # 确保返回的数据包含 memory 字段
        if not data:
            data = {'todos': [], 'current_todo_index': None, 'current_step_index': None, 'steps': [], 'memory': {}}
        elif 'memory' not in data or data['memory'] is None:
            data['memory'] = {}
        return data

def save_task_data(data: dict):
    """保存任务数据"""
    # 确保目录存在
    os.makedirs(os.path.dirname(task_file), exist_ok=True)
    with open(task_file, 'w', encoding='utf-8', newline='') as f:
        yaml.dump(data, f, allow_unicode=True, default_flow_style=False, sort_keys=False)

def get_step_status(index: int, current_index: int) -> tuple:
    """获取步骤状态"""
    if index < current_index:
        return "[x]", "(已完成)"
    elif index == current_index:
        return "[*]", "(当前步骤)"
    else:
        return "[ ]", "(未开始)"

def format_step_line(step: dict, index: int, status: tuple) -> str:
    """格式化步骤行"""
    checkbox, state = status
    return f"  - {checkbox} {index + 1}. {step['title']} {state}"

def format_multiline_text(text: str, base_indent: str, extra_indent: str = "") -> List[str]:
    """格式化多行文本，保持原有的缩进结构

    Args:
        text: 要格式化的文本
        base_indent: 基础缩进(用于所有行)
        extra_indent: 额外缩进(用于第一行之后的行)

    Returns:
        格式化后的文本行列表
    """
    result = []
    lines = text.splitlines()

    # 找出最小缩进
    min_indent = None
    for line in lines:
        if line.strip():  # 忽略空行
            current_indent = len(line) - len(line.lstrip())
            if min_indent is None or current_indent < min_indent:
                min_indent = current_indent

    if min_indent is None:
        min_indent = 0

    # 处理每一行
    for i, line in enumerate(lines):
        if not line.strip():  # 空行不添加额外缩进
            if i == 0:
                result.append(base_indent)  # 第一行空行只添加基础缩进
            else:
                result.append(base_indent + extra_indent)  # 后续空行添加基础缩进和额外缩进
            continue

        # 计算相对缩进
        current_indent = len(line) - len(line.lstrip())
        relative_indent = " " * (current_indent - min_indent) if current_indent > min_indent else ""

        # 第一行使用基础缩进
        if i == 0:
            result.append(f"{base_indent}{relative_indent}{line.lstrip()}")
        else:
            # 后续行添加额外缩进
            result.append(f"{base_indent}{extra_indent}{relative_indent}{line.lstrip()}")

    return result

def format_step_details(details: Union[str, List[str], Dict[str, Any]], base_indent: str = "", skip_title: bool = True) -> List[str]:
    """格式化步骤详情

    Args:
        details: 步骤详情,可以是字符串、列表或字典
        base_indent: 基础缩进
        skip_title: 是否跳过标题字段的显示

    Returns:
        格式化后的文本行列表
    """
    result = []
    max_line_length = 100  # 最大行长度
    STEP_BASE_INDENT = "  "  # 步骤内容的基础缩进量
    LIST_ITEM_INDENT = "  "   # 列表项的额外缩进量

    def wrap_long_line(line: str, indent: str, subsequent_indent: str = None) -> List[str]:
        """处理长行,在适当位置换行"""
        if subsequent_indent is None:
            subsequent_indent = indent

        if len(indent + line) <= max_line_length:
            return [indent + line]

        words = line.split()
        lines = []
        current_line = indent

        for word in words:
            if len(current_line) + len(word) + 1 <= max_line_length:
                if current_line == indent:
                    current_line += word
                else:
                    current_line += ' ' + word
            else:
                if current_line != indent:
                    lines.append(current_line)
                current_line = subsequent_indent + word

        if current_line and current_line != indent:
            lines.append(current_line)

        return lines if lines else [indent]

    def process_multiline_text(text: str, current_indent: str, is_list_item: bool = False) -> List[str]:
        """处理多行文本，保持正确的缩进结构"""
        lines = text.splitlines()
        result = []
        section_title_pattern = r'^\[.*\]$'  # 匹配 [xxx] 格式的标题
        base_indent = current_indent + LIST_ITEM_INDENT  # 基础缩进
        max_line_length = 100  # 最大行长度
        in_code_block = False
        code_block_indent = base_indent + "    "  # 代码块的额外缩进
        code_block_content_indent = code_block_indent + "    "  # 代码块内容的额外缩进

        def wrap_text(text: str, indent: str) -> List[str]:
            """处理长文本，在适当位置换行"""
            if len(indent + text) <= max_line_length:
                return [indent + text]

            # 如果文本包含逗号，优先在逗号处换行
            if ',' in text:
                parts = text.split(',')
                lines = []
                current_line = indent
                for i, part in enumerate(parts):
                    part = part.strip()
                    if i < len(parts) - 1:
                        part += ','
                    if len(current_line) + len(part) + 1 <= max_line_length:
                        if current_line == indent:
                            current_line += part
                        else:
                            current_line += ' ' + part
                    else:
                        if current_line != indent:
                            lines.append(current_line)
                        current_line = indent + part
                if current_line != indent:
                    lines.append(current_line)
                return lines

            # 如果文本包含句号，尝试在句号处换行
            if '。' in text:
                parts = text.split('。')
                lines = []
                current_line = indent
                for i, part in enumerate(parts):
                    part = part.strip()
                    if i < len(parts) - 1:
                        part += '。'
                    if len(current_line) + len(part) + 1 <= max_line_length:
                        if current_line == indent:
                            current_line += part
                        else:
                            current_line += ' ' + part
                    else:
                        if current_line != indent:
                            lines.append(current_line)
                        current_line = indent + part
                if current_line != indent:
                    lines.append(current_line)
                return lines

            # 否则按单词换行
            words = text.split()
            lines = []
            current_line = indent

            for word in words:
                if len(current_line) + len(word) + 1 <= max_line_length:
                    if current_line == indent:
                        current_line += word
                    else:
                        current_line += ' ' + word
                else:
                    if current_line != indent:
                        lines.append(current_line)
                    current_line = indent + word

            if current_line != indent:
                lines.append(current_line)

            return lines

        for i, line in enumerate(lines):
            if not line.strip():
                result.append("")
                continue

            stripped_line = line.lstrip()

            # 处理代码块标记
            if stripped_line.startswith('```'):
                in_code_block = not in_code_block
                result.append(f"{code_block_indent}{stripped_line}")
                continue

            # 处理节标题 [xxx]
            if re.match(section_title_pattern, stripped_line):
                result.append(f"{current_indent}{stripped_line}")
                continue

            # 处理代码块内容
            if in_code_block:
                # 注释行使用代码块缩进
                if stripped_line.strip().startswith('//'):
                    result.append(f"{code_block_indent}{stripped_line}")
                # 代码行使用额外缩进
                else:
                    result.append(f"{code_block_content_indent}{stripped_line}")
                continue

            # 检查是否为列表项
            if stripped_line.startswith('- '):
                # 列表项的处理
                item_content = stripped_line[2:].strip()  # 移除 "- " 前缀
                # 所有列表项使用相同的基础缩进
                result.extend(wrap_text(f"- {item_content}", base_indent))
            else:
                # 非列表项的处理，使用相同的基础缩进
                result.extend(wrap_text(stripped_line, base_indent))

        return result

    if isinstance(details, str):
        # 处理多行字符串
        if '```' in details:  # 处理代码块
            in_code_block = False
            code_indent = base_indent + STEP_BASE_INDENT
            for line in details.splitlines():
                if '```' in line:
                    in_code_block = not in_code_block
                    result.append(f"{code_indent}{line.strip()}")
                else:
                    if in_code_block:
                        result.append(f"{code_indent}{line}")
                    else:
                        result.extend(process_multiline_text(line, base_indent))
        else:
            # 尝试解析 JSON
            try:
                json_obj = json.loads(details)
                json_str = json.dumps(json_obj, indent=2, ensure_ascii=False)
                for line in json_str.splitlines():
                    if line.strip():
                        result.append(f"{base_indent + STEP_BASE_INDENT}{line}")
                    else:
                        result.append("")
            except json.JSONDecodeError:
                # 非 JSON 格式，按普通多行文本处理
                result.extend(process_multiline_text(details, base_indent))

    elif isinstance(details, list):
        # 处理列表
        for item in details:
            if isinstance(item, str):
                if '\n' in item or '```' in item:
                    # 多行文本
                    result.append(f"{base_indent + STEP_BASE_INDENT}- {item.splitlines()[0].strip()}")
                    remaining_lines = '\n'.join(item.splitlines()[1:])
                    if remaining_lines:
                        result.extend(process_multiline_text(remaining_lines, base_indent + STEP_BASE_INDENT, True))
                else:
                    result.append(f"{base_indent + STEP_BASE_INDENT}- {item.strip()}")
            elif isinstance(item, dict):
                # 处理字典类型的列表项
                for key, value in item.items():
                    # 添加标题行
                    result.append(f"{base_indent + STEP_BASE_INDENT}- {key}:")
                    if isinstance(value, str):
                        # 处理多行文本
                        result.extend(process_multiline_text(value, base_indent + STEP_BASE_INDENT + LIST_ITEM_INDENT))
                    else:
                        # 递归处理其他类型的值
                        result.extend(format_step_details(value, base_indent + STEP_BASE_INDENT + LIST_ITEM_INDENT))

    elif isinstance(details, dict):
        # 处理字典
        for key, value in details.items():
            if skip_title and key == 'title':
                continue

            display_key = FIELD_NAMES.get(key, key)
            if isinstance(value, (str, list, dict)):
                result.append(f"\n{base_indent + STEP_BASE_INDENT}{display_key}:")
                if isinstance(value, str):
                    if '\n' in value or '```' in value:
                        result.extend(process_multiline_text(value, base_indent + STEP_BASE_INDENT))
                    else:
                        result.extend(wrap_long_line(value.strip(), base_indent + STEP_BASE_INDENT + LIST_ITEM_INDENT))
                else:
                    result.extend(format_step_details(value, base_indent + STEP_BASE_INDENT))
            else:
                result.append(f"{base_indent + STEP_BASE_INDENT}{display_key}: {value}")

    return result

def format_todo_details(todo: dict, steps: list, current_step_index: int, indent: str = "     ") -> List[str]:
    """格式化待办事项详细信息"""
    details = []
    SECTION_INDENT = "  "  # 段落缩进

    # 添加任务说明
    if todo.get('description'):
        details.append(f"{indent}{SECTION_INDENT}任务说明:")
        details.extend(format_step_details(todo['description'], indent + SECTION_INDENT))

    # 添加当前步骤
    if 0 <= current_step_index < len(steps):
        current_step = steps[current_step_index]
        details.append(f"{indent}{SECTION_INDENT}当前步骤:")
        details.append(f"{indent}{SECTION_INDENT}{SECTION_INDENT}{current_step['title']}")

        # 格式化步骤内容
        step_content = {}
        if current_step.get('rule'):
            step_content[FIELD_NAMES['rule']] = current_step['rule']
        if current_step.get('output'):
            step_content[FIELD_NAMES['output']] = current_step['output']
        if current_step.get('input'):
            step_content[FIELD_NAMES['input']] = current_step['input']

        details.extend(format_step_details(step_content, indent + SECTION_INDENT + SECTION_INDENT))

    return details

def format_todo_output(data: dict, only_memory: bool = False) -> str:
    """格式化输出任务列表
    Args:
        data: 任务数据
        only_memory: 是否只输出任务记忆部分
    """
    if not data:
        return "错误：数据为空"

    current_todo_index = data.get('current_todo_index')
    current_step_index = data.get('current_step_index', 0)
    todos = data.get('todos', [])
    steps = data.get('steps', [])
    memory = data.get('memory', {})
    max_display_todos = 3
    max_line_length = 100  # 最大行长度

    def wrap_text(text: str, indent: str = "") -> List[str]:
        """处理长文本，在适当位置换行"""
        if len(indent + text) <= max_line_length:
            return [indent + text]

        # 如果文本包含逗号，优先在逗号处换行
        if ',' in text:
            parts = text.split(',')
            lines = []
            current_line = indent
            for i, part in enumerate(parts):
                part = part.strip()
                if i < len(parts) - 1:
                    part += ','
                if len(current_line) + len(part) + 1 <= max_line_length:
                    if current_line == indent:
                        current_line += part
                    else:
                        current_line += ' ' + part
                else:
                    if current_line != indent:
                        lines.append(current_line)
                    current_line = indent + part
            if current_line != indent:
                lines.append(current_line)
            return lines

        # 如果文本包含句号，尝试在句号处换行
        if '。' in text:
            parts = text.split('。')
            lines = []
            current_line = indent
            for i, part in enumerate(parts):
                part = part.strip()
                if i < len(parts) - 1:
                    part += '。'
                if len(current_line) + len(part) + 1 <= max_line_length:
                    if current_line == indent:
                        current_line += part
                    else:
                        current_line += ' ' + part
                else:
                    if current_line != indent:
                        lines.append(current_line)
                    current_line = indent + part
            if current_line != indent:
                lines.append(current_line)
            return lines

        # 否则按单词换行
        words = text.split()
        lines = []
        current_line = indent

        for word in words:
            if len(current_line) + len(word) + 1 <= max_line_length:
                if current_line == indent:
                    current_line += word
                else:
                    current_line += ' ' + word
            else:
                if current_line != indent:
                    lines.append(current_line)
                current_line = indent + word

        if current_line != indent:
            lines.append(current_line)

        return lines

    output = []

    if only_memory:
      if memory:
        output.append("\n任务记忆:")
        if isinstance(memory, dict):
            for key, value in memory.items():
                if isinstance(value, (list, dict)):
                    output.append(f"  {key}:")
                    if isinstance(value, list):
                        for item in value:
                            output.extend(wrap_text(str(item), "    - "))
                    else:
                        for k, v in value.items():
                            output.extend(wrap_text(f"{k}: {v}", "    "))
                else:
                    output.extend(wrap_text(f"{key}: {value}", "  "))
        elif isinstance(memory, list):
            for item in memory:
                output.extend(wrap_text(str(item), "  - "))
        else:
            output.extend(wrap_text(str(memory), "  "))

      return "\n".join(output)


    if not todos:
        output.append("等待用户指定任务目标:")
        output.append("任务步骤:")
        for j, step in enumerate(steps):
            status = get_step_status(j, current_step_index)
            checkbox, state = status
            output.append(f"{j + 1}. {checkbox} {step['title']}{state}")
            if j == current_step_index:
                output.extend(format_step_details(step))
    else:
        # 显示当前任务
        current_todo = todos[current_todo_index] if current_todo_index is not None else None
        if current_todo:
            output.append(f"当前任务:{current_todo['title']}")
            if current_todo.get('description'):
                output.append("任务说明:")
                # 先尝试按句号分割，如果没有句号再按整体处理
                description = current_todo['description']
                if '。' in description:
                    parts = description.split('。')
                    for i, part in enumerate(parts):
                        if part.strip():  # 忽略空字符串
                            if i < len(parts) - 1:
                                part = part.strip() + '。'
                            else:
                                part = part.strip()
                            output.extend(wrap_text(part, "  "))
                else:
                    output.extend(wrap_text(description, "  "))

            # 展示任务记忆
            if memory:
                output.append("\n任务记忆:")
                if isinstance(memory, dict):
                    for key, value in memory.items():
                        if isinstance(value, (list, dict)):
                            output.append(f"  {key}:")
                            if isinstance(value, list):
                                for item in value:
                                    output.extend(wrap_text(str(item), "    - "))
                            else:
                                for k, v in value.items():
                                    output.extend(wrap_text(f"{k}: {v}", "    "))
                        else:
                            output.extend(wrap_text(f"{key}: {value}", "  "))
                elif isinstance(memory, list):
                    for item in memory:
                        output.extend(wrap_text(str(item), "  - "))
                else:
                    output.extend(wrap_text(str(memory), "  "))

            # 显示任务步骤
            output.append("\n任务步骤:")
            for j, step in enumerate(steps):
                status = get_step_status(j, current_step_index)
                checkbox, state = status
                output.append(f"{j + 1}. {checkbox} {step['title']}{state}")
                if j == current_step_index:
                    # 格式化步骤内容
                    step_content = {}
                    if step.get('rule'):
                        step_content[FIELD_NAMES['rule']] = step['rule']
                    if step.get('output'):
                        step_content[FIELD_NAMES['output']] = step['output']
                    if step.get('input'):
                        step_content[FIELD_NAMES['input']] = step['input']
                    output.extend(format_step_details(step_content, "  "))

        remaining_count = len(todos) - max_display_todos
        if remaining_count > 0:
            output.append(f"\n(剩余{remaining_count}个步骤未展示，当前步骤完成后，请使用/task next 指令进入下一步骤)")

    output.extend(["", "**以上是最新的当前任务信息，当前步骤完成后，使用/task next 指令进入下一步骤**"])
    output.extend(["", "**接下来，你(AI)必须清晰明确的罗列出自己要遵循的当前步骤规则，严格遵循，自动执行**"])
    return "\n".join(output)

def update_rules_file(output: str):
    """更新规则文件中的current_task标签内容"""
    if not os.path.exists(rules_path):
        with open(rules_path, 'w', encoding='utf-8') as f:
            f.write('<current_task>\n</current_task>')

    with open(rules_path, 'r', encoding='utf-8') as f:
        content = f.read()

    if '<current_task>' not in content:
        content += '\n<current_task>\n</current_task>'

    import re
    # 在正则处理前转义反斜杠
    # 如果output不为空，将反斜杠替换为双反斜杠
    escaped_output = output.replace('\\', '\\\\') if output else output

    # 使用正则表达式替换current_task标签内的内容
    new_content = re.sub(
        r'<current_task>.*?</current_task>',
        f'<current_task>\n{escaped_output}\n</current_task>',
        content,
        flags=re.DOTALL
    )

    with open(rules_path, 'w', encoding='utf-8') as f:
        f.write(new_content)

def start_workflow(workflow_name: str = '') -> str:
    """启动指定工作流程"""
    if not workflow_name:
        return "错误: 未指定工作流名称"

    # 先清空当前任务
    clear_task()

    # 优先在本地任务流目录下查找
    local_template_path = os.path.join(LOCAL_TASK_DIR, f'{workflow_name}.yml')
    # 如果在本地目录下没找到，则在agent任务流目录下查找
    if os.path.exists(local_template_path):
        template_path = local_template_path
    else:
        agent_template_path = os.path.join(AGENT_TASK_DIR, f'{workflow_name}.yml')
        if os.path.exists(agent_template_path):
            template_path = agent_template_path
        else:
            return f"错误: 未找到工作流规则文件，已尝试以下路径:\n - {local_template_path}\n - {agent_template_path}"

    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template_data = yaml.safe_load(f)

        # 合并非空的 pre_steps
        if template_data.get('pre_steps'):
            template_data['steps'] = template_data['pre_steps'] + template_data.get('steps', [])

        # 初始化任务状态
        template_data['current_todo_index'] = 0 if template_data.get('todos') else None
        template_data['current_step_index'] = 0

        save_task_data(template_data)

        # 获取当前任务信息用于上报
        task_name = template_data.get('name', workflow_name)
        if task_name:
            report_action({
              'action': '任务流:启动',
              'task_name': task_name
            })

        return format_todo_output(template_data)
    except Exception as e:
        return f"错误: {str(e)}"

def update_task(json_str: str) -> str:
    """更新任务，添加新的任务项"""
    try:
        new_todo = json.loads(json_str)

        if not isinstance(new_todo, dict):
            raise ValueError("输入必须是一个JSON对象")

        required_fields = ['title', 'description']
        for field in required_fields:
            if field not in new_todo:
                raise ValueError(f"缺少必要字段: {field}")

        data = load_task_data()
        if not data:
            data = {'todos': [], 'steps': [], 'current_todo_index': None, 'current_step_index': None}

        data['todos'].append(new_todo)

        if data['current_todo_index'] is None and data['todos']:
            data['current_todo_index'] = 0
        data['current_step_index'] = len(data.get('pre_steps', []))

        save_task_data(data)
        result = format_todo_output(data)
        print("**任务已创建成功，现在已经进入下一步骤了，请开始工作**")
        return result
    except Exception as e:
        return f"更新任务失败: {str(e)}"

def next_step() -> str:
    """进入下一个步骤"""
    data = load_task_data()
    if not data:
        output = "没有待处理的任务"
        print(output)
        update_rules_file(output)
        return output

    current_todo_index = data.get('current_todo_index')
    current_step_index = data.get('current_step_index')


    # 如果没有任务列表，但有步骤列表，继续执行步骤
    if not data.get('todos') and 'steps' in data:
        if current_step_index is None:
            data['current_step_index'] = 0
        elif current_step_index < len(data['steps']) - 1:
            data['current_step_index'] = current_step_index + 1
    # 如果有任务列表
    elif data.get('todos'):
        if current_todo_index is None:
            data['current_todo_index'] = 0
            data['current_step_index'] = 0
        elif current_step_index < len(data['steps']) - 1:
            data['current_step_index'] = current_step_index + 1
        else:
            # 当前任务的所有步骤已完成
            if current_todo_index < len(data['todos']) - 1:
                data['todos'][current_todo_index]['completed'] = True
                data['current_todo_index'] = current_todo_index + 1
                data['current_step_index'] = 0
                output = "当前任务已完成，开始处理下一个任务\n" + format_todo_output(data)
                save_task_data(data)
                update_rules_file(output)
                return output
            else:
                output = "所有任务已完成"
                data['current_todo_index'] = None
                data['current_step_index'] = None
                save_task_data(data)
                update_rules_file(output)
                return output

    save_task_data(data)
    output = format_todo_output(data)
    update_rules_file(output)

    # 上报信息
    task_name = data.get('name', '')
    step_name = ""

    if data.get('todos') and data['current_todo_index'] is not None and data['current_todo_index'] < len(data['todos']):
        task_name = data['todos'][data['current_todo_index']].get('title', '')

    if data.get('steps') and data['current_step_index'] is not None and data['current_step_index'] < len(data['steps']):
        step_name = data['steps'][data['current_step_index']].get('title', '')

    report_action({
        'action': '任务流:下一步',
        'task_name': task_name,
        'step_name': step_name
    })

    return output

def go_back() -> str:
    """返回上一步或上一个任务"""
    data = load_task_data()
    if not data:
        output = "没有待处理的任务"
        update_rules_file(output)
        return output

    current_todo_index = data.get('current_todo_index')
    current_step_index = data.get('current_step_index')

    if current_step_index is None:
        return "无法返回"

    if current_step_index > 0:
        data['current_step_index'] = current_step_index - 1
    elif current_todo_index is not None and current_todo_index > 0:
        data['current_todo_index'] = current_todo_index - 1
        data['todos'][current_todo_index - 1]['completed'] = False
        data['current_step_index'] = 0

    save_task_data(data)
    output = format_todo_output(data)
    update_rules_file(output)
    return output

def clear_task() -> str:
    """清空任务"""
    if os.path.exists(task_file):
        os.remove(task_file)
    output = ""
    update_rules_file(output)
    return output

def update_memory(memory_data: str, mode: str = 'append') -> str:
    """更新任务记忆
    Args:
        memory_data: 记忆数据的JSON字符串
        mode: 更新模式，'append'为新增模式，'replace'为覆盖模式
    """
    try:
        new_memory = json.loads(memory_data)

        data = load_task_data()
        if not data:
            data = {'todos': [], 'steps': [], 'current_todo_index': None, 'current_step_index': None, 'memory': {}}

        # 确保 memory 字段存在
        if 'memory' not in data:
            data['memory'] = {}

        # 根据模式更新记忆
        if mode == 'replace':
            data['memory'] = new_memory
        else:  # append mode
            if isinstance(new_memory, dict):
                data['memory'].update(new_memory)
            else:
                data['memory'] = new_memory

        # save_task_data(data)
        result = format_todo_output(data, only_memory=True)
        print(f"**任务记忆已{mode == 'replace' and '覆盖' or '更新'}**")
        return result
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        return f"更新任务记忆失败: {str(e)}"

def clear_memory() -> str:
    """清空任务记忆"""
    data = load_task_data()
    if not data:
        return "没有任务数据"

    data['memory'] = {}
    save_task_data(data)
    result = format_todo_output(data, only_memory=True)
    print("**任务记忆已清空**")
    return result

def reset_current_task() -> str:
    """重置当前任务的步骤到pre_steps之后的第一步"""
    data = load_task_data()
    if not data:
        output = "没有待处理的任务"
        update_rules_file(output)
        return output

    # 获取pre_steps长度
    pre_steps_len = len(data.get('pre_steps', []))

    # 如果当前步骤在pre_steps中，不重置
    if data.get('current_step_index', 0) < pre_steps_len:
        return format_todo_output(data)

    # 重置到pre_steps之后的第一个步骤
    data['current_step_index'] = pre_steps_len

    # 如果有任务列表，重置当前任务的步骤
    if data.get('todos'):
        current_index = data.get('current_todo_index')
        if current_index is not None and current_index < len(data['todos']):
            data['current_step_index'] = pre_steps_len

    save_task_data(data)
    output = format_todo_output(data)
    output += "\n当前任务未完成，步骤已重置，请按步骤重新分析、执行"
    update_rules_file(output)
    return output

def list_workflows():
    """列出所有可用的任务流模板"""
    # 导入tabulate库
    try:
        from tabulate import tabulate  # 需要安装: pip install tabulate
    except ImportError:
        print("请先安装tabulate库: pip install tabulate")
        print("或者运行: pip install -r .agent/requirements.txt")
        return

    # 不再需要打印扫描目录信息
    # print(f"Scanning local task dir: {LOCAL_TASK_DIR}")
    # print(f"Scanning agent task dir: {AGENT_TASK_DIR}")

    workflows = []

    # 扫描本地任务流目录
    if os.path.exists(LOCAL_TASK_DIR):
        for file in os.listdir(LOCAL_TASK_DIR):
            if file.endswith('.yml'):
                path = os.path.join(LOCAL_TASK_DIR, file)
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        workflow = yaml.safe_load(f)
                        workflows.append({
                            'name': os.path.splitext(file)[0],
                            'title': workflow.get('name', ''),
                            'path': path
                        })
                except Exception as e:
                    print(f"Error reading {path}: {e}")

    # 扫描agent任务流目录
    if os.path.exists(AGENT_TASK_DIR):
        for file in os.listdir(AGENT_TASK_DIR):
            if file.endswith('.yml'):
                path = os.path.join(AGENT_TASK_DIR, file)
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        workflow = yaml.safe_load(f)
                        workflows.append({
                            'name': os.path.splitext(file)[0],
                            'title': workflow.get('name', ''),
                            'path': path
                        })
                except Exception as e:
                    print(f"Error reading {path}: {e}")

    # 按名称排序
    workflows.sort(key=lambda x: x['name'])

    # 准备表格数据
    headers = ["任务流名称", "标题", "路径"]
    table_data = []

    for workflow in workflows:
        path = workflow['path']
        # 提取路径的最后一部分，通常是文件名
        path = os.path.basename(path)

        table_data.append([
            workflow['name'],
            workflow['title'],
            path
        ])

    # 使用tabulate生成表格，设置表格格式
    try:
        # 使用grid样式，每行都有横线分隔
        table = tabulate(table_data, headers=headers, tablefmt="grid")
    except:
        # 如果不支持，回退到fancy_grid
        table = tabulate(table_data, headers=headers, tablefmt="fancy_grid")

    # 输出任务流列表
    print("\n可用的任务流模板:")
    print(table)

    # 添加用法示例
    print("\n用法示例:")
    print("  /task use <任务流名称>    # 使用指定任务流")
    print("  /task now                # 查看当前任务详情")
    print("  /task next               # 进入下一个步骤")

def main():
    """主函数"""

    # 在执行任何命令前先检查用户信息
    user_id, user_name = get_user_info()
    if not user_id or not user_name:
        print(f"错误: 未找到用户信息，请确保.env文件存在并包含USER_ID和USER_NAME环境变量")
        return

    if len(sys.argv) < 2:
        print("示例: python task.py <command> [args]")
        print("可用命令: now, start, next, back, update, clear, empty, use, list, memory, forget, reset")
        return

    # 在执行任何命令前先执行清屏命令
    os.system('cls' if os.name == 'nt' else 'clear')

    workspace_path = Path(workspace)
    workspace_path.mkdir(parents=True, exist_ok=True)

    cmd = sys.argv[1]
    commands = {
        'use': lambda: start_workflow(sys.argv[2]) if len(sys.argv) > 2 else "错误: 未指定工作流名称",
        'now': lambda: format_todo_output(load_task_data()),
        'next': next_step,
        'back': go_back,
        'update': lambda: update_task(sys.argv[2]) if len(sys.argv) > 2 else "错误: 未提供更新数据",
        'clear': clear_task,
        'memory': lambda: update_memory(sys.argv[2], sys.argv[3] if len(sys.argv) > 3 else 'append') if len(sys.argv) > 2 else "错误: 未提供记忆数据",
        'forget': clear_memory,
        'reset': reset_current_task,
        'flows': list_workflows,
    }

    if cmd in commands:
        output = commands[cmd]()
        print(output)
        update_rules_file(output)
    else:
        output = f"未知命令: {cmd}\n可用命令: next, back, update, clear, memory [data] [mode], forget, reset, flows"
        print(output)
        update_rules_file(output)

if __name__ == '__main__':
    main()

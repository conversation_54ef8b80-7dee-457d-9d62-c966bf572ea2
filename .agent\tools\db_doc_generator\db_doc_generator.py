#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库文档生成器
用于自动生成MySQL/PostgreSQL数据库的markdown格式文档
"""

import os
import sys
import json
import time
from typing import Dict, List, Optional, Union
from abc import ABC, abstractmethod

# 数据库连接相关
import pymysql
import psycopg2

class DBConnector(ABC):
    """数据库连接器基类"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self._conn = None
        
    @abstractmethod
    def connect(self):
        """建立数据库连接"""
        pass
        
    @abstractmethod
    def close(self):
        """关闭数据库连接"""
        pass
        
    @abstractmethod
    def get_tables(self) -> List[Dict]:
        """获取所有表信息"""
        pass
        
    @abstractmethod
    def get_table_fields(self, table_name: str) -> List[Dict]:
        """获取表字段信息"""
        pass
        
    @abstractmethod
    def get_table_indexes(self, table_name: str) -> List[Dict]:
        """获取表索引信息"""
        pass
        
    @abstractmethod
    def get_table_foreign_keys(self, table_name: str) -> List[Dict]:
        """获取表外键信息"""
        pass

class MySQLConnector(DBConnector):
    """MySQL连接器"""
    
    def connect(self):
        try:
            self._conn = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4'
            )
            return self._conn
        except Exception as e:
            raise Exception(f"MySQL连接失败: {str(e)}")
            
    def close(self):
        if self._conn:
            self._conn.close()
            
    def get_tables(self) -> List[Dict]:
        """获取所有表信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    table_name,
                    table_comment,
                    table_rows
                FROM 
                    information_schema.tables 
                WHERE 
                    table_schema = %s
                    AND table_type = 'BASE TABLE'
                ORDER BY 
                    table_name
            """
            cursor.execute(sql, (self.database,))
            tables = []
            for row in cursor.fetchall():
                tables.append({
                    'table_name': row[0],
                    'table_comment': row[1],
                    'table_rows': row[2] or 0
                })
            return tables
        finally:
            cursor.close()
            
    def get_table_fields(self, table_name: str) -> List[Dict]:
        """获取表字段信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    column_name,
                    column_type,
                    is_nullable,
                    column_default,
                    column_comment,
                    extra
                FROM 
                    information_schema.columns
                WHERE 
                    table_schema = %s
                    AND table_name = %s
                ORDER BY 
                    ordinal_position
            """
            cursor.execute(sql, (self.database, table_name))
            fields = []
            for row in cursor.fetchall():
                fields.append({
                    'field_name': row[0],
                    'field_type': row[1],
                    'nullable': row[2] == 'YES',
                    'default_value': row[3],
                    'comment': row[4],
                    'extra': row[5]
                })
            return fields
        finally:
            cursor.close()
            
    def get_table_indexes(self, table_name: str) -> List[Dict]:
        """获取表索引信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    index_name,
                    non_unique,
                    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns
                FROM 
                    information_schema.statistics
                WHERE 
                    table_schema = %s
                    AND table_name = %s
                GROUP BY 
                    index_name, non_unique
                ORDER BY 
                    index_name
            """
            cursor.execute(sql, (self.database, table_name))
            indexes = []
            for row in cursor.fetchall():
                indexes.append({
                    'index_name': row[0],
                    'is_unique': row[1] == 0,
                    'columns': row[2].split(',')
                })
            return indexes
        finally:
            cursor.close()
            
    def get_table_foreign_keys(self, table_name: str) -> List[Dict]:
        """获取表外键信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    constraint_name,
                    column_name,
                    referenced_table_name,
                    referenced_column_name
                FROM 
                    information_schema.key_column_usage
                WHERE 
                    table_schema = %s
                    AND table_name = %s
                    AND referenced_table_name IS NOT NULL
                ORDER BY 
                    constraint_name
            """
            cursor.execute(sql, (self.database, table_name))
            foreign_keys = []
            for row in cursor.fetchall():
                foreign_keys.append({
                    'constraint_name': row[0],
                    'column_name': row[1],
                    'referenced_table': row[2],
                    'referenced_column': row[3]
                })
            return foreign_keys
        finally:
            cursor.close()

class PostgreSQLConnector(DBConnector):
    """PostgreSQL连接器"""
    
    def __init__(self, host: str, port: int, user: str, password: str, database: str, schema: str = 'public'):
        super().__init__(host, port, user, password, database)
        self.schema = schema
    
    def connect(self):
        try:
            self._conn = psycopg2.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                dbname=self.database
            )
            return self._conn
        except Exception as e:
            raise Exception(f"PostgreSQL连接失败: {str(e)}")
            
    def close(self):
        if self._conn:
            self._conn.close()
            
    def get_tables(self) -> List[Dict]:
        """获取所有表信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    c.relname as table_name,
                    obj_description(c.oid) as table_comment,
                    c.reltuples::bigint as table_rows
                FROM 
                    pg_class c
                    JOIN pg_namespace n ON n.oid = c.relnamespace
                WHERE 
                    n.nspname = %s
                    AND c.relkind = 'r'
                ORDER BY 
                    c.relname
            """
            cursor.execute(sql, (self.schema,))
            tables = []
            for row in cursor.fetchall():
                tables.append({
                    'table_name': row[0],
                    'table_comment': row[1] or '',
                    'table_rows': row[2]
                })
            return tables
        finally:
            cursor.close()
            
    def get_table_fields(self, table_name: str) -> List[Dict]:
        """获取表字段信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    a.attname as column_name,
                    format_type(a.atttypid, a.atttypmod) as data_type,
                    a.attnotnull as not_null,
                    pg_get_expr(d.adbin, d.adrelid) as default_value,
                    col_description(a.attrelid, a.attnum) as comment
                FROM 
                    pg_attribute a
                    LEFT JOIN pg_attrdef d ON a.attrelid = d.adrelid AND a.attnum = d.adnum
                WHERE 
                    a.attrelid = %s::regclass
                    AND a.attnum > 0
                    AND NOT a.attisdropped
                ORDER BY 
                    a.attnum
            """
            cursor.execute(sql, (f"{self.schema}.{table_name}",))
            fields = []
            for row in cursor.fetchall():
                fields.append({
                    'field_name': row[0],
                    'field_type': row[1],
                    'nullable': not row[2],
                    'default_value': row[3],
                    'comment': row[4] or ''
                })
            return fields
        finally:
            cursor.close()
            
    def get_table_indexes(self, table_name: str) -> List[Dict]:
        """获取表索引信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT 
                    i.relname as index_name,
                    ix.indisunique as is_unique,
                    array_to_string(ARRAY(
                        SELECT pg_get_indexdef(ix.indexrelid, k + 1, true)
                        FROM generate_subscripts(ix.indkey, 1) as k
                        ORDER BY k
                    ), ', ') as columns
                FROM 
                    pg_index ix
                    JOIN pg_class i ON i.oid = ix.indexrelid
                    JOIN pg_class t ON t.oid = ix.indrelid
                    LEFT JOIN pg_namespace n ON n.oid = t.relnamespace
                WHERE 
                    t.relname = %s
                    AND n.nspname = %s
                ORDER BY 
                    i.relname
            """
            cursor.execute(sql, (table_name, self.schema))
            indexes = []
            for row in cursor.fetchall():
                indexes.append({
                    'index_name': row[0],
                    'is_unique': row[1],
                    'columns': row[2].split(', ')
                })
            return indexes
        finally:
            cursor.close()
            
    def get_table_foreign_keys(self, table_name: str) -> List[Dict]:
        """获取表外键信息"""
        cursor = self._conn.cursor()
        try:
            sql = """
                SELECT
                    tc.constraint_name,
                    kcu.column_name,
                    ccu.table_name AS referenced_table,
                    ccu.column_name AS referenced_column
                FROM 
                    information_schema.table_constraints AS tc
                    JOIN information_schema.key_column_usage AS kcu
                        ON tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                        ON ccu.constraint_name = tc.constraint_name
                        AND ccu.table_schema = tc.table_schema
                WHERE 
                    tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = %s
                    AND tc.table_name = %s
            """
            cursor.execute(sql, (self.schema, table_name))
            foreign_keys = []
            for row in cursor.fetchall():
                foreign_keys.append({
                    'constraint_name': row[0],
                    'column_name': row[1],
                    'referenced_table': row[2],
                    'referenced_column': row[3]
                })
            return foreign_keys
        finally:
            cursor.close()

class MarkdownGenerator:
    """Markdown文档生成器"""
    
    def __init__(self, db_type: str, database: str):
        self.db_type = db_type
        self.database = database
        
    def generate_doc(self, tables: List[Dict], table_details: Dict[str, Dict], output_type: str = 'table') -> str:
        """生成文档
        
        Args:
            tables: 表信息列表
            table_details: 表详细信息
            output_type: 输出类型，可选值：'table'（只输出表格）, 'ddl'（只输出DDL）
        """
        if output_type == 'table':
            return self._generate_table_doc(tables, table_details)
        elif output_type == 'ddl':
            return self._generate_ddl_doc(tables, table_details)
        else:
            raise ValueError(f"不支持的输出类型: {output_type}")
            
    def _generate_table_doc(self, tables: List[Dict], table_details: Dict[str, Dict]) -> str:
        """生成表格形式的文档"""
        doc_parts = []
        
        # 文档头部
        doc_parts.append(self._generate_header())
        
        # 表概览
        doc_parts.append(self._generate_tables_overview(tables))
        
        # 详细表结构（仅表格形式）
        doc_parts.append(self._generate_tables_detail_table_only(tables, table_details))
        
        return '\n\n'.join(doc_parts)
        
    def _generate_ddl_doc(self, tables: List[Dict], table_details: Dict[str, Dict]) -> str:
        """生成DDL形式的文档"""
        doc_parts = []
        
        # 文档头部
        doc_parts.append(self._generate_header())
        
        # 表概览
        doc_parts.append(self._generate_tables_overview(tables))
        
        # 详细表结构（仅DDL形式）
        doc_parts.append(self._generate_tables_detail_ddl_only(tables, table_details))
        
        return '\n\n'.join(doc_parts)
        
    def _generate_header(self) -> str:
        """生成文档头部"""
        return f"""# {self.database} 数据库文档

## 文档说明
- 数据库类型：{self.db_type}
- 数据库名称：{self.database}
- 文档生成时间：{time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
    def _generate_tables_overview(self, tables: List[Dict]) -> str:
        """生成表概览部分"""
        parts = ['## 表清单\n']
        parts.append('| 序号 | 表名 | 说明 | 约估行数 |')
        parts.append('| ---- | ---- | ---- | -------- |')
        
        for i, table in enumerate(tables, 1):
            parts.append(f"| {i} | {table['table_name']} | {table['table_comment']} | {table['table_rows']} |")
            
        return '\n'.join(parts)
        
    def _generate_tables_detail(self, tables: List[Dict], table_details: Dict[str, Dict]) -> str:
        """生成详细表结构部分（包含表格和DDL）"""
        parts = ['## 表结构详情\n']
        
        for table_name, details in table_details.items():
            # 表信息
            parts.append(f"### {table_name}\n")
            parts.append(f"**表说明**：{details['table_info']['table_comment']}\n")
            
            # 表结构DDL
            parts.append("#### 建表语句\n")
            parts.append("```sql")
            parts.extend(self._generate_table_ddl(table_name, details))
            parts.append("```\n")
            
            # 字段信息
            parts.append('#### 字段列表\n')
            parts.append('| 序号 | 字段名 | 类型 | 必填 | 默认值 | 说明 |')
            parts.append('| ---- | ------ | ---- | ---- | ------ | ---- |')
            
            for i, field in enumerate(details['fields'], 1):
                nullable = '否' if not field['nullable'] else '是'
                default = field['default_value'] if field['default_value'] is not None else ''
                parts.append(f"| {i} | {field['field_name']} | {field['field_type']} | {nullable} | {default} | {field['comment']} |")
            
            # 索引信息
            if details['indexes']:
                parts.append('\n#### 索引列表\n')
                parts.append('| 序号 | 索引名 | 唯一索引 | 字段列表 |')
                parts.append('| ---- | ------ | -------- | -------- |')
                
                for i, index in enumerate(details['indexes'], 1):
                    is_unique = '是' if index['is_unique'] else '否'
                    columns = ', '.join(index['columns'])
                    parts.append(f"| {i} | {index['index_name']} | {is_unique} | {columns} |")
            
            # 外键信息
            if details['foreign_keys']:
                parts.append('\n#### 外键约束\n')
                parts.append('| 序号 | 约束名 | 字段名 | 关联表 | 关联字段 |')
                parts.append('| ---- | ------ | ------ | ------ | -------- |')
                
                for i, fk in enumerate(details['foreign_keys'], 1):
                    parts.append(f"| {i} | {fk['constraint_name']} | {fk['column_name']} | {fk['referenced_table']} | {fk['referenced_column']} |")
            
            parts.append('\n')  # 添加表间空行
            
        return '\n'.join(parts)
        
    def _generate_tables_detail_table_only(self, tables: List[Dict], table_details: Dict[str, Dict]) -> str:
        """生成详细表结构部分（仅表格形式）"""
        parts = ['## 表结构详情\n']
        
        for table_name, details in table_details.items():
            # 表信息
            parts.append(f"### {table_name}\n")
            parts.append(f"**表说明**：{details['table_info']['table_comment']}\n")
            
            # 字段信息
            parts.append('#### 字段列表\n')
            parts.append('| 序号 | 字段名 | 类型 | 必填 | 默认值 | 说明 |')
            parts.append('| ---- | ------ | ---- | ---- | ------ | ---- |')
            
            for i, field in enumerate(details['fields'], 1):
                nullable = '否' if not field['nullable'] else '是'
                default = field['default_value'] if field['default_value'] is not None else ''
                parts.append(f"| {i} | {field['field_name']} | {field['field_type']} | {nullable} | {default} | {field['comment']} |")
            
            # 索引信息
            if details['indexes']:
                parts.append('\n#### 索引列表\n')
                parts.append('| 序号 | 索引名 | 唯一索引 | 字段列表 |')
                parts.append('| ---- | ------ | -------- | -------- |')
                
                for i, index in enumerate(details['indexes'], 1):
                    is_unique = '是' if index['is_unique'] else '否'
                    columns = ', '.join(index['columns'])
                    parts.append(f"| {i} | {index['index_name']} | {is_unique} | {columns} |")
            
            # 外键信息
            if details['foreign_keys']:
                parts.append('\n#### 外键约束\n')
                parts.append('| 序号 | 约束名 | 字段名 | 关联表 | 关联字段 |')
                parts.append('| ---- | ------ | ------ | ------ | -------- |')
                
                for i, fk in enumerate(details['foreign_keys'], 1):
                    parts.append(f"| {i} | {fk['constraint_name']} | {fk['column_name']} | {fk['referenced_table']} | {fk['referenced_column']} |")
            
            parts.append('\n')  # 添加表间空行
            
        return '\n'.join(parts)
        
    def _generate_tables_detail_ddl_only(self, tables: List[Dict], table_details: Dict[str, Dict]) -> str:
        """生成详细表结构部分（仅DDL形式）"""
        parts = ['## 表结构详情\n']
        
        for table_name, details in table_details.items():
            # 表信息
            parts.append(f"### {table_name}\n")
            parts.append(f"**表说明**：{details['table_info']['table_comment']}\n")
            
            # 表结构DDL
            parts.append("```sql")
            parts.extend(self._generate_table_ddl(table_name, details))
            parts.append("```\n")
            
        return '\n'.join(parts)
        
    def _generate_table_ddl(self, table_name: str, details: Dict) -> List[str]:
        """生成表的DDL语句"""
        ddl_parts = []
        
        # 生成CREATE TABLE语句
        ddl_parts.append(f"-- 表名: {table_name}")
        ddl_parts.append(f"-- 说明: {details['table_info']['table_comment']}")
        ddl_parts.append(f"CREATE TABLE {table_name} (")
        
        # 字段定义
        field_definitions = []
        for field in details['fields']:
            field_def = f"    {field['field_name']} {field['field_type']}"
            if not field['nullable']:
                field_def += " NOT NULL"
            if field['default_value'] is not None:
                field_def += f" DEFAULT {field['default_value']}"
            if field['comment']:
                field_def += f" -- {field['comment']}"
            field_definitions.append(field_def)
        
        ddl_parts.append(',\n'.join(field_definitions))
        ddl_parts.append(");\n")
        
        # 索引定义
        for index in details['indexes']:
            index_type = "UNIQUE" if index['is_unique'] else ""
            columns = ', '.join(index['columns'])
            ddl_parts.append(f"CREATE {index_type} INDEX {index['index_name']} ON {table_name} ({columns});")
        
        # 外键定义
        for fk in details['foreign_keys']:
            ddl_parts.append(
                f"ALTER TABLE {table_name} ADD CONSTRAINT {fk['constraint_name']} "
                f"FOREIGN KEY ({fk['column_name']}) "
                f"REFERENCES {fk['referenced_table']} ({fk['referenced_column']});"
            )
        
        return ddl_parts

def generate_db_doc(
    db_type: str,
    host: str,
    port: int,
    user: str,
    password: str,
    database: str,
    output_file: str,
    schema: str = 'public',
    input_tables: Optional[List[str]] = None,
    ignore_tables: Optional[List[str]] = None,
    output_type: str = 'table',
    output_dir: str = None
) -> None:
    """
    生成数据库文档
    
    Args:
        db_type: 数据库类型（'mysql' 或 'postgresql'）
        host: 数据库主机地址
        port: 数据库端口
        user: 数据库用户名
        password: 数据库密码
        database: 数据库名
        output_file: 输出文件名
        schema: PostgreSQL的schema名称，默认为public
        input_tables: 要生成文档的表名列表，为None时生成所有表的文档（优先级高于ignore_tables）
        ignore_tables: 要忽略的表名列表，这些表不会出现在文档中
        output_type: 输出类型，可选值：'table'（只输出表格）或 'ddl'（只输出DDL）
        output_dir: 输出目录，默认为项目根目录下的'_agent-local/knowledge/db'
    """
    # 设置默认输出目录为项目根目录下的_agent-local/knowledge/db
    if output_dir is None:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录 (当前目录的上上级目录)
        project_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
        # 设置默认输出目录
        output_dir = os.path.join(project_root, '_agent-local', 'knowledge', 'db')
    
    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    # 完整的输出文件路径
    output_path = os.path.join(output_dir, output_file)
    
    # 创建数据库连接器
    if db_type == 'mysql':
        connector = MySQLConnector(host, port, user, password, database)
    elif db_type == 'postgresql':
        connector = PostgreSQLConnector(host, port, user, password, database, schema)
    else:
        raise ValueError(f"不支持的数据库类型: {db_type}")
        
    # 连接数据库
    connector.connect()
    
    try:
        # 获取所有表信息
        all_tables = connector.get_tables()
        
        # 过滤表
        if input_tables is not None:
            all_tables = [t for t in all_tables if t['table_name'] in input_tables]
        elif ignore_tables is not None:
            all_tables = [t for t in all_tables if t['table_name'] not in ignore_tables]
            
        # 获取每个表的详细信息
        table_details = {}
        for table in all_tables:
            table_name = table['table_name']
            table_details[table_name] = {
                'table_info': table,
                'fields': connector.get_table_fields(table_name),
                'indexes': connector.get_table_indexes(table_name),
                'foreign_keys': connector.get_table_foreign_keys(table_name)
            }
            
        # 生成文档
        generator = MarkdownGenerator(db_type, database)
        content = generator.generate_doc(all_tables, table_details, output_type)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
            
        print(f"文档已生成: {output_path}")
            
    finally:
        connector.close()

def main():
    """命令行入口"""
    if len(sys.argv) != 2:
        print("用法: python db_doc_generator.py <config.json>")
        sys.exit(1)
        
    # 读取配置文件
    config_file = sys.argv[1]
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")
        sys.exit(1)
        
    # 生成文档
    try:
        generate_db_doc(
            db_type=config.get('db_type', 'mysql'),
            host=config['host'],
            port=config.get('port', 3306),
            user=config['user'],
            password=config['password'],
            database=config['database'],
            output_file=config['output_file'],
            schema=config.get('schema', 'public'),
            input_tables=config.get('input_tables'),
            ignore_tables=config.get('ignore_tables'),
            output_type=config.get('output_type', 'table'),
            output_dir=config.get('output_dir')
        )
    except Exception as e:
        print(f"生成文档失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()

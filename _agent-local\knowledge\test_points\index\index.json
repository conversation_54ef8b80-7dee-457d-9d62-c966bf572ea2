{"documents": {"登录功能_example_test_points_20250418184706": {"keywords": ["账号锁定30分钟后", "系统应该能防御sql注入攻击", "文本文件", "文件名", "连续多次密码错误锁定", "13", "1744973107", "输入正确的用户名", "记住密码功能", "系统应该正常处理", "账号锁定后解锁", "应该自动解锁", "11", "同一账号在不同设备同时登录", "14", "下次打开登录页面应该自动填充用户名和密码", "忘记密码功能", "系统应该能正常处理或给出合理提示", "请输入密码", "输入正确的用户名和错误的密码", "10", "15", "测试点列表", "无需重新登录", "特殊字符用户名登录", "不输入密码", "如50个字符", "错误的密码登录", "输入不存在的用户名和任意密码", "不输入用户名", "空用户名登录", "文件类型", "点击登录按钮", "选项", "2588677", "应该保持登录状态", "密码错误", "账号已锁定", "错误的用户名登录", "登录超时", "点击", "忘记密码", "sql注入测试", "记住密码", "账号应该被锁定", "文件信息", "16", "系统应该允许多设备同时在线", "登录会话保持", "连续输入错误密码5次", "example_test_points", "登录成功后", "链接", "请输入用户名", "测试点", "不出现异常", "系统应该自动退出登录", "输入任意密码", "成功登录后", "系统应该提示", "系统应该跳转到密码重置页面", "系统应该成功登录并跳转到首页", "正确的用户名和密码登录", "输入正确的用户名和密码", "txt", "可以正常登录", "使用包含特殊字符的用户名", "登录状态保持30分钟不操作", "使用超长的用户名和密码", "长用户名和密码登录", "同时登录多个设备", "12", "导入时间", "空密码登录", "关闭浏览器再打开", "登录功能测试点", "勾选", "在用户名或密码中输入sql注入语句", "请30分钟后再试", "用户名不存在", "并提示"], "metadata": {"original_file": "D:\\0AI\\TESTCASE\\2025CASE\\input\\example_test_points.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\登录功能_example_test_points_20250418184706.md", "category": "登录功能", "added_at": "2025-04-18T18:47:06.771402", "title": "example_test_points"}}, "满减活动_营销中心勋章满减POS应用_20250421120308": {"keywords": ["功能概述", "导入时间", "会员3勋章ab都有", "前置条件", "pos端有勋章的会员应用可命中满减", "账单金额20元", "文本文件", "文件名", "账单金额10元", "会员1有勋章a", "10", "txt", "营销中心勋章满减pos应用", "1744873380", "已点餐", "适用人群勋章a", "会员2有勋章b", "测试场景", "满20减1", "文件类型", "会员信息如下", "账单金额80元", "适用人群勋章b", "账单金额50元", "1857572", "活动命中情况", "适用人群全部勋章", "活动3", "活动2", "文件信息", "测试点列表", "测试点", "活动1", "应用不同会员", "会员4无勋章", "满30减3", "账单金额30元", "满50减5", "营销中心已设置满减活动"], "metadata": {"original_file": "营销中心勋章满减POS应用.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\满减活动_营销中心勋章满减POS应用_20250421120308.md", "category": "满减活动", "added_at": "2025-04-21T12:03:08.086083", "title": "营销中心勋章满减POS应用"}}, "满减活动_营销中心标签满减POS应用_20250421142619": {"keywords": ["10", "营销中心标签满减pos应用", "12", "txt", "文件名", "自动命中最大优惠", "已点餐", "测试场景及预期结果", "会员2有标签b", "默认会员标签可正常获取", "账单金额50元", "文本文件", "仅功能验证", "账单金额30元", "营销中心已设置满减活动", "满50减5", "活动命中情况", "存在多个满减时", "应用不同会员", "账单金额20元", "导入时间", "1744873639", "13", "适用人群标签b", "活动2", "活动1", "账单金额10元", "测试点", "14", "文件信息", "会员信息如下", "不考虑满减与其他优惠共享场景", "活动3", "会员3标签ab都有", "适用人群全部标签", "测试点列表", "适用人群标签a", "前置条件", "会员4无标签", "满30减3", "功能概述", "满20减1", "文件类型", "413975", "会员1有标签a", "11", "其他说明", "pos端有标签的会员命中满减活动"], "metadata": {"original_file": "营销中心标签满减POS应用.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\满减活动_营销中心标签满减POS应用_20250421142619.md", "category": "满减活动", "added_at": "2025-04-21T14:26:19.637098", "title": "营销中心标签满减POS应用"}}, "满减活动_营销中心会员等级满折POS应用_20250423144341": {"keywords": ["适用人群vip会员", "营销中心会员等级满折pos应用", "2061036", "导入时间", "会员1为普通会员", "会员2为vip会员", "应用不同会员", "pos端不同等级会员命中满打折活动", "活动命中情况", "文件名", "账单金额80元", "11", "文件类型", "不考虑满打折与其他优惠共享场景", "1745301850", "适用人群普通会员", "账单金额100元", "存在多个满打折时", "12", "测试点", "活动3", "满100打80折", "账单金额300元", "前置条件", "适用人群钻石会员", "文件信息", "满100打85折", "测试场景及预期结果", "仅功能验证", "会员3为钻石会员", "10", "已点餐", "活动4", "测试点列表", "账单金额200元", "活动1", "会员信息如下", "满100打90折", "营销中心已设置满打折活动", "文本文件", "txt", "满300打75折", "活动2", "适用人群全部会员", "自动命中最大优惠", "其他说明", "13", "功能概述"], "metadata": {"original_file": "营销中心会员等级满折POS应用.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\满减活动_营销中心会员等级满折POS应用_20250423144341.md", "category": "满减活动", "added_at": "2025-04-23T14:43:41.489489", "title": "营销中心会员等级满折POS应用"}}, "满减活动_营销中心会员等级满折POS应用_20250423153436": {"keywords": ["活动2", "会员1为普通会员", "应用不同会员", "11", "功能概述", "活动4", "2061036", "活动命中情况", "满100打90折", "测试点列表", "账单金额100元", "适用人群钻石会员", "自动命中最大优惠", "文本文件", "13", "文件名", "满100打80折", "前置条件", "1745301850", "营销中心会员等级满折pos应用", "其他说明", "txt", "会员信息如下", "已点餐", "营销中心已设置满打折活动", "测试场景及预期结果", "不考虑满打折与其他优惠共享场景", "文件类型", "测试点", "导入时间", "会员2为vip会员", "满300打75折", "存在多个满打折时", "账单金额200元", "适用人群vip会员", "活动3", "适用人群全部会员", "12", "账单金额80元", "10", "仅功能验证", "文件信息", "账单金额300元", "活动1", "会员3为钻石会员", "pos端不同等级会员命中满打折活动", "适用人群普通会员", "满100打85折"], "metadata": {"original_file": "营销中心会员等级满折POS应用.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\满减活动_营销中心会员等级满折POS应用_20250423153436.md", "category": "满减活动", "added_at": "2025-04-23T15:34:36.026958", "title": "营销中心会员等级满折POS应用"}}, "物业接口_欢乐颂店物业接口验证_20250425172645": {"keywords": ["测试点列表", "现金", "物业参数已正常配置", "不需要传菜品详细信息", "需线下手动处理", "文本文件", "账单有优惠金额", "上传时优惠金额固定0", "导入时间", "上传要求", "测试点", "文件类型", "数据上传记录写入数据库表中", "微信", "9378176", "正常账单类型sale和退单类型onlinerefund", "挂账", "会自动重试3次", "功能描述", "测试场景如下", "要传付款列表明确各付款方式的金额值", "账单金额直接传实收", "欢乐颂店物业接口验证", "账单付款场景", "补充说明", "1744960857", "以上各种付款场景退单", "数据上传物业", "每次都失败将不再上传", "系统支持传48小时内账单", "菜品信息传空数组即可", "文件信息", "找零", "优惠", "支付宝", "同时有日志记录", "前置条件", "txt", "订单时间格式", "文件名", "以上各种付款场景", "不考虑物业测试环境和性能测试", "网络中断或者物业不可用时", "服务支持接口上传", "不同的支付方式组合", "0金额账单", "yyyymmddhhmmss"], "metadata": {"original_file": "欢乐颂店物业接口验证.txt", "stored_file": "_agent-local\\knowledge\\test_points\\documents\\物业接口_欢乐颂店物业接口验证_20250425172645.md", "category": "物业接口", "added_at": "2025-04-25T17:26:45.626672", "title": "欢乐颂店物业接口验证"}}}, "keywords": {"账号锁定30分钟后": ["登录功能_example_test_points_20250418184706"], "系统应该能防御sql注入攻击": ["登录功能_example_test_points_20250418184706"], "文本文件": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "文件名": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "连续多次密码错误锁定": ["登录功能_example_test_points_20250418184706"], "13": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "1744973107": ["登录功能_example_test_points_20250418184706"], "输入正确的用户名": ["登录功能_example_test_points_20250418184706"], "记住密码功能": ["登录功能_example_test_points_20250418184706"], "系统应该正常处理": ["登录功能_example_test_points_20250418184706"], "账号锁定后解锁": ["登录功能_example_test_points_20250418184706"], "应该自动解锁": ["登录功能_example_test_points_20250418184706"], "11": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "同一账号在不同设备同时登录": ["登录功能_example_test_points_20250418184706"], "14": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心标签满减POS应用_20250421142619"], "下次打开登录页面应该自动填充用户名和密码": ["登录功能_example_test_points_20250418184706"], "忘记密码功能": ["登录功能_example_test_points_20250418184706"], "系统应该能正常处理或给出合理提示": ["登录功能_example_test_points_20250418184706"], "请输入密码": ["登录功能_example_test_points_20250418184706"], "输入正确的用户名和错误的密码": ["登录功能_example_test_points_20250418184706"], "10": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "15": ["登录功能_example_test_points_20250418184706"], "测试点列表": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "无需重新登录": ["登录功能_example_test_points_20250418184706"], "特殊字符用户名登录": ["登录功能_example_test_points_20250418184706"], "不输入密码": ["登录功能_example_test_points_20250418184706"], "如50个字符": ["登录功能_example_test_points_20250418184706"], "错误的密码登录": ["登录功能_example_test_points_20250418184706"], "输入不存在的用户名和任意密码": ["登录功能_example_test_points_20250418184706"], "不输入用户名": ["登录功能_example_test_points_20250418184706"], "空用户名登录": ["登录功能_example_test_points_20250418184706"], "文件类型": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "点击登录按钮": ["登录功能_example_test_points_20250418184706"], "选项": ["登录功能_example_test_points_20250418184706"], "2588677": ["登录功能_example_test_points_20250418184706"], "应该保持登录状态": ["登录功能_example_test_points_20250418184706"], "密码错误": ["登录功能_example_test_points_20250418184706"], "账号已锁定": ["登录功能_example_test_points_20250418184706"], "错误的用户名登录": ["登录功能_example_test_points_20250418184706"], "登录超时": ["登录功能_example_test_points_20250418184706"], "点击": ["登录功能_example_test_points_20250418184706"], "忘记密码": ["登录功能_example_test_points_20250418184706"], "sql注入测试": ["登录功能_example_test_points_20250418184706"], "记住密码": ["登录功能_example_test_points_20250418184706"], "账号应该被锁定": ["登录功能_example_test_points_20250418184706"], "文件信息": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "16": ["登录功能_example_test_points_20250418184706"], "系统应该允许多设备同时在线": ["登录功能_example_test_points_20250418184706"], "登录会话保持": ["登录功能_example_test_points_20250418184706"], "连续输入错误密码5次": ["登录功能_example_test_points_20250418184706"], "example_test_points": ["登录功能_example_test_points_20250418184706"], "登录成功后": ["登录功能_example_test_points_20250418184706"], "链接": ["登录功能_example_test_points_20250418184706"], "请输入用户名": ["登录功能_example_test_points_20250418184706"], "测试点": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "不出现异常": ["登录功能_example_test_points_20250418184706"], "系统应该自动退出登录": ["登录功能_example_test_points_20250418184706"], "输入任意密码": ["登录功能_example_test_points_20250418184706"], "成功登录后": ["登录功能_example_test_points_20250418184706"], "系统应该提示": ["登录功能_example_test_points_20250418184706"], "系统应该跳转到密码重置页面": ["登录功能_example_test_points_20250418184706"], "系统应该成功登录并跳转到首页": ["登录功能_example_test_points_20250418184706"], "正确的用户名和密码登录": ["登录功能_example_test_points_20250418184706"], "输入正确的用户名和密码": ["登录功能_example_test_points_20250418184706"], "txt": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "可以正常登录": ["登录功能_example_test_points_20250418184706"], "使用包含特殊字符的用户名": ["登录功能_example_test_points_20250418184706"], "登录状态保持30分钟不操作": ["登录功能_example_test_points_20250418184706"], "使用超长的用户名和密码": ["登录功能_example_test_points_20250418184706"], "长用户名和密码登录": ["登录功能_example_test_points_20250418184706"], "同时登录多个设备": ["登录功能_example_test_points_20250418184706"], "12": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "导入时间": ["登录功能_example_test_points_20250418184706", "满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "空密码登录": ["登录功能_example_test_points_20250418184706"], "关闭浏览器再打开": ["登录功能_example_test_points_20250418184706"], "登录功能测试点": ["登录功能_example_test_points_20250418184706"], "勾选": ["登录功能_example_test_points_20250418184706"], "在用户名或密码中输入sql注入语句": ["登录功能_example_test_points_20250418184706"], "请30分钟后再试": ["登录功能_example_test_points_20250418184706"], "用户名不存在": ["登录功能_example_test_points_20250418184706"], "并提示": ["登录功能_example_test_points_20250418184706"], "功能概述": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员3勋章ab都有": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "前置条件": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436", "物业接口_欢乐颂店物业接口验证_20250425172645"], "pos端有勋章的会员应用可命中满减": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "账单金额20元": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "账单金额10元": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "会员1有勋章a": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "营销中心勋章满减pos应用": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "1744873380": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "已点餐": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群勋章a": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "会员2有勋章b": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "测试场景": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "满20减1": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "会员信息如下": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "账单金额80元": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群勋章b": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "账单金额50元": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "1857572": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "活动命中情况": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群全部勋章": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "活动3": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "活动2": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "活动1": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "应用不同会员": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员4无勋章": ["满减活动_营销中心勋章满减POS应用_20250421120308"], "满30减3": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "账单金额30元": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "满50减5": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "营销中心已设置满减活动": ["满减活动_营销中心勋章满减POS应用_20250421120308", "满减活动_营销中心标签满减POS应用_20250421142619"], "营销中心标签满减pos应用": ["满减活动_营销中心标签满减POS应用_20250421142619"], "自动命中最大优惠": ["满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "测试场景及预期结果": ["满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员2有标签b": ["满减活动_营销中心标签满减POS应用_20250421142619"], "默认会员标签可正常获取": ["满减活动_营销中心标签满减POS应用_20250421142619"], "仅功能验证": ["满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "存在多个满减时": ["满减活动_营销中心标签满减POS应用_20250421142619"], "1744873639": ["满减活动_营销中心标签满减POS应用_20250421142619"], "适用人群标签b": ["满减活动_营销中心标签满减POS应用_20250421142619"], "不考虑满减与其他优惠共享场景": ["满减活动_营销中心标签满减POS应用_20250421142619"], "会员3标签ab都有": ["满减活动_营销中心标签满减POS应用_20250421142619"], "适用人群全部标签": ["满减活动_营销中心标签满减POS应用_20250421142619"], "适用人群标签a": ["满减活动_营销中心标签满减POS应用_20250421142619"], "会员4无标签": ["满减活动_营销中心标签满减POS应用_20250421142619"], "413975": ["满减活动_营销中心标签满减POS应用_20250421142619"], "会员1有标签a": ["满减活动_营销中心标签满减POS应用_20250421142619"], "其他说明": ["满减活动_营销中心标签满减POS应用_20250421142619", "满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "pos端有标签的会员命中满减活动": ["满减活动_营销中心标签满减POS应用_20250421142619"], "适用人群vip会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "营销中心会员等级满折pos应用": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "2061036": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员1为普通会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员2为vip会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "pos端不同等级会员命中满打折活动": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "不考虑满打折与其他优惠共享场景": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "1745301850": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群普通会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "账单金额100元": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "存在多个满打折时": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "满100打80折": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "账单金额300元": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群钻石会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "满100打85折": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "会员3为钻石会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "活动4": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "账单金额200元": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "满100打90折": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "营销中心已设置满打折活动": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "满300打75折": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "适用人群全部会员": ["满减活动_营销中心会员等级满折POS应用_20250423144341", "满减活动_营销中心会员等级满折POS应用_20250423153436"], "现金": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "物业参数已正常配置": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "不需要传菜品详细信息": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "需线下手动处理": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "账单有优惠金额": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "上传时优惠金额固定0": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "上传要求": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "数据上传记录写入数据库表中": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "微信": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "9378176": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "正常账单类型sale和退单类型onlinerefund": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "挂账": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "会自动重试3次": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "功能描述": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "测试场景如下": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "要传付款列表明确各付款方式的金额值": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "账单金额直接传实收": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "欢乐颂店物业接口验证": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "账单付款场景": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "补充说明": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "1744960857": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "以上各种付款场景退单": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "数据上传物业": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "每次都失败将不再上传": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "系统支持传48小时内账单": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "菜品信息传空数组即可": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "找零": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "优惠": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "支付宝": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "同时有日志记录": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "订单时间格式": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "以上各种付款场景": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "不考虑物业测试环境和性能测试": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "网络中断或者物业不可用时": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "服务支持接口上传": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "不同的支付方式组合": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "0金额账单": ["物业接口_欢乐颂店物业接口验证_20250425172645"], "yyyymmddhhmmss": ["物业接口_欢乐颂店物业接口验证_20250425172645"]}}
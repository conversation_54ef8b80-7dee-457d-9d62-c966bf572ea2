# 数据库文档生成器

这是一个用于自动生成MySQL/PostgreSQL数据库文档的工具。它可以生成包含表结构、字段、索引和外键等信息的markdown格式文档。

## 功能特点

- 支持MySQL和PostgreSQL数据库
- 生成markdown格式的数据库文档
- 包含表结构、字段、索引和外键信息
- 支持指定要生成文档的表
- 支持忽略不需要生成文档的表
- 自动生成表格式的文档，便于阅读
- 支持生成DDL格式的文档，方便数据库结构复制
- 支持PostgreSQL的schema指定
- 支持自定义输出目录，方便文档管理

## 安装

```bash
pip install -r requirements.txt
```

## 使用方法

1. 创建配置文件 `config.json`：

```json
{
    "db_type": "mysql",
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "password",
    "database": "your_database",
    "schema": "public",  // 可选，PostgreSQL数据库schema，默认为public
    "output_file": "database_doc.md",
    "output_type": "table",  // 可选，输出格式，支持'table'或'ddl'，默认为'table'
    "input_tables": ["table1", "table2"],  // 可选，指定要生成文档的表
    "ignore_tables": ["log_table", "temp_table"],  // 可选，指定要忽略的表
    "output_dir": "/absolute/path/to/output/dir"  // 可选，指定输出目录，默认为项目根目录下的'_agent-local/knowledge/db'
}
```

## 运行工具

```bash
python db_doc_generator.py config.json
```

## 配置说明

| 字段          | 类型    | 必填 | 说明                                                          |
| ------------- | ------- | ---- | ------------------------------------------------------------- |
| db_type       | string  | 是   | 数据库类型，支持 'mysql' 或 'postgresql'                      |
| host          | string  | 是   | 数据库主机地址                                                |
| port          | integer | 否   | 数据库端口，默认MySQL为3306，PostgreSQL为5432                 |
| user          | string  | 是   | 数据库用户名                                                  |
| password      | string  | 是   | 数据库密码                                                    |
| database      | string  | 是   | 数据库名称                                                    |
| schema        | string  | 否   | PostgreSQL数据库schema，默认为'public'                        |
| output_file   | string  | 是   | 输出文件名                                                    |
| output_type   | string  | 否   | 输出格式，支持'table'或'ddl'，默认为'table'                   |
| input_tables  | array   | 否   | 要生成文档的表名列表，为空时生成所有表的文档                  |
| ignore_tables | array   | 否   | 要忽略的表名列表，这些表不会出现在文档中                      |
| output_dir    | string  | 否   | 指定输出目录，默认为项目根目录下的'_agent-local/knowledge/db' |

## 输出格式

### 表格式 (output_type: "table")

生成的markdown文档包含以下内容：

1. 数据库基本信息
2. 表清单（包含表名、说明、约估行数）
3. 每个表的详细信息：
   - 表说明
   - 字段列表（字段名、类型、必填、默认值、说明）
   - 索引列表（索引名、唯一索引、字段列表）
   - 外键信息（如果有）

### DDL格式 (output_type: "ddl")

生成的markdown文档包含以下内容：

1. 数据库基本信息
2. 表清单（包含表名、说明、约估行数）
3. 每个表的DDL语句（可直接用于创建表）

## 注意事项

1. 确保数据库用户有足够的权限读取表结构信息
2. 对于大型数据库，建议使用input_tables参数指定需要生成文档的表
3. 使用ignore_tables参数可以排除不需要文档的表（如日志表、临时表等）
4. 生成的文档不包含表数据，只包含表结构信息
5. PostgreSQL用户需要确保指定了正确的schema
6. DDL格式适合用于数据库结构迁移或复制
7. 文档默认生成在项目根目录的`_agent-local/knowledge/db`目录下，可通过output_dir参数自定义

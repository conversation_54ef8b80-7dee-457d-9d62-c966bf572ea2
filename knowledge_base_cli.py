#!/usr/bin/env python
"""
测试点知识库命令行工具
用于快速导入和检索知识库
"""

import os
import sys
import argparse
import json
from pathlib import Path
from datetime import datetime

from knowledge_base import KnowledgeBaseManager

def import_document(args):
    """导入文档到知识库"""
    kb = KnowledgeBaseManager()
    
    if os.path.isdir(args.path):
        # 导入目录下的所有文档
        count = 0
        for ext in ['.txt', '.md']:
            for file in Path(args.path).glob(f"*{ext}"):
                try:
                    doc_id = kb.add_document(str(file), category=args.category)
                    print(f"已导入: {file.name} -> {doc_id}")
                    count += 1
                except Exception as e:
                    print(f"导入失败: {file.name} - {str(e)}")
        
        print(f"共导入 {count} 个文档")
    else:
        # 导入单个文档
        try:
            doc_id = kb.add_document(args.path, category=args.category)
            print(f"已导入: {Path(args.path).name} -> {doc_id}")
        except Exception as e:
            print(f"导入失败: {args.path} - {str(e)}")
            sys.exit(1)

def search_documents(args):
    """搜索知识库"""
    kb = KnowledgeBaseManager()
    
    results = kb.search(args.query, category=args.category, limit=args.limit)
    
    if not results:
        print(f"未找到匹配 '{args.query}' 的文档")
        return
    
    print(f"找到 {len(results)} 个匹配的文档:")
    for i, result in enumerate(results, 1):
        print(f"{i}. {result['title']} (ID: {result['id']})")
        print(f"   分类: {result['category']}")
        print(f"   相关度: {result['score']}")
        print(f"   添加时间: {result['added_at']}")
        print()
    
    if args.show_content and results:
        # 显示第一个结果的内容
        doc = kb.get_document(results[0]['id'])
        if doc:
            print("=" * 50)
            print(f"文档内容: {doc['title']}")
            print("=" * 50)
            print(doc['content'][:500] + "..." if len(doc['content']) > 500 else doc['content'])
            print("\n(使用 --id 选项查看完整文档)")

def show_document(args):
    """显示文档内容"""
    kb = KnowledgeBaseManager()
    
    doc = kb.get_document(args.id)
    if not doc:
        print(f"未找到ID为 '{args.id}' 的文档")
        return
    
    print("=" * 50)
    print(f"文档: {doc['title']} (ID: {doc['id']})")
    print(f"分类: {doc['category']}")
    print(f"添加时间: {doc['added_at']}")
    print("=" * 50)
    print(doc['content'])

def list_documents(args):
    """列出知识库中的所有文档"""
    kb = KnowledgeBaseManager()
    
    docs = kb.list_documents(category=args.category)
    
    if not docs:
        print("知识库中没有文档" + (f" (分类: {args.category})" if args.category else ""))
        return
    
    print(f"知识库中共有 {len(docs)} 个文档" + (f" (分类: {args.category})" if args.category else ""))
    for i, doc in enumerate(docs, 1):
        print(f"{i}. {doc['title']} (ID: {doc['id']})")
        print(f"   分类: {doc['category']}")
        print(f"   添加时间: {doc['added_at']}")
        print()

def delete_document(args):
    """删除文档"""
    kb = KnowledgeBaseManager()
    
    if args.id:
        # 删除指定ID的文档
        if kb.delete_document(args.id):
            print(f"已删除ID为 '{args.id}' 的文档")
        else:
            print(f"未找到ID为 '{args.id}' 的文档")
    elif args.all and args.category:
        # 删除指定分类的所有文档
        docs = kb.list_documents(category=args.category)
        if not docs:
            print(f"分类 '{args.category}' 中没有文档")
            return
        
        count = 0
        for doc in docs:
            if kb.delete_document(doc['id']):
                count += 1
        
        print(f"已删除分类 '{args.category}' 中的 {count} 个文档")
    elif args.all:
        # 删除所有文档
        docs = kb.list_documents()
        if not docs:
            print("知识库中没有文档")
            return
        
        confirm = input(f"确定要删除所有 {len(docs)} 个文档吗? (y/n): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
        
        count = 0
        for doc in docs:
            if kb.delete_document(doc['id']):
                count += 1
        
        print(f"已删除 {count} 个文档")

def show_stats(args):
    """显示知识库统计信息"""
    kb = KnowledgeBaseManager()
    
    stats = kb.get_statistics()
    
    print("知识库统计信息:")
    print(f"总文档数: {stats['total_documents']}")
    print("分类统计:")
    for category, count in stats['categories'].items():
        print(f"  - {category}: {count}文档")
    print(f"创建时间: {stats['created_at']}")
    print(f"最后更新: {stats['updated_at']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试点知识库命令行工具")
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 导入命令
    import_parser = subparsers.add_parser("import", help="导入文档到知识库")
    import_parser.add_argument("path", help="文档路径或目录路径")
    import_parser.add_argument("--category", "-c", default="general", help="文档分类")
    
    # 搜索命令
    search_parser = subparsers.add_parser("search", help="搜索知识库")
    search_parser.add_argument("query", help="搜索关键词")
    search_parser.add_argument("--category", "-c", help="限定分类")
    search_parser.add_argument("--limit", "-l", type=int, default=10, help="返回结果数量限制")
    search_parser.add_argument("--show-content", "-s", action="store_true", help="显示第一个结果的内容")
    
    # 显示文档命令
    show_parser = subparsers.add_parser("show", help="显示文档内容")
    show_parser.add_argument("id", help="文档ID")
    
    # 列出文档命令
    list_parser = subparsers.add_parser("list", help="列出知识库中的所有文档")
    list_parser.add_argument("--category", "-c", help="限定分类")
    
    # 删除文档命令
    delete_parser = subparsers.add_parser("delete", help="删除文档")
    delete_parser.add_argument("--id", help="文档ID")
    delete_parser.add_argument("--category", "-c", help="限定分类")
    delete_parser.add_argument("--all", "-a", action="store_true", help="删除所有文档")
    
    # 统计命令
    stats_parser = subparsers.add_parser("stats", help="显示知识库统计信息")
    
    args = parser.parse_args()
    
    if args.command == "import":
        import_document(args)
    elif args.command == "search":
        search_documents(args)
    elif args.command == "show":
        show_document(args)
    elif args.command == "list":
        list_documents(args)
    elif args.command == "delete":
        delete_document(args)
    elif args.command == "stats":
        show_stats(args)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()

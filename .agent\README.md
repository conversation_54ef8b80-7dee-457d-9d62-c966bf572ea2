# Agent 3.0 —— AI工程化解决方案


## 环境要求

- Python 3.8+
- pip3

## 快速开始

1. 在某个项目中引入.agent 3.0

```bash
git clone https://gitlab.acewill.cn/ai/agent-dev.git .agent

然后将 .agent 加入项目的 .gitignore 中
```

2. 执行初始化脚本

```bash
cd .agent
# mac/linux
sh init.sh 
# windows
init.bat
```

3. 配置环境变量

初始化完成后，检查并根据需要修改 `.agent/.env` 文件中的配置，确认是否需要修改：

```ini
# 使用 windsurf 时需配置.windsurfrules， 使用cursor时需要改为 .cursorrules
LOCAL_RULES_PATH=.windsurfrules
GLOBAL_RULES_PATH=.windsurfrules

# 项目中工作空间的目录，用于保存项目自定义的工作流、知识库等，不能加入.gitignore, 可自定义目录名称，所有使用该项目的同事需统一配置相同目录名称，该目录下的 task.yml 可配置为git忽略
LOCAL_WORKSPACE=_agent-local

# USER_ID 使用字母
# USER_NAME 必须与gitstat中的姓名一致，gitstat中部分姓名可能与姓名不一致
USER_ID=ypf
USER_NAME=杨鹏飞

```



4. 配置windsurf、cursor全局规则
- 复制.agent/rule/global_rule.md 中的内容复制到AI工具的全局规则中
- 如果是windows，需要将python3 批量替换为 python


windows 系统解决输出中文乱码问题

```bash
# 将以下内容添加到 PowerShell 中的 $PROFILE 配置文件
$env:PYTHONIOENCODING="utf-8"
```


## 指令使用说明

### 角色切换指令

使用以下指令可以切换不同的工作角色：

- `/p` - 切换到 Planner 角色（分析与规划者）
- `/d` - 切换到 Developer 角色（开发者）
- `/r` - 切换到 Reviewer 角色（评审者）
- `/t` - 切换到 Tester 角色（测试者）

### 工作指令

工作管理相关的指令：

- `/work flows` - 查询所有可用的工作流
- `/work list` - 查询我所有进行中和暂停的工作
- `/work use <workflow_name>` - 使用指定工作流创建工作计划，当前进行中的工作流会设置为暂停
- `/work on`   - 继续暂停的工作流
- `/work subtask` - 为当前工作任务创建子任务规划
- `/work now` - 查看当前进行中的工作
- `/work next` - 进行下一项任务或子任务
- `/work back` - 回到上一项任务或子任务继续工作
- `/work clear` - 清空当前工作，以便开始新的工作

### 任务指令

任务管理相关的指令：

- `/task flows` - 查看可用的任务流模板
- `/task use <workflow_name>` - 使用模板启动一个子任务
- `/task update` - 为当前子任务创建任务目标
- `/task now` - 查看当前任务目标和步骤
- `/task next` - 当前步骤完成，进入下一步
- `/task reset` - 任务目标失败，回到最初步骤重新开始
- `/task clear` - 删除当前任务
- `/task memory <JSON_STRING> <mode>` - 更新任务记忆
- `/task forget` - 清空任务记忆



## 目录结构

```
.agent/                 # Agent 系统目录
├── commands/           # 命令实现
│   ├── role.py         # 角色管理
│   ├── work.py         # 工作任务管理
│   └── task.py         # 任务步骤管理
├── roles/              # 角色定义
├── rule/               # 规则配置
├── .env                # 环境变量
└── README.md           # 说明文档

_agent-local/          # 项目级配置
├── workspace/         # 工作空间
├── workflows/          # 项目自定义工作流
│    ├──task/          # 工作任务步骤流程模板
│    └──work/          # 工作流程模板
├── knowledge/         # 项目知识库
├── roles/             # 项目自定义角色
└── guidelines/        # 规范指引文件

现有项目代码文件

# 测试用例设计文档

## 测试概述
- 测试目标：验证POS端会员标签满减应用功能的正确性，确保系统能够根据会员标签和消费金额正确应用满减活动。
- 测试范围：覆盖所有会员标签组合（无标签、单一标签、多标签）和账单金额场景（低于、等于、高于满减条件）。
- 测试策略：采用黑盒测试方法，基于需求规格进行功能测试和边界值测试。
- 测试环境：POS系统测试环境，会员系统测试数据，满减活动配置权限。

## 测试用例列表
### POS端会员标签满减应用 测试用例

#### TC-001: 无标签会员不能享受满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员4无标签

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为20元
  2. 应用会员4信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持20元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员4（无标签）
  - 账单金额: 20元

#### TC-002: 无标签会员不能享受满减活动（高金额）
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员4无标签

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为50元
  2. 应用会员4信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持50元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员4（无标签）
  - 账单金额: 50元

#### TC-003: 会员1（标签A）账单金额低于满减条件时不应用满减
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员1有标签A

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为10元
  2. 应用会员1信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持10元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员1（标签A）
  - 账单金额: 10元

#### TC-004: 会员2（标签B）账单金额低于满减条件时不应用满减
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员2有标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为10元
  2. 应用会员2信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持10元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员2（标签B）
  - 账单金额: 10元

#### TC-005: 会员1（标签A）享受对应满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员1有标签A

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为30元
  2. 应用会员1信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动1（满30减3）
  2. 账单金额从30元减少到27元

- **测试数据**：
  - 会员信息: 会员1（标签A）
  - 账单金额: 30元

#### TC-006: 会员2（标签B）享受对应满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员2有标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为50元
  2. 应用会员2信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动2（满50减5）
  2. 账单金额从50元减少到45元

- **测试数据**：
  - 会员信息: 会员2（标签B）
  - 账单金额: 50元

#### TC-007: 会员1（标签A）享受全部标签适用的满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员1有标签A

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为20元
  2. 应用会员1信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动3（满20减1）
  2. 账单金额从20元减少到19元

- **测试数据**：
  - 会员信息: 会员1（标签A）
  - 账单金额: 20元

#### TC-008: 会员2（标签B）享受全部标签适用的满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员2有标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为20元
  2. 应用会员2信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动3（满20减1）
  2. 账单金额从20元减少到19元

- **测试数据**：
  - 会员信息: 会员2（标签B）
  - 账单金额: 20元

#### TC-009: 会员3（标签A和B）享受全部标签适用的满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员3有标签A和标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为20元
  2. 应用会员3信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动3（满20减1）
  2. 账单金额从20元减少到19元

- **测试数据**：
  - 会员信息: 会员3（标签A和标签B）
  - 账单金额: 20元

#### TC-010: 会员3（标签A和B）在30元消费时自动命中最优满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员3有标签A和标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为30元
  2. 应用会员3信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中最优惠的活动1（满30减3）
  2. 账单金额从30元减少到27元

- **测试数据**：
  - 会员信息: 会员3（标签A和标签B）
  - 账单金额: 30元

#### TC-011: 会员3（标签A和B）在50元消费时自动命中最优满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员3有标签A和标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为50元
  2. 应用会员3信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中最优惠的活动2（满50减5）
  2. 账单金额从50元减少到45元

- **测试数据**：
  - 会员信息: 会员3（标签A和标签B）
  - 账单金额: 50元

#### TC-012: 会员1（标签A）账单金额恰好等于满减活动最低消费金额（边界值测试）
- **优先级**：中
- **测试类型**：边界值测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员1有标签A

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为30.00元（恰好等于满减条件）
  2. 应用会员1信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动1（满30减3）
  2. 账单金额从30.00元减少到27.00元

- **测试数据**：
  - 会员信息: 会员1（标签A）
  - 账单金额: 30.00元

#### TC-013: 会员2（标签B）账单金额恰好等于满减活动最低消费金额（边界值测试）
- **优先级**：中
- **测试类型**：边界值测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员2有标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为50.00元（恰好等于满减条件）
  2. 应用会员2信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动2（满50减5）
  2. 账单金额从50.00元减少到45.00元

- **测试数据**：
  - 会员信息: 会员2（标签B）
  - 账单金额: 50.00元

#### TC-014: 会员4（无标签）账单金额为10元（低于所有满减条件）
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员4无标签

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为10元
  2. 应用会员4信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持10元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员4（无标签）
  - 账单金额: 10元

#### TC-015: 会员3（标签A和B）账单金额为10元（低于所有满减条件）
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员3有标签A和标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为10元
  2. 应用会员3信息
  3. 观察系统是否应用满减活动

- **预期结果**：
  1. 系统提示无可用满减活动
  2. 账单金额保持10元不变，不享受任何满减

- **测试数据**：
  - 会员信息: 会员3（标签A和标签B）
  - 账单金额: 10元

#### TC-016: 会员3（标签A和B）账单金额为20元（恰好等于全部标签满减条件）
- **优先级**：中
- **测试类型**：边界值测试
- **前置条件**：
  - 营销中心已设置满减活动：活动1（满30减3，适用人群标签A）、活动2（满50减5，适用人群标签B）、活动3（满20减1，适用人群全部标签）
  - 会员3有标签A和标签B

- **测试步骤**：
  1. 在POS端完成点餐，账单金额为20.00元（恰好等于满减条件）
  2. 应用会员3信息
  3. 观察系统应用的满减活动

- **预期结果**：
  1. 系统自动命中活动3（满20减1）
  2. 账单金额从20.00元减少到19.00元

- **测试数据**：
  - 会员信息: 会员3（标签A和标签B）
  - 账单金额: 20.00元

## 测试覆盖率分析
- 功能覆盖率：100%（覆盖了所有功能点和业务规则）
- 业务规则覆盖率：100%（覆盖了所有业务规则BR-001至BR-007）
  - BR-001：满减活动根据会员标签判断适用性（TC-001至TC-011）
  - BR-002：满减活动1：满30减3，适用人群标签A（TC-005, TC-010, TC-012）
  - BR-003：满减活动2：满50减5，适用人群标签B（TC-006, TC-011, TC-013）
  - BR-004：满减活动3：满20减1，适用人群全部标签（TC-007, TC-008, TC-009, TC-016）
  - BR-005：无标签会员不能享受满减活动（TC-001, TC-002, TC-014）
  - BR-006：存在多个满减时，自动命中最大优惠（TC-010, TC-011）
  - BR-007：不考虑满减与其他优惠共享场景（所有测试用例）
- 边界条件覆盖率：100%（覆盖了所有边界条件）
  - 账单金额恰好等于满减活动的最低消费金额（TC-012, TC-013, TC-016）
  - 会员同时拥有多个标签，符合多个满减活动条件（TC-009, TC-010, TC-011, TC-016）
  - 账单金额低于所有满减活动的最低消费金额（TC-003, TC-004, TC-014, TC-015）
  - 会员没有任何标签（TC-001, TC-002, TC-014）
  - 会员拥有的标签不符合任何满减活动的适用条件（不适用，因为所有有标签的会员都至少符合活动3）

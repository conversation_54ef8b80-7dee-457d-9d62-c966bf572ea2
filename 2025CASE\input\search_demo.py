"""
知识库检索演示
"""
import json
import sys
from pathlib import Path

# 添加知识库模块路径
sys.path.append('D:/0AI/TESTCASE')
from knowledge_base import KnowledgeBaseManager

def main():
    """知识库检索演示"""
    # 读取导入结果
    with open('D:/0AI/TESTCASE/2025CASE/output/import_result.json', 'r', encoding='utf-8') as f:
        import_result = json.load(f)

    document_id = import_result['document_id']
    document_path = import_result['document_path']
    import_time = import_result['import_time']

    # 创建知识库管理器
    kb = KnowledgeBaseManager()

    # 定义示例查询关键词
    search_examples = [
        "物业接口",
        "账单付款",
        "上传要求",
        "网络中断",
        "测试场景"
    ]

    # 执行查询并收集结果
    search_results = []
    for keyword in search_examples:
        results = kb.search(keyword)
        search_results.append({
            "查询关键词": keyword,
            "结果": [
                {
                    "title": result.get("metadata", {}).get("title", "未知标题"),
                    "score": result.get("score", 0),
                    "category": result.get("metadata", {}).get("category", "未分类"),
                    "added_at": result.get("metadata", {}).get("added_at", "未知时间")
                }
                for result in results
            ]
        })

    # 获取完整文档
    document = kb.get_document(document_id)

    # 生成Markdown报告
    markdown_content = f"""# 知识库检索演示

## 导入信息
- 文档ID: {document_id}
- 文档路径: {document_path}
- 导入时间: {import_time}

## 检索示例
"""

    for example in search_results:
        markdown_content += f"""### 示例: {example['查询关键词']}

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("{example['查询关键词']}")
```

**检索结果:**
"""

        if example['结果']:
            for result in example['结果']:
                markdown_content += f"""- 文档: {result['title']}
  - 相关度: {result['score']}
  - 分类: {result['category']}
  - 添加时间: {result['added_at']}
"""
        else:
            markdown_content += "未找到匹配的结果\n"

    markdown_content += """
## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")
```

### 检索文档
```python
# 关键词搜索
results = kb.search("物业接口")

# 按分类搜索
results = kb.search("账单付款", category="物业接口")

# 限制结果数量
results = kb.search("上传要求", limit=5)
```

### 查看文档
```python
doc = kb.get_document("doc_id")
print(doc["title"])
print(doc["content"])
```

### 列出所有文档
```python
all_docs = kb.list_documents()
for doc in all_docs:
    print(f"{doc['id']}: {doc['title']}")
```

### 删除文档
```python
kb.delete_document("doc_id")
```
"""

    # 保存Markdown报告
    output_path = Path("D:/0AI/TESTCASE/2025CASE/output/search_demo.md")
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(markdown_content)

    print(f"检索演示报告已保存到: {output_path}")

if __name__ == "__main__":
    main()

#!/bin/bash

# 确保脚本在.agent目录下执行
if [[ ! -f ".env.tpl" ]]; then
    echo "错误：请在 .agent 目录下运行此脚本"
    exit 1
fi

# 如果.env文件不存在，从.env.tpl创建
if [[ ! -f ".env" ]]; then
    echo "未找到.env文件，将从.env.tpl创建..."
    cp .env.tpl .env
    echo ".env文件已创建"
fi

# 检查Python3是否安装
if ! command -v python3 &> /dev/null; then
    echo "未找到 Python3，正在安装..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        brew install python3
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        sudo apt-get update
        sudo apt-get install -y python3
    else
        echo "不支持的操作系统"
        exit 1
    fi
fi

# 安装pip依赖
echo "正在安装 Python 依赖..."
python3 -m pip install -r requirements.txt

# 读取并确认.env文件中的LOCAL_WORKSPACE变量
if [[ -f ".env" ]]; then
    source .env
    local_workspace_dir="${LOCAL_WORKSPACE}"

    # 显示当前配置并请求用户确认
    echo "当前 .env 中配置的 LOCAL_WORKSPACE 为: ${local_workspace_dir}"
    echo "将在以下路径创建/更新目录结构: ../${local_workspace_dir}/"
    read -p "是否继续使用此路径？(y/n): " confirm

    if [[ $confirm != "y" && $confirm != "Y" ]]; then
        echo "操作已被用户取消"
        exit 1
    fi
else
    echo "错误：未找到 .env 文件"
    exit 1
fi

# 创建目录结构
echo "正在创建目录结构..."
mkdir -p "../${local_workspace_dir}/workspace"
mkdir -p "../${local_workspace_dir}/workflows/task"
mkdir -p "../${local_workspace_dir}/workflows/work"
mkdir -p "../${local_workspace_dir}/knowledge"
mkdir -p "../${local_workspace_dir}/roles"
mkdir -p "../${local_workspace_dir}/guidelines"

# 更新 .gitignore 文件
echo "正在更新 .gitignore 文件..."
gitignore_file="../.gitignore"
workspace_ignore="${local_workspace_dir}/workspace/"
cursorrules_ignore=".cursorrules"

# 检查并添加 workspace 忽略规则
if ! grep -q "^${workspace_ignore}$" "${gitignore_file}" 2>/dev/null; then
    echo "${workspace_ignore}" >> "${gitignore_file}"
    echo "已将 ${workspace_ignore} 添加到 ${gitignore_file}"
fi

# 检查并添加 .cursorrules 忽略规则
if ! grep -q "^${cursorrules_ignore}$" "${gitignore_file}" 2>/dev/null; then
    echo "${cursorrules_ignore}" >> "${gitignore_file}"
    echo "已将 ${cursorrules_ignore} 添加到 ${gitignore_file}"
fi

echo "初始化完成！"

id: jiaojingjing_md_to_xmind_0529_18_25
name: md_to_xmind
description: 将已有的MD格式测试用例文档直接转换为XMIND思维导图
rules: []
status: pending
user_id: jiaojingjing
user_name: 焦晶晶
tasks:
  - name: XMIND思维导图生成
    description: 将MD格式的测试用例文档转换为XMIND思维导图
    role: tester
    input:
      - doc: 小程序企业团餐用例.md
    output:
      - file: ${work_path}/小程序企业团餐用例1.xmind
        template: "# 小程序企业团餐测试用例\n\n## 中心主题\n* 小程序企业团餐测试用例\n\n## 主要功能模块\n### 1. 企业团餐支付配置\n\
          #### 1.1 企业团餐付款方式配置\n* 正常场景\n  * 前置条件：系统已安装并正常运行，用户具有配置权限\n  * 测试步骤：登录系统，进入付款方式配置页面，验证付款类型、来源和绑定中台支付方式\n\
          \  * 预期结果：付款类型为\"第三方付款\"，付款来源为\"企业团餐\"，绑定中台支付方式为\"企餐余额支付\"\n* 异常场景\n  *\
          \ 前置条件：系统已安装并正常运行，用户具有配置权限\n  * 测试步骤：登录系统，进入付款方式配置页面，验证错误配置情况\n  * 预期结果：系统提示配置错误，不允许转单\n\
          \n#### 1.2 企餐优惠付款方式配置\n* 正常场景\n  * 前置条件：系统已安装并正常运行，用户具有配置权限\n  * 测试步骤：登录系统，进入付款方式配置页面，验证付款类型和收入计算\n\
          \  * 预期结果：付款类型为\"第三方付款\"，是否计收入为\"否\"\n* 异常场景\n  * 前置条件：系统已安装并正常运行，用户具有配置权限\n\
          \  * 测试步骤：登录系统，进入付款方式配置页面，验证错误配置情况\n  * 预期结果：系统提示配置错误，不允许转单\n\n#### 1.3\
          \ 企餐优惠优惠方式配置\n* 正常场景\n  * 前置条件：系统已安装并正常运行，用户具有配置权限\n  * 测试步骤：登录系统，进入优惠方式配置页面，验证优惠方式设置\n\
          \  * 预期结果：绑定中台优惠方式为\"企餐优惠\"，优惠属性为\"不计收入优惠\"\n* 异常场景\n  * 前置条件：系统已安装并正常运行，用户具有配置权限\n\
          \  * 测试步骤：登录系统，进入优惠方式配置页面，验证错误配置情况\n  * 预期结果：系统提示配置错误，不允许转单\n\n### 2. 企业团餐订单处理\n\
          #### 2.1 菜品存在订单转单\n* 正常场景\n  * 前置条件：系统已安装并正常运行，菜品编码与门店对应\n  * 测试步骤：小程序下单，观察中台和POS接收情况\n\
          \  * 预期结果：订单成功转单，菜品信息正确匹配\n* 异常场景\n  * 前置条件：系统已安装并正常运行，菜品编码与门店对应但价格不匹配\n\
          \  * 测试步骤：小程序下单，观察中台和POS接收情况\n  * 预期结果：系统提示价格不匹配，不允许转单\n\n#### 2.2 菜品不存在订单转单\n\
          * 正常场景\n  * 前置条件：系统已安装并正常运行\n  * 测试步骤：小程序下单（使用不存在的菜品编码）\n  * 预期结果：系统按传值转单，订单正常处理\n\
          * 异常场景\n  * 前置条件：系统已安装并正常运行\n  * 测试步骤：小程序下单（使用不存在的菜品编码且金额异常）\n  * 预期结果：系统检测到金额异常，不允许转单\n\
          \n### 3. 企业团餐订单退款\n#### 3.1 实时订单退单\n* 正常场景\n  * 前置条件：存在可退单的实时订单\n  * 测试步骤：选择订单并执行退单操作\n\
          \  * 预期结果：成功调用三合一退单接口，退单完成\n* 异常场景\n  * 前置条件：存在可退单的实时订单，三合一退单接口返回错误\n  *\
          \ 测试步骤：选择订单并执行退单操作\n  * 预期结果：系统提示退单失败，退单操作未完成\n\n#### 3.2 历史订单退单限制\n* 正常场景\n\
          \  * 前置条件：存在历史订单，默认不允许跨天退单\n  * 测试步骤：选择历史订单并尝试退单\n  * 预期结果：系统显示禁止跨天退单提示，退单被阻止\n\
          * 异常场景\n  * 前置条件：存在历史订单，系统时间异常\n  * 测试步骤：修改系统时间并尝试退单\n  * 预期结果：系统检测到时间异常，退单被阻止\n\
          \n#### 3.3 允许跨天退单功能\n* 正常场景\n  * 前置条件：存在历史订单，已设置允许跨天退单\n  * 测试步骤：选择历史订单并执行退单操作\n\
          \  * 预期结果：成功调用三合一退单接口，退单完成\n* 异常场景\n  * 前置条件：存在历史订单，三合一退单接口返回错误\n  * 测试步骤：选择历史订单并执行退单操作\n\
          \  * 预期结果：系统提示退单失败，退单操作未完成\n\n#### 3.4 部分退款限制\n* 正常场景\n  * 前置条件：存在可退单的订单\n\
          \  * 测试步骤：尝试执行部分退款操作\n  * 预期结果：系统显示禁止部分退款提示，操作被阻止\n* 异常场景\n  * 前置条件：存在可退单的订单，订单金额异常\n\
          \  * 测试步骤：尝试执行部分退款操作\n  * 预期结果：系统提示订单金额异常，操作被阻止\n\n### 4. 企业团餐订单展示\n####\
          \ 4.1 KVS订单展示\n* 正常场景\n  * 前置条件：KVS显示屏已连接，存在企业团餐订单\n  * 测试步骤：创建订单并观察KVS显示\n\
          \  * 预期结果：KVS正确显示订单信息\n* 异常场景\n  * 前置条件：KVS显示屏连接异常\n  * 测试步骤：创建订单并观察KVS显示\n\
          \  * 预期结果：系统提示KVS连接异常，订单信息未显示\n\n#### 4.2 KVS参数设置\n* 正常场景\n  * 前置条件：用户具有配置权限\n\
          \  * 测试步骤：配置企业团餐渠道显示设置\n  * 预期结果：设置成功，KVS显示屏相应更新\n* 异常场景\n  * 前置条件：用户具有配置权限，KVS显示屏连接异常\n\
          \  * 测试步骤：配置企业团餐渠道显示设置\n  * 预期结果：设置保存成功但未生效，系统提示连接异常\n\n### 5. 企业团餐堂食和打包\n\
          #### 5.1 堂食订单处理\n* 渠道验证\n  * 前置条件：系统正常运行，已配置企业团餐功能\n  * 测试步骤：创建堂食订单并查看转单记录\n\
          \  * 预期结果：渠道显示为QYTC32，销售模式为XSMS_TS\n* 取餐号验证\n  * 前置条件：系统正常运行，已配置企业团餐功能\n\
          \  * 测试步骤：创建堂食订单并查看取餐号\n  * 预期结果：取餐号格式为[企餐01]，连续递增\n\n#### 5.2 自提订单处理\n\
          * 渠道验证\n  * 前置条件：系统正常运行，已配置企业团餐功能\n  * 测试步骤：创建自提订单并查看转单记录\n  * 预期结果：渠道显示为QYTC32，销售模式为XSMS_WM\n\
          * 取餐号验证\n  * 前置条件：系统正常运行，已配置企业团餐功能\n  * 测试步骤：创建自提订单并查看取餐号\n  * 预期结果：取餐号格式为[企餐自提02]，连续递增\n\
          \n#### 5.3 KVS显示验证\n* 堂食订单显示\n  * 前置条件：系统正常运行，KVS系统正常运行\n  * 测试步骤：创建堂食订单并查看KVS显示\n\
          \  * 预期结果：KVS正确显示取餐号[企餐01]\n* 自提订单显示\n  * 前置条件：系统正常运行，KVS系统正常运行\n  * 测试步骤：创建自提订单并查看KVS显示\n\
          \  * 预期结果：KVS正确显示取餐号[企餐自提02]\n\n#### 5.4 小票打印验证\n* 堂食订单小票\n  * 前置条件：系统正常运行，小票打印机正常工作\n\
          \  * 测试步骤：创建堂食订单并打印小票\n  * 预期结果：小票正确显示取餐号[企餐01]\n* 自提订单小票\n  * 前置条件：系统正常运行，小票打印机正常工作\n\
          \  * 测试步骤：创建自提订单并打印小票\n  * 预期结果：小票正确显示取餐号[企餐自提02] "
    status: todo
work_path: D:\0AI\TESTCASE\_agent-local\workspace\jiaojingjing_md_to_xmind_0529_18_25\work.yml
# POS端菜品设置图片信息测试用例

## 需求分析

### 功能概述
POS端菜品设置图片信息功能允许用户在POS系统中为菜品设置和管理图片，主要位于系统设置-查看餐谱设定中的餐类明细设定页签。

### 功能点分析

#### 1. 图片设置权限控制
- **功能描述**：仅餐类明细设定页签允许设置图片，其他页签只允许查看
- **优先级**：高
- **依赖关系**：系统权限管理
- **实现复杂度**：低

#### 2. 餐类和菜品明细展示
- **功能描述**：餐类明细设定页签左侧展示餐类，右侧展示对应类下菜品明细
- **优先级**：高
- **依赖关系**：数据库中的餐类和菜品数据
- **实现复杂度**：中

#### 3. 图片设置条件控制
- **功能描述**：只有选中菜品明细才允许设置图片，仅选中餐类不允许设置图片
- **优先级**：高
- **依赖关系**：UI交互逻辑
- **实现复杂度**：低

#### 4. 图片采样页面展示
- **功能描述**：选中菜品明细后自动弹出图片采样页面
- **优先级**：高
- **依赖关系**：UI交互逻辑
- **实现复杂度**：中

#### 5. 未设置图片的菜品采样页面
- **功能描述**：未设置过图片的菜品，图片采样页面只有【采样】和【返回】按钮可操作，其他信息仅供查看
- **优先级**：高
- **依赖关系**：图片数据存储
- **实现复杂度**：中

#### 6. 已设置图片的菜品采样页面
- **功能描述**：已设置过图片的菜品，图片采样页面展示已上传图片信息，每张图片下有可操作的【删除】按钮
- **优先级**：高
- **依赖关系**：图片数据存储
- **实现复杂度**：中

#### 7. 商品采集页面
- **功能描述**：点击【采集】按钮弹出【商品采集】页面，包含采样、全选、反选、移除、上传、保存图片、关闭等功能
- **优先级**：高
- **依赖关系**：图片处理功能
- **实现复杂度**：高

### 测试策略
- **测试范围**：功能验证测试，不包括性能和安全性测试
- **测试优先级**：按功能点重要性排序
- **测试环境要求**：POS系统测试环境

## 测试用例列表

### TC-001: 验证餐类明细设定页签图片设置权限
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定

**测试步骤**：
1. 进入系统设置-查看餐谱设定
2. 依次点击各个页签：餐谱设定、餐别设定、餐类设定、餐类明细设定、功能按钮设定、付款方式设定
3. 在每个页签中尝试设置图片功能

**预期结果**：
1. 仅在餐类明细设定页签中可以设置图片
2. 其他页签中无图片设置功能或该功能被禁用

### TC-002: 验证餐类明细设定页签的界面展示
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签

**测试步骤**：
1. 观察页面布局
2. 点击左侧不同餐类

**预期结果**：
1. 左侧展示餐类列表
2. 右侧展示对应餐类下的菜品明细
3. 点击不同餐类时，右侧菜品明细随之更新

### TC-003: 验证仅选中餐类时的图片设置限制
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签

**测试步骤**：
1. 在左侧仅选中一个餐类，不选中右侧任何菜品明细
2. 尝试设置图片

**预期结果**：
1. 系统不允许设置图片
2. 可能会提示用户需要选择菜品明细

### TC-004: 验证选中菜品明细后自动弹出图片采样页面
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签

**测试步骤**：
1. 在左侧选中一个餐类
2. 在右侧选中一个菜品明细

**预期结果**：
1. 系统自动弹出图片采样页面

### TC-005: 验证未设置图片的菜品采样页面功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个未设置过图片的菜品明细

**测试步骤**：
1. 观察弹出的图片采样页面
2. 尝试操作页面上的各个按钮和区域

**预期结果**：
1. 页面上只有【采样】和【返回】按钮可以点击操作
2. 其他展示信息区域仅供查看，不可操作

### TC-006: 验证已设置图片的菜品采样页面功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个已设置过图片的菜品明细

**测试步骤**：
1. 观察弹出的图片采样页面
2. 查看已上传的图片信息
3. 尝试点击图片下方的【删除】按钮

**预期结果**：
1. 【采样】按钮右侧区域展示已上传的图片信息
2. 每张图片下方都有【删除】按钮
3. 点击【删除】按钮可以成功删除对应图片

### TC-007: 验证点击采集按钮弹出商品采集页面
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 已弹出图片采样页面

**测试步骤**：
1. 点击图片采样页面上的【采集】按钮

**预期结果**：
1. 系统弹出【商品采集】页面

### TC-008: 验证商品采集页面功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面

**测试步骤**：
1. 测试【采样】按钮功能
2. 测试【全选】按钮功能
3. 测试【反选】按钮功能
4. 测试【移除】按钮功能
5. 测试【上传】按钮功能
6. 测试【保存图片】按钮功能
7. 测试右上角【X】关闭按钮功能

**预期结果**：
1. 所有按钮功能正常可用
2. 【采样】按钮可以采集图片
3. 【全选】按钮可以选中所有图片
4. 【反选】按钮可以反向选择图片
5. 【移除】按钮可以移除选中的图片
6. 【上传】按钮可以上传选中的图片
7. 【保存图片】按钮可以保存图片
8. 右上角【X】按钮可以关闭商品采集页面

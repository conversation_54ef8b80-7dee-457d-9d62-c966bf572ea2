# 文本解析结果

## 原始文件信息
- 文件名称：欢乐颂店物业接口验证.txt
- 文件类型：txt
- 文件路径：欢乐颂店物业接口验证.txt
- 解析时间：2023-04-19

## 功能点列表
### 欢乐颂店物业接口验证
- 描述：验证欢乐颂店与物业系统之间的接口功能，确保各种账单付款场景和退单场景下的数据能正确上传至物业系统
- 优先级：高
- 相关业务规则：
  - 账单数据上传规则
  - 退单数据上传规则
  - 网络异常处理规则
  - 支付方式组合规则
  - 账单时间限制规则
  - 数据记录规则

### 账单付款场景
- 描述：验证各种支付方式和组合方式下的账单处理
- 优先级：高
- 相关业务规则：
  - 支付方式组合规则
  - 账单数据上传规则
  - 支持多种支付方式：现金、微信、支付宝、挂账
  - 支持组合支付：现金+找零、优惠+现金、优惠+微信、优惠+支付宝、现金+挂账
  - 支持0金额账单处理

### 数据上传物业
- 描述：验证各种付款场景下的数据能否正确上传到物业系统
- 优先级：高
- 相关业务规则：
  - 账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收
  - 菜品信息传空数组，不需要传菜品详细信息
  - 正常账单类型SALE
  - 订单时间格式：yyyyMMddHHmmss

### 退单场景
- 描述：验证各种支付方式和组合方式下的退单处理
- 优先级：高
- 相关业务规则：
  - 退单数据上传规则
  - 支付方式组合规则
  - 退单类型ONLINEREFUND
  - 订单时间格式：yyyyMMddHHmmss

## 业务规则列表
### BR001
- 描述：账单数据上传规则
- 适用范围：所有账单上传场景
- 约束条件：
  - 账单有优惠金额时，上传时优惠金额固定为0，账单金额直接传实收
  - 菜品信息传空数组，不需要传菜品详细信息
  - 正常账单类型为SALE
  - 订单时间格式为yyyyMMddHHmmss

### BR002
- 描述：退单数据上传规则
- 适用范围：所有退单上传场景
- 约束条件：
  - 退单类型为ONLINEREFUND
  - 其他规则同账单数据上传规则

### BR003
- 描述：网络异常处理规则
- 适用范围：所有数据上传场景
- 约束条件：
  - 网络中断或物业不可用时，自动重试3次
  - 每次都失败将不再上传，需线下手动处理

### BR004
- 描述：支付方式组合规则
- 适用范围：所有涉及多种支付方式组合的场景
- 约束条件：
  - 不同的支付方式组合，要传付款列表明确各付款方式的金额值

### BR005
- 描述：账单时间限制规则
- 适用范围：所有账单上传场景
- 约束条件：
  - 系统支持传48小时内账单

### BR006
- 描述：数据记录规则
- 适用范围：所有数据上传场景
- 约束条件：
  - 数据上传记录写入数据库表中
  - 同时有日志记录

## 数据字典
### 支付方式
- 类型：枚举
- 描述：账单支付的方式
- 取值范围：现金(CH)、微信(WP)、支付宝(AP)、挂账、银行卡(CI)、其它(OT)
- 默认值：无

### 账单类型
- 类型：字符串
- 描述：标识账单的类型
- 取值范围：SALE（正常账单）、ONLINEREFUND（退单）
- 默认值：SALE

### 订单时间
- 类型：字符串
- 描述：订单创建的时间
- 取值范围：符合yyyyMMddHHmmss格式的时间字符串
- 默认值：当前时间
- 说明：时区为GMT+8，接口仅支持接收48天内的订单

### 优惠金额
- 类型：数值
- 描述：账单中的优惠金额
- 取值范围：任意非负数值
- 默认值：0（上传时固定为0）
- 说明：正数表示销售订单，负数表示退货订单

## 物业参数补充说明
根据参考文档"大米欢乐颂店物业.md"，物业参数配置是本测试的重要前置条件。物业参数包括：

1. **接口配置**：
   - 物业系统接口URL已正确配置
   - 接口认证信息（如API密钥、账号等）已正确设置
   - 接口超时参数已合理配置

2. **数据格式要求**：
   - 账单数据格式严格遵循物业系统要求
   - 优惠金额处理有特殊规则（上传时固定为0）
   - 菜品信息简化处理（传空数组）
   - 时间格式必须符合yyyyMMddHHmmss规范

3. **业务处理规则**：
   - 支持多种支付方式和组合支付场景
   - 正常账单和退单使用不同的类型标识
   - 网络异常有自动重试机制
   - 数据上传有完整的记录和日志

## 接口请求和响应信息
根据参考文档"接口请求和相应信息.md"，接口的详细信息如下：

### 请求报文信息
- **Content-Type**：application/json;charset=UTF-8
- **请求参数示例**：
```json
{"REQUEST": {"REQUEST_DATA": {"cashierId":"20170zinl227n0101","checkCode":"p88888888","mall":"","orderId":"9028298012760","payList":[{"discountAmt":0,"payAmt":63,"paymentMethod":"CH","time":"20220322174131","value":63}],"store":"Y1GB0122N03","tillId":"01","time":"20220322174130","totalAmt":63,"type":"SALE"},"HRT_ATTRS": {"Partner_ID": "70000029","Api_Version": "1.0.1","App_Sub_ID": "10000187223ZZ","Format": "json","Time_Stamp": "2024-02-06 10:32:41:446","Api_ID": "mixc.imPOSWBJB.GLWXCJB.orderCollect","App_Token": "5d93e6da4a914176aefe512ae3b52ecf","App_Pub_ID": "10000187223RL","Sign_Method": "md5","Sign": "7F65174D5829D7EFA0976B1A715E5A99","Sys_ID": "100001872"}}}
```

### 响应报文信息
- **响应示例**：
```json
{
    "RETURN_DATA": {
        "header": {
            "errcode": "0000",
            "errmsg": "成功"
        },
        "body": {
            "orderId": "1103600458211214010001",
            "refOrderId": "HSS0351001100120041010007"
        }
    },
    "RETURN_CODE": "INTERFACE HAS NO CODE",
    "RETURN_DESC": "INTERFACE HAS NO DESC",
    "RETURN_STAMP": ""
}
```

- **错误码说明**：
  - 0000：成功
  - 1001：失败，不同错误在"errmsg"中提示不同错误信息

### 主要请求参数
| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| cashierId | String | Y | 收银员编号，必填项 |
| itemList | Array | Y | 商品数组，必填项；填写内容："itemList": [ ] |
| mall | String | Y | 商场编号，必填项，长度：最小1位，最大40位 |
| orderId | String | Y | 订单号，必填项，长度：最小6位，最大40位 |
| payList | Array | Y | 支付行，必填项 |
| paymentMethod | String | Y | 支付方式，必填项，例如现金是CH；支付宝是AP；微信是WP；银行卡是CI；其它是OT等 |
| time | String | Y | 订单时间，必填项，格式：yyyyMMddHHmmss |
| totalAmt | String | Y | 订单总金额，必填项，正数表示销售订单，负数表示退货订单 |
| type | String | Y | 订单类型，必填项，销售：SALE，退货：ONLINEREFUND |
| checkCode | String | Y | 店铺验证密钥(登录密码)，必填项，由IMPOS提供 |

### 支付行(payList)参数
| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| paymentMethod | String | Y | 支付方式，必填项 |
| time | String | Y | 支付时间，必填项，格式：yyyyMMddHHmmss |
| value | Number | Y | 支付金额（实收金额），必填项，正数表示销售订单，负数表示退货订单 |
| payAmt | Number | Y | 应收金额，必填项，正数表示销售订单，负数表示退货订单 |
| discountAmt | Number | Y | 优惠金额，必填项，正数表示销售订单，负数表示退货订单 |
| cardBank | String | N | 支付银行，非必填 |
| cardNumber | String | N | 银行卡卡号，非必填 |

## 其他关键信息
- 前置条件：物业参数已正常配置，服务支持接口上传
- 不考虑物业测试环境和性能测试
- 测试场景包括多种支付方式：现金、现金+找零、优惠+现金、微信、支付宝、优惠+微信、优惠+支付宝、挂账、现金+挂账、0金额账单
- 所有场景都需要验证数据能否正确上传到物业系统，包括正常账单和退单两种情况
- 系统支持传48小时内账单
- 数据上传记录写入数据库表中，同时有日志记录

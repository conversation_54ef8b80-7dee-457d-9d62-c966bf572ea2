# 苏客全自助点餐结账功能 - 测试用例设计文档

## 测试概述
- **测试目标**：验证苏客全自助点餐结账功能的完整性、准确性和稳定性，确保AI识别、菜品修正、金额计算、支付流程等各个环节正常工作
- **测试范围**：覆盖前置条件验证、AI菜品识别、菜品修正、金额计算、操作控制、退出机制、支付流程、流水记录等所有功能点
- **测试策略**：
  1. 采用黑盒测试方法，基于功能需求进行测试设计
  2. 覆盖正常流程、异常流程和边界条件
  3. 重点验证AI识别准确性和支付流程完整性
  4. 确保每个功能点至少有2个测试用例
- **测试环境**：
  1. POS系统已正确安装和配置
  2. AI识别功能已启用
  3. 菜品已设置AI图片
  4. 支付接口已配置
  5. 测试用餐盘和菜品已准备

## 测试用例列表

### 1. 前置条件验证测试用例

#### TC-001: 验证未开店时全自助功能限制
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统未开店状态
  - 菜品已设置AI图片

- **测试步骤**：
  1. 在收银页面中间区域找到【全自助】按钮
  2. 点击【全自助】按钮
  3. 观察系统响应

- **预期结果**：
  1. 系统提示不允许操作加菜
  2. 不进入全自助点餐结账流程
  3. 保持在收银页面

- **测试数据**：
  - 系统状态：未开店
  - 操作按钮：【全自助】

#### TC-002: 验证未开班时全自助功能限制
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店但未开班
  - 菜品已设置AI图片

- **测试步骤**：
  1. 在收银页面中间区域找到【全自助】按钮
  2. 点击【全自助】按钮
  3. 观察系统响应

- **预期结果**：
  1. 系统提示不允许操作加菜
  2. 不进入全自助点餐结账流程
  3. 保持在收银页面

- **测试数据**：
  - 系统状态：已开店，未开班
  - 操作按钮：【全自助】

### 2. AI菜品识别测试用例

#### TC-003: 验证单个菜品正常识别流程
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品已设置AI图片
  - 餐盘已放置单个可识别菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将餐盘放置到AI识别区域
  3. 等待系统自动识别菜品
  4. 观察菜品识别结果和显示信息
  5. 等待系统稳定后自动跳转

- **预期结果**：
  1. 成功进入全自助点餐结账流程
  2. 系统自动识别出菜品
  3. 菜品上方显示菜品名称及序号
  4. 识别稳定后自动跳转至结账页面
  5. 声音播放'请扫码支付'

- **测试数据**：
  - 菜品类型：已设置AI图片的单个菜品
  - 识别区域：AI可识别区域

#### TC-004: 验证多个菜品同时识别
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 多个菜品已设置AI图片
  - 餐盘已放置多个可识别菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将餐盘放置到AI识别区域（包含多个菜品）
  3. 等待系统自动识别所有菜品
  4. 观察每个菜品的识别结果
  5. 检查菜品数量和名称显示

- **预期结果**：
  1. 系统能够识别餐盘中的所有菜品
  2. 每个菜品上方显示对应的菜品名称及序号
  3. 菜品数量统计正确
  4. 识别稳定后自动跳转至结账页面

- **测试数据**：
  - 菜品类型：3-5个已设置AI图片的不同菜品
  - 菜品数量：每种菜品1-2份

### 3. 菜品修正功能测试用例

#### TC-005: 验证无法识别菜品的修正功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中包含无法识别的菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将餐盘放置到AI识别区域（包含无法识别的菜品）
  3. 等待系统识别完成
  4. 观察无法识别菜品的显示状态
  5. 点击无法识别菜品上方的[修正]字样
  6. 在菜品信息页面选择正确的菜品
  7. 确认修正操作

- **预期结果**：
  1. 无法识别的菜品上方显示[修正]字样
  2. 点击[修正]后进入菜品信息页面
  3. 能够手动选择正确的菜品进行绑定
  4. 修正后菜品显示正确的名称和信息
  5. 此次识别的菜品图片绑定给选定菜品

- **测试数据**：
  - 菜品类型：未设置AI图片或AI无法识别的菜品
  - 修正目标：系统中已存在的菜品

#### TC-006: 验证已识别菜品不允许修正
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中包含已成功识别的菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将餐盘放置到AI识别区域
  3. 等待系统成功识别菜品
  4. 尝试点击已识别菜品的显示区域
  5. 观察系统响应

- **预期结果**：
  1. 已识别的菜品正常显示菜品名称及序号
  2. 已识别的菜品不显示[修正]字样
  3. 点击已识别菜品无法进入修正功能
  4. 系统不允许对已识别菜品进行修正操作

- **测试数据**：
  - 菜品类型：已设置AI图片且能正常识别的菜品

### 4. 金额计算验证测试用例

#### TC-007: 验证单个菜品金额计算准确性
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中放置单个已知价格的菜品

- **测试步骤**：
  1. 记录菜品的标准价格
  2. 点击【全自助】按钮进入全自助点餐结账流程
  3. 将餐盘放置到AI识别区域
  4. 等待系统识别完成
  5. 检查左侧显示的应收金额、优惠金额、菜品数量和待付金额

- **预期结果**：
  1. 应收金额等于菜品标准价格
  2. 菜品数量显示为1
  3. 如无优惠活动，优惠金额为0，待付金额等于应收金额
  4. 如有优惠活动，优惠金额和待付金额计算正确

- **测试数据**：
  - 菜品价格：已知的标准菜品价格
  - 菜品数量：1份

#### TC-008: 验证多个菜品金额计算准确性
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中放置多个不同价格的菜品

- **测试步骤**：
  1. 记录所有菜品的标准价格和数量
  2. 计算预期的总金额
  3. 点击【全自助】按钮进入全自助点餐结账流程
  4. 将餐盘放置到AI识别区域
  5. 等待系统识别完成
  6. 检查左侧显示的应收金额、优惠金额、菜品数量和待付金额

- **预期结果**：
  1. 应收金额等于所有菜品价格总和
  2. 菜品数量统计正确
  3. 优惠金额和待付金额计算准确
  4. 各项金额显示清晰无误

- **测试数据**：
  - 菜品组合：3-5个不同价格的菜品
  - 总金额：预计算的标准总价

### 5. 操作控制测试用例

#### TC-009: 验证手动扫码支付功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品正在识别中且已确定识别成功
  - 尚未自动进入付款页面

- **测试步骤**：
  1. 完成菜品识别但等待在识别页面
  2. 点击【扫码支付】按钮
  3. 观察页面跳转情况
  4. 检查支付页面显示内容

- **预期结果**：
  1. 成功进入扫码付款页面
  2. 页面显示正确的付款金额
  3. 显示付款二维码或付款提示
  4. 声音播放'请扫码支付'

- **测试数据**：
  - 操作时机：菜品识别成功但未自动跳转时
  - 按钮：【扫码支付】

#### TC-010: 验证继续识别功能
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品正在识别中且未进入付款页面

- **测试步骤**：
  1. 将餐盘放置到AI识别区域
  2. 在识别过程中点击【继续识别】按钮
  3. 观察系统响应
  4. 检查识别结果是否更新

- **预期结果**：
  1. 系统重新开始识别菜品
  2. 之前的识别结果被清除
  3. 重新显示识别过程
  4. 最终显示新的识别结果

- **测试数据**：
  - 操作时机：菜品识别过程中
  - 按钮：【继续识别】

#### TC-011: 验证清空商品功能
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品正在识别中且未进入付款页面

- **测试步骤**：
  1. 将餐盘放置到AI识别区域并完成部分识别
  2. 点击【清空商品】按钮
  3. 观察页面变化
  4. 检查是否重新开始识别

- **预期结果**：
  1. 所有已识别的菜品被清空
  2. 金额信息重置为0
  3. 系统重新开始识别菜品
  4. 页面恢复到初始识别状态

- **测试数据**：
  - 操作时机：部分菜品已识别时
  - 按钮：【清空商品】

### 6. 退出机制测试用例

#### TC-012: 验证员工登录退出功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品正在识别中且未进入付款页面
  - 已知有效的员工密码

- **测试步骤**：
  1. 在识别过程中点击右上角X按钮
  2. 观察是否弹出员工登录页面
  3. 输入正确的员工密码
  4. 点击确定按钮
  5. 观察系统响应

- **预期结果**：
  1. 点击X按钮后弹出员工登录页面
  2. 输入正确密码后成功退出全自助模式
  3. 返回到收银页面
  4. 全自助流程被终止

- **测试数据**：
  - 员工密码：有效的员工登录密码
  - 操作按钮：右上角X按钮

#### TC-013: 验证员工登录取消操作
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 菜品正在识别中且未进入付款页面

- **测试步骤**：
  1. 在识别过程中点击右上角X按钮
  2. 观察员工登录页面弹出
  3. 点击取消按钮
  4. 观察系统响应

- **预期结果**：
  1. 员工登录页面关闭
  2. 返回到菜品识别页面
  3. 继续之前的识别流程
  4. 不退出全自助模式

- **测试数据**：
  - 操作：点击取消按钮
  - 预期状态：继续识别流程

### 7. 支付流程测试用例

#### TC-014: 验证正确付款码扫码支付成功
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 已完成菜品识别并进入扫码付款页面
  - 准备有效的付款码

- **测试步骤**：
  1. 在扫码付款页面使用有效付款码进行扫码
  2. 等待支付处理完成
  3. 观察支付结果提示
  4. 检查是否自动完成结账流程

- **预期结果**：
  1. 扫码支付成功
  2. 显示支付成功提示信息
  3. 自动完成结账流程
  4. 返回到收银页面或显示完成页面

- **测试数据**：
  - 付款码：有效的微信/支付宝付款码
  - 支付金额：识别菜品的实际金额

#### TC-015: 验证错误付款码扫码处理
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 已完成菜品识别并进入扫码付款页面
  - 准备无效或错误的付款码

- **测试步骤**：
  1. 在扫码付款页面使用无效或错误的付款码进行扫码
  2. 观察系统响应
  3. 检查错误提示信息
  4. 验证是否可以重新扫码

- **预期结果**：
  1. 系统识别出错误的付款码
  2. 显示明确的错误提示信息
  3. 不允许支付操作继续
  4. 可以重新进行扫码操作

- **测试数据**：
  - 付款码：无效、过期或格式错误的付款码
  - 错误类型：多种不同的错误情况

### 8. 流水记录验证测试用例

#### TC-016: 验证支付成功后流水记录
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 已完成一笔全自助扫码支付

- **测试步骤**：
  1. 完成一笔全自助点餐结账支付
  2. 进入流水查询功能
  3. 查找刚完成的交易记录
  4. 检查流水中的付款方式和金额信息
  5. 验证交易详细信息的准确性

- **预期结果**：
  1. 流水中能找到对应的交易记录
  2. 付款方式正确显示（微信/支付宝等）
  3. 支付金额与实际支付金额一致
  4. 交易时间、菜品信息等详细信息准确
  5. 交易状态显示为成功

- **测试数据**：
  - 交易金额：实际支付的金额
  - 付款方式：实际使用的付款方式
  - 交易时间：支付完成的时间

## 测试覆盖率分析
- **功能覆盖率**：测试用例覆盖了所有12个功能测试点，包括前置条件验证、AI识别、菜品修正、金额计算、操作控制、退出机制、支付流程、流水记录
- **场景覆盖率**：涵盖正常流程、异常流程、边界条件和错误处理场景
- **优先级分布**：高优先级10个用例，中优先级6个用例，确保核心功能优先测试
- **测试类型**：主要为功能测试，确保业务逻辑正确性

### 9. 边界条件和异常场景测试用例

#### TC-017: 验证空餐盘识别处理
- **优先级**：中
- **测试类型**：边界条件测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 准备空餐盘（无任何菜品）

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将空餐盘放置到AI识别区域
  3. 等待系统识别处理
  4. 观察系统响应和提示信息

- **预期结果**：
  1. 系统能够识别出餐盘为空
  2. 显示相应的提示信息（如"请放置菜品"）
  3. 不进入结账流程
  4. 等待用户放置菜品

- **测试数据**：
  - 餐盘状态：完全空白的餐盘

#### TC-018: 验证大量菜品同时识别的处理能力
- **优先级**：中
- **测试类型**：边界条件测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 准备大量菜品（超过正常数量）

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将包含大量菜品的餐盘放置到AI识别区域
  3. 等待系统识别处理
  4. 观察识别速度和准确性
  5. 检查金额计算是否正确

- **预期结果**：
  1. 系统能够处理大量菜品的识别
  2. 识别结果准确，不遗漏菜品
  3. 金额计算正确
  4. 系统性能稳定，不出现卡顿或崩溃

- **测试数据**：
  - 菜品数量：10-15个不同菜品
  - 菜品类型：混合已知和未知菜品

#### TC-019: 验证识别过程中移动餐盘的处理
- **优先级**：低
- **测试类型**：异常场景测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中放置菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将餐盘放置到AI识别区域
  3. 在识别过程中移动或移除餐盘
  4. 观察系统响应
  5. 重新放置餐盘并观察识别结果

- **预期结果**：
  1. 系统能够检测到餐盘移动
  2. 显示相应的提示信息
  3. 重新放置后能够继续识别
  4. 不会因为移动而导致系统错误

- **测试数据**：
  - 移动时机：识别过程中的不同阶段
  - 移动方式：轻微移动、完全移除

#### TC-020: 验证网络中断时的支付处理
- **优先级**：高
- **测试类型**：异常场景测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 已完成菜品识别并进入扫码付款页面
  - 能够模拟网络中断

- **测试步骤**：
  1. 在扫码付款页面准备进行支付
  2. 模拟网络中断
  3. 尝试扫码支付
  4. 观察系统响应和错误处理
  5. 恢复网络连接后重试支付

- **预期结果**：
  1. 系统能够检测到网络异常
  2. 显示明确的网络错误提示
  3. 不会丢失当前订单信息
  4. 网络恢复后能够继续支付流程

- **测试数据**：
  - 网络状态：正常→中断→恢复
  - 支付金额：正常的订单金额

#### TC-021: 验证系统重启后的状态恢复
- **优先级**：中
- **测试类型**：异常场景测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 正在进行全自助点餐流程

- **测试步骤**：
  1. 在全自助点餐过程中（识别阶段）
  2. 模拟系统意外重启
  3. 系统重启完成后重新登录
  4. 检查系统状态和数据恢复情况
  5. 重新尝试全自助点餐流程

- **预期结果**：
  1. 系统重启后能够正常启动
  2. 不会保留未完成的全自助订单
  3. 能够重新开始全自助点餐流程
  4. 系统功能正常，无数据损坏

- **测试数据**：
  - 重启时机：识别过程中、支付过程中
  - 数据状态：重启前后的数据一致性

#### TC-022: 验证同一菜品重复识别的处理
- **优先级**：中
- **测试类型**：边界条件测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中放置多份相同菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将包含多份相同菜品的餐盘放置到AI识别区域
  3. 等待系统识别完成
  4. 检查菜品数量统计
  5. 验证金额计算准确性

- **预期结果**：
  1. 系统能够正确识别多份相同菜品
  2. 菜品数量统计准确
  3. 每份菜品都有正确的序号显示
  4. 总金额计算正确（单价×数量）

- **测试数据**：
  - 菜品类型：同一种菜品
  - 菜品数量：3-5份相同菜品

#### TC-023: 验证混合识别状态的处理（部分识别成功，部分需要修正）
- **优先级**：高
- **测试类型**：综合场景测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 餐盘中混合放置可识别和不可识别的菜品

- **测试步骤**：
  1. 点击【全自助】按钮进入全自助点餐结账流程
  2. 将混合菜品的餐盘放置到AI识别区域
  3. 等待系统识别完成
  4. 观察识别结果的显示状态
  5. 对需要修正的菜品进行修正操作
  6. 检查最终的金额计算

- **预期结果**：
  1. 可识别菜品正常显示名称和序号
  2. 不可识别菜品显示[修正]字样
  3. 能够对需要修正的菜品进行修正
  4. 修正完成后金额计算正确
  5. 能够正常进入支付流程

- **测试数据**：
  - 菜品组合：2-3个可识别菜品 + 1-2个不可识别菜品
  - 修正操作：手动选择正确的菜品类型

#### TC-024: 验证支付超时的处理
- **优先级**：中
- **测试类型**：异常场景测试
- **前置条件**：
  - 已登录POS系统
  - 系统已开店且已开班
  - 已完成菜品识别并进入扫码付款页面

- **测试步骤**：
  1. 进入扫码付款页面
  2. 长时间不进行任何支付操作
  3. 观察系统是否有超时处理机制
  4. 检查超时后的系统状态
  5. 验证是否可以重新开始流程

- **预期结果**：
  1. 系统在合理时间后提示支付超时
  2. 超时后自动退出支付流程或提供重试选项
  3. 不会无限期等待用户操作
  4. 超时处理不影响系统稳定性

- **测试数据**：
  - 超时时间：系统设定的支付超时时长
  - 处理方式：自动退出或提示重试

## 测试覆盖率分析
- **功能覆盖率**：测试用例覆盖了所有12个功能测试点，包括前置条件验证、AI识别、菜品修正、金额计算、操作控制、退出机制、支付流程、流水记录
- **场景覆盖率**：涵盖正常流程、异常流程、边界条件和错误处理场景，共24个测试用例
- **优先级分布**：高优先级13个用例，中优先级9个用例，低优先级2个用例，确保核心功能优先测试
- **测试类型**：功能测试、边界条件测试、异常场景测试、综合场景测试

## 测试数据准备清单
1. **菜品数据**：
   - 已设置AI图片的标准菜品（5-10种）
   - 未设置AI图片的菜品（2-3种）
   - 不同价格范围的菜品（低、中、高价位）
   - 相同菜品的多份样本

2. **支付数据**：
   - 有效的微信付款码
   - 有效的支付宝付款码
   - 无效或过期的付款码
   - 格式错误的付款码

3. **系统环境**：
   - 正常网络环境
   - 网络中断模拟环境
   - 不同的系统状态（开店/未开店、开班/未开班）

4. **员工数据**：
   - 有效的员工登录密码
   - 无效的员工密码

## 测试执行建议
1. **测试顺序**：按优先级从高到低执行，先验证核心功能再测试辅助功能和边界条件
2. **测试环境**：确保AI识别功能稳定，准备多种类型的测试菜品和完整的测试数据
3. **测试数据**：按照测试数据准备清单准备完整的测试数据
4. **回归测试**：每次系统更新后重新执行高优先级测试用例
5. **性能测试**：关注AI识别速度和支付响应时间，特别是大量菜品识别的性能
6. **异常恢复测试**：重点测试各种异常情况下的系统恢复能力
7. **用户体验测试**：关注操作流程的顺畅性和提示信息的清晰度

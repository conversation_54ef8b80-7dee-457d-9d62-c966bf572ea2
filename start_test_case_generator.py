import os
import sys
import subprocess
import datetime
import yaml
from pathlib import Path

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))
agent_dir = os.path.join(current_dir, '.agent')

# 设置环境变量
os.environ['USER_ID'] = 'jiaojingjing'
os.environ['USER_NAME'] = '焦晶晶'
os.environ['LOCAL_WORKSPACE'] = os.path.join(current_dir, '_agent-local')
os.environ['LOCAL_RULES_PATH'] = os.path.join(current_dir, '_agent-local', 'rules.md')

# 打印环境变量
print("设置的环境变量:")
for key, value in os.environ.items():
    if key in ['USER_ID', 'USER_NAME', 'LOCAL_WORKSPACE', 'LOCAL_RULES_PATH']:
        print(f"  {key}: {value}")

# 手动创建工作流实例
try:
    # 读取工作流模板
    workflow_template_path = os.path.join(current_dir, '_agent-local', 'workflows', 'work', 'simple_test.yml')
    print(f"读取工作流模板: {workflow_template_path}")

    with open(workflow_template_path, 'r', encoding='utf-8') as f:
        workflow = yaml.safe_load(f)

    # 创建工作流实例
    timestamp = datetime.datetime.now().strftime('%m%d_%H_%M')
    user_id = os.environ['USER_ID']
    user_name = os.environ['USER_NAME']
    workflow_name = 'simple_test'

    # 创建工作流实例目录
    workspace_dir = os.path.join(current_dir, '_agent-local', 'workspace')
    os.makedirs(workspace_dir, exist_ok=True)

    work_dir = os.path.join(workspace_dir, f"{user_id}_{workflow_name}_{timestamp}")
    os.makedirs(work_dir, exist_ok=True)

    # 准备工作流实例数据
    work_data = {
        'id': f"{user_id}_{workflow_name}_{timestamp}",
        'name': workflow.get('name', workflow_name),
        'description': workflow.get('description', ''),
        'rules': workflow.get('rules', []),
        'status': 'in_progress',
        'user_id': user_id,
        'user_name': user_name,
        'tasks': workflow.get('tasks', [])
    }

    # 处理任务状态
    for task in work_data['tasks']:
        task['status'] = 'todo'

    # 标记第一个任务为当前任务
    if work_data['tasks']:
        work_data['tasks'][0]['status'] = 'in_progress'

    # 保存工作流实例
    work_file = os.path.join(work_dir, 'work.yml')
    with open(work_file, 'w', encoding='utf-8') as f:
        yaml.dump(work_data, f, allow_unicode=True, sort_keys=False)

    print(f"已创建工作流实例: {work_file}")

    # 更新规则文件
    rules_file = os.path.join(current_dir, '_agent-local', 'rules.md')

    # 格式化工作输出
    output = f"- 当前工作: {work_data['name']}\n"
    output += f"- 工作简介: {work_data['description']}\n"
    output += "- 工作规则:\n"

    for rule in work_data['rules']:
        output += f"    - {rule}\n"

    output += "- 任务列表:\n"

    for i, task in enumerate(work_data['tasks']):
        status_text = ""
        if task['status'] == 'in_progress':
            status_text = "(当前任务)"
        elif task['status'] == 'todo':
            status_text = "(未开始)"
        elif task['status'] == 'done':
            status_text = "(已完成)"

        output += f"    - {'[x]' if task['status'] == 'done' else '[ ]'} {task['name']} {status_text}\n"

        if task['status'] == 'in_progress':
            output += f"        任务说明: {task.get('description', '')}\n"
            output += f"        执行角色: {task.get('worker', '')}\n"

            if task.get('output'):
                output += "        任务输出文档:\n"
                for doc_output in task['output']:
                    output += f"            - {doc_output.get('doc', '')}\n"
                    if doc_output.get('template'):
                        output += "                文档模板:\n"
                        output += "                ```\n"
                        output += doc_output['template'] + "\n"
                        output += "                ```\n"

            if task.get('rules'):
                output += "        任务规则:\n"
                for rule in task['rules']:
                    output += f"            - {rule}\n"

            if task.get('prompt'):
                output += "\n当前工作命令: " + task['prompt'] + "\n"

    # 更新规则文件
    with open(rules_file, 'w', encoding='utf-8') as f:
        f.write(f"<global_work>\n{output}\n</global_work>")

    print(f"已更新规则文件: {rules_file}")
    print("\n工作流已成功创建，请使用以下命令继续操作:")
    print("1. 使用 /work now 查看当前工作状态")
    print("2. 使用 /work next 进入下一个任务")

except Exception as e:
    print(f"创建工作流实例失败: {e}")

# 查找最新创建的工作流实例文件夹
workspace_dir = os.path.join(current_dir, '_agent-local', 'workspace')
if os.path.exists(workspace_dir):
    folders = [f for f in os.listdir(workspace_dir) if os.path.isdir(os.path.join(workspace_dir, f))]
    if folders:
        latest_folder = max(folders, key=lambda f: os.path.getmtime(os.path.join(workspace_dir, f)))
        print(f"最新创建的工作流实例文件夹: {latest_folder}")
    else:
        print("未找到工作流实例文件夹")
else:
    print(f"工作空间目录不存在: {workspace_dir}")

# 测试用例设计文档

## 测试概述
- 测试目标：验证租户上传销售接口的功能正确性、稳定性和性能
- 测试范围：正常销售上传、覆盖历史销售、退货功能、多种付款方式组合、并发上传处理、网络错误恢复
- 测试策略：结合功能测试、接口测试、业务规则测试、性能测试和容错测试
- 测试环境：测试环境URL为http://kc.lvgemgroup.com.cn:8185/posservice/，使用指定的测试参数

## 测试用例列表

### 正常销售上传 测试用例

#### TC-001: 正常销售上传基本功能验证
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥（LVGEMHSL）

- **测试步骤**：
  1. 构建包含完整请求参数的JSON数据，包括：
     - apiKey: LVGEMHSL
     - docKey: 生成唯一的销售单号
     - 店铺号storecode: 09210029
     - 收银机号tillid: 00
     - 收银员编号cashier: 09210029
     - 商品编号: 0921002901
     - 付款方式编码: CH
     - 销售数量和金额: 正常值
  2. 发送HTTP POST请求到URL: http://kc.lvgemgroup.com.cn:8185/posservice/rest/salestransaction/salestranslitev61
  3. 接收响应并记录

- **预期结果**：
  1. 接收到成功响应，errorCode为0
  2. 响应中包含成功信息
  3. 销售数据已正确保存到系统中

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_001
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 付款方式: CH
  - 销售数量: 2
  - 销售金额: 100.00

#### TC-002: 缺少必填参数测试 - apiKey
- **优先级**：高
- **测试类型**：异常测试
- **前置条件**：
  - 测试环境可访问

- **测试步骤**：
  1. 构建缺少apiKey的JSON请求数据，包含其他所有必填参数
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为-1或相应的错误码
  2. 响应中包含关于缺少apiKey的错误信息

- **测试数据**：
  - docKey: SALE_TEST_002
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 付款方式: CH
  - 销售数量: 2
  - 销售金额: 100.00

#### TC-003: 缺少必填参数测试 - docKey
- **优先级**：高
- **测试类型**：异常测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建缺少docKey的JSON请求数据，包含其他所有必填参数
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为相应的错误码
  2. 响应中包含关于缺少docKey的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 付款方式: CH
  - 销售数量: 2
  - 销售金额: 100.00

#### TC-004: 金额一致性验证 - 销售项目金额与总计金额不一致
- **优先级**：高
- **测试类型**：业务规则测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建JSON请求数据，使salesItem中的netAmount总和与salesTotal中的netAmount不一致
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为-18
  2. 响应中包含关于金额不一致的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_004
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - salesItem.netAmount: 100.00
  - salesTotal.netAmount: 120.00
  - 付款方式: CH
  - 付款金额: 120.00

#### TC-005: 金额一致性验证 - 支付方式金额与总计金额不一致
- **优先级**：高
- **测试类型**：业务规则测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建JSON请求数据，使salesTender中的payAmount总和与salesTotal中的netAmount不一致
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为-19
  2. 响应中包含关于金额不一致的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_005
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - salesItem.netAmount: 100.00
  - salesTotal.netAmount: 100.00
  - 付款方式: CH
  - 付款金额: 80.00

#### TC-006: 商品编号验证 - 无效商品编号
- **优先级**：中
- **测试类型**：业务规则测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建JSON请求数据，使用无效的商品编号
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为-12
  2. 响应中包含关于无效商品编号的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_006
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: INVALID_PRODUCT_CODE
  - 销售数量: 2
  - 销售金额: 100.00
  - 付款方式: CH
  - 付款金额: 100.00

#### TC-007: 店铺和收银机验证 - 无效店铺号
- **优先级**：中
- **测试类型**：业务规则测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建JSON请求数据，使用无效的店铺号
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应，errorCode为-16
  2. 响应中包含关于无效店铺号的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_007
  - storecode: INVALID_STORE_CODE
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 2
  - 销售金额: 100.00
  - 付款方式: CH
  - 付款金额: 100.00

#### TC-008: 销售单号唯一性验证
- **优先级**：高
- **测试类型**：业务规则测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 已成功上传一笔销售数据

- **测试步骤**：
  1. 使用与之前成功上传相同的docKey构建JSON请求数据
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 接收到错误响应
  2. 响应中包含关于销售单号已存在的错误信息

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_001（与TC-001相同）
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 3
  - 销售金额: 150.00
  - 付款方式: CH
  - 付款金额: 150.00

#### TC-009: 边界条件测试 - 极大销售数量和金额
- **优先级**：低
- **测试类型**：边界测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建JSON请求数据，使用极大的销售数量和金额
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录

- **预期结果**：
  1. 系统能够正确处理极大的销售数量和金额
  2. 接收到成功响应，errorCode为0

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_TEST_009
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 999999
  - 销售金额: 9999999.99
  - 付款方式: CH
  - 付款金额: 9999999.99

### 覆盖历史销售 测试用例

#### TC-010: 覆盖历史销售基本功能验证
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 已成功上传一笔销售数据

- **测试步骤**：
  1. 记录之前上传的销售数据的日期、店铺号和收银机号
  2. 构建新的JSON请求数据，使用相同的日期、店铺号和收银机号，但销售数量和金额不同
  3. 发送HTTP POST请求到覆盖历史销售接口URL
  4. 接收响应并记录
  5. 查询系统中的销售数据

- **预期结果**：
  1. 接收到成功响应，errorCode为0
  2. 系统中只保留最后一笔上传的销售数据
  3. 历史销售数据已被清零

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_REPLACE_010
  - storecode: 09210029（与之前上传相同）
  - tillid: 00（与之前上传相同）
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 5
  - 销售金额: 250.00
  - 付款方式: CH
  - 付款金额: 250.00

#### TC-011: 多次覆盖同一销售数据
- **优先级**：中
- **测试类型**：边界测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 已成功覆盖一笔销售数据

- **测试步骤**：
  1. 记录之前覆盖的销售数据的日期、店铺号和收银机号
  2. 构建新的JSON请求数据，使用相同的日期、店铺号和收银机号，但销售数量和金额再次不同
  3. 发送HTTP POST请求到覆盖历史销售接口URL
  4. 接收响应并记录
  5. 重复步骤2-4多次
  6. 查询系统中的销售数据

- **预期结果**：
  1. 每次覆盖都接收到成功响应，errorCode为0
  2. 系统中只保留最后一笔上传的销售数据
  3. 所有历史销售数据都已被清零

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: SALE_REPLACE_011_1, SALE_REPLACE_011_2, SALE_REPLACE_011_3
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 依次为6, 7, 8
  - 销售金额: 依次为300.00, 350.00, 400.00
  - 付款方式: CH
  - 付款金额: 与销售金额相同

### 退货功能 测试用例

#### TC-012: 退货基本功能验证
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 已成功上传一笔销售数据

- **测试步骤**：
  1. 记录之前上传的销售数据的docKey和其他信息
  2. 构建退货JSON请求数据，包含原销售单信息，netQty和netAmount为负数
  3. 发送HTTP POST请求到指定URL
  4. 接收响应并记录
  5. 查询系统中的销售和退货数据

- **预期结果**：
  1. 接收到成功响应，errorCode为0
  2. 系统中正确记录了退货信息
  3. 财务数据正确反映了退货影响

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: RETURN_TEST_012
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: -2（负数表示退货）
  - 销售金额: -100.00（负数表示退货）
  - 付款方式: CH
  - 付款金额: -100.00
  - 原销售单信息: 包含之前上传的销售数据的docKey和其他信息

#### TC-013: 部分退货测试
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 已成功上传一笔销售数据，销售数量大于1

- **测试步骤**：
  1. 记录之前上传的销售数据的docKey和其他信息
  2. 构建部分退货JSON请求数据，退货数量小于原销售数量
  3. 发送HTTP POST请求到指定URL
  4. 接收响应并记录
  5. 查询系统中的销售和退货数据

- **预期结果**：
  1. 接收到成功响应，errorCode为0
  2. 系统中正确记录了部分退货信息
  3. 财务数据正确反映了部分退货影响

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: RETURN_TEST_013
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: -1（部分退货）
  - 销售金额: -50.00（部分退货金额）
  - 付款方式: CH
  - 付款金额: -50.00
  - 原销售单信息: 包含之前上传的销售数据的docKey和其他信息

### 多种付款方式组合 测试用例

#### TC-014: 多种付款方式组合基本功能验证
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥

- **测试步骤**：
  1. 构建包含多种付款方式的JSON请求数据，确保付款方式金额总和与销售总计金额一致
  2. 发送HTTP POST请求到指定URL
  3. 接收响应并记录
  4. 查询系统中的销售和支付数据

- **预期结果**：
  1. 接收到成功响应，errorCode为0
  2. 系统中正确记录了多种付款方式的信息
  3. 财务数据正确反映了各种付款方式的金额

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: PAYMENT_TEST_014
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 2
  - 销售金额: 100.00
  - 付款方式1: CH（现金）
  - 付款金额1: 60.00
  - 付款方式2: CK（支票）
  - 付款金额2: 40.00

### 并发上传处理 测试用例

#### TC-015: 并发上传基本功能验证
- **优先级**：低
- **测试类型**：性能测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 具备模拟并发请求的工具

- **测试步骤**：
  1. 准备多个不同docKey但相同店铺号的销售数据
  2. 使用并发工具同时发送多个HTTP POST请求到指定URL
  3. 接收所有响应并记录
  4. 查询系统中的销售数据

- **预期结果**：
  1. 系统能够处理并发请求
  2. 部分请求可能返回错误码-67（销售单据在处理队列中）
  3. 所有成功处理的请求都正确记录在系统中

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: CONCURRENT_TEST_015_1, CONCURRENT_TEST_015_2, CONCURRENT_TEST_015_3
  - storecode: 09210029（所有请求相同）
  - tillid: 00（所有请求相同）
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 1
  - 销售金额: 50.00
  - 付款方式: CH
  - 付款金额: 50.00

### 网络错误恢复 测试用例

#### TC-016: 网络中断恢复测试
- **优先级**：中
- **测试类型**：容错测试
- **前置条件**：
  - 测试环境可访问
  - 拥有有效的API密钥
  - 具备模拟网络中断的工具

- **测试步骤**：
  1. 准备销售数据
  2. 模拟网络中断（例如，断开网络连接或使用网络代理工具）
  3. 尝试发送HTTP POST请求到指定URL
  4. 确认请求失败
  5. 恢复网络连接
  6. 使用相同的销售数据重新发送请求
  7. 接收响应并记录
  8. 查询系统中的销售数据

- **预期结果**：
  1. 网络恢复后，系统能够正确处理重新上传的请求
  2. 接收到成功响应，errorCode为0
  3. 销售数据正确记录在系统中

- **测试数据**：
  - apiKey: LVGEMHSL
  - docKey: NETWORK_TEST_016
  - storecode: 09210029
  - tillid: 00
  - cashier: 09210029
  - 商品编号: 0921002901
  - 销售数量: 1
  - 销售金额: 50.00
  - 付款方式: CH
  - 付款金额: 50.00

## 测试覆盖率分析
- 功能覆盖率：测试用例覆盖了所有主要功能点，包括正常销售上传、覆盖历史销售、退货功能、多种付款方式组合、并发上传处理和网络错误恢复
- 业务规则覆盖率：测试用例验证了所有关键业务规则，包括必填参数验证、金额一致性验证、商品编号验证、店铺和收银机验证、销售单号唯一性验证等
- 边界条件覆盖率：测试用例包含了多种边界条件测试，如极大销售数量和金额、多次覆盖同一销售数据、部分退货等

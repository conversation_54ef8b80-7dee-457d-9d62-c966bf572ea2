# 测试用例设计文档

## 测试概述
- 测试目标：验证POS端不同等级会员命中满减活动功能的正确性和可靠性
- 测试范围：POS端会员等级满减活动应用功能的完整流程，包括会员等级识别、满减活动匹配、优惠计算和应用
- 测试策略：采用黑盒测试方法，结合等价类划分和边界值分析，覆盖正常流程、异常流程和边界条件
- 测试环境：POS系统测试环境，包含会员系统测试数据和营销中心满减活动配置

## 测试用例列表
### POS端会员等级满减活动应用 测试用例

#### TC-001: 普通会员消费20元不满足任何满减条件
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为20元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（20元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统未应用任何满减活动（账单金额低于满减门槛）
  3. 最终应付金额为20元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 20元
  - 预期优惠金额: 0元
  - 预期应付金额: 20元

#### TC-002: 普通会员消费30元命中全部会员满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为30元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（30元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统应用活动4（满30减2，适用全部会员）
  3. 最终应付金额为28元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 30元
  - 预期优惠金额: 2元
  - 预期应付金额: 28元

#### TC-003: 普通会员消费50元命中普通会员满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为50元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（50元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统应用活动1（满50减5，适用普通会员），而非活动4（满30减2）
  3. 最终应付金额为45元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 50元
  - 预期优惠金额: 5元
  - 预期应付金额: 45元

#### TC-004: VIP会员消费100元命中VIP会员满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动2（满100减15，适用VIP会员）、活动4（满30减2，适用全部会员）
  - 会员2为VIP会员
  - POS系统已完成点餐，账单金额为100元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员2（VIP会员）
  3. 查看账单金额（100元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员2为VIP会员
  2. 系统应用活动2（满100减15，适用VIP会员），而非活动4（满30减2）
  3. 最终应付金额为85元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员2
  - 会员等级: VIP会员
  - 账单金额: 100元
  - 预期优惠金额: 15元
  - 预期应付金额: 85元

#### TC-005: 钻石会员消费200元命中钻石会员满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动3（满200减40，适用钻石会员）、活动4（满30减2，适用全部会员）
  - 会员3为钻石会员
  - POS系统已完成点餐，账单金额为200元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员3（钻石会员）
  3. 查看账单金额（200元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员3为钻石会员
  2. 系统应用活动3（满200减40，适用钻石会员），而非活动4（满30减2）
  3. 最终应付金额为160元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员3
  - 会员等级: 钻石会员
  - 账单金额: 200元
  - 预期优惠金额: 40元
  - 预期应付金额: 160元

#### TC-006: 新入会普通会员消费50元命中普通会员满减活动
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）
  - 会员4为新入会普通会员
  - POS系统已完成点餐，账单金额为50元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员4（新入会普通会员）
  3. 查看账单金额（50元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员4为普通会员
  2. 系统应用活动1（满50减5，适用普通会员），而非活动4（满30减2）
  3. 最终应付金额为45元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员4
  - 会员等级: 普通会员（新入会）
  - 账单金额: 50元
  - 预期优惠金额: 5元
  - 预期应付金额: 45元

#### TC-007: 消费金额恰好等于满减门槛（边界值测试）
- **优先级**：中
- **测试类型**：边界值测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为50.00元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（50.00元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统应用活动1（满50减5，适用普通会员）
  3. 最终应付金额为45.00元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 50.00元
  - 预期优惠金额: 5.00元
  - 预期应付金额: 45.00元

#### TC-008: 消费金额略低于满减门槛（边界值测试）
- **优先级**：中
- **测试类型**：边界值测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为49.99元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（49.99元）
  4. 观察系统是否应用满减活动
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统未应用活动1（满50减5，适用普通会员），但应用活动4（满30减2，适用全部会员）
  3. 最终应付金额为47.99元
  4. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 49.99元
  - 预期优惠金额: 2.00元
  - 预期应付金额: 47.99元

#### TC-009: 会员等级识别失败（异常场景测试）
- **优先级**：中
- **测试类型**：异常测试
- **前置条件**：
  - 营销中心已配置满减活动：活动1（满50减5，适用普通会员）、活动4（满30减2，适用全部会员）
  - 会员系统暂时无法访问或会员信息不完整
  - POS系统已完成点餐，账单金额为50元

- **测试步骤**：
  1. 登录POS系统
  2. 尝试选择会员（但会员系统无法正常响应）
  3. 查看账单金额（50元）
  4. 观察系统如何处理会员等级识别失败的情况
  5. 完成支付流程

- **预期结果**：
  1. 系统提示会员信息获取失败
  2. 系统默认将会员视为普通会员
  3. 系统应用活动1（满50减5，适用普通会员）
  4. 最终应付金额为45元
  5. 支付流程正常完成

- **测试数据**：
  - 会员ID: 未知或不完整
  - 默认会员等级: 普通会员
  - 账单金额: 50元
  - 预期优惠金额: 5元
  - 预期应付金额: 45元

#### TC-010: 满减活动配置错误（异常场景测试）
- **优先级**：中
- **测试类型**：异常测试
- **前置条件**：
  - 营销中心满减活动配置错误或不完整
  - 会员1为普通会员
  - POS系统已完成点餐，账单金额为50元

- **测试步骤**：
  1. 登录POS系统
  2. 选择会员1（普通会员）
  3. 查看账单金额（50元）
  4. 观察系统如何处理满减活动配置错误的情况
  5. 完成支付流程

- **预期结果**：
  1. 系统正确识别会员1为普通会员
  2. 系统提示营销活动异常或无法应用满减
  3. 不应用任何满减优惠
  4. 最终应付金额为50元
  5. 支付流程正常完成

- **测试数据**：
  - 会员ID: 会员1
  - 会员等级: 普通会员
  - 账单金额: 50元
  - 预期优惠金额: 0元
  - 预期应付金额: 50元

## 测试覆盖率分析
- 功能覆盖率：100%（覆盖了POS端会员等级满减活动应用的所有功能点）
- 业务规则覆盖率：100%（覆盖了所有业务规则，包括不同等级会员对应不同满减活动、存在多个满减时自动命中最大优惠等）
- 边界条件覆盖率：90%（覆盖了大部分边界条件，包括消费金额恰好等于或略低于满减门槛、会员等级识别失败等）

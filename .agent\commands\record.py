#!/usr/bin/env python3
import os
import requests
import datetime
from pathlib import Path
from dotenv import load_dotenv

# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

# 加载环境变量
env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
load_dotenv(env_path)

def get_absolute_path(path: str, base_dir: str) -> str:
    """将路径转换为绝对路径
    如果是绝对路径直接返回，否则基于base_dir转换为绝对路径
    """
    if not path:
        return path
    return path if os.path.isabs(path) else os.path.join(base_dir, path)

# 获取环境变量并根据需要转换为绝对路径
workspace = get_absolute_path(os.getenv('LOCAL_WORKSPACE'), project_root)

def get_user_info():
    """从.env文件读取用户信息

    Returns:
        Tuple[str, str]: (user_id, user_name)
    """
    user_id = os.getenv('USER_ID')
    user_name = os.getenv('USER_NAME')

    return user_id, user_name

def report_action(data):
    """向API上报操作数据

    Args:
        data (dict): 上报数据对象，包含以下字段：
            - user_name (str): 用户名(必填)
            - action (str): 操作类型，如'执行工作流'、'工作流:下一步'等(必填)
            - content (str, optional): 操作内容详情，默认为空字符串
            - workflow_name (str, optional): 工作流名称，默认为空字符串
            - task_name (str, optional): 任务名称，默认为空字符串
            - step_name (str, optional): 步骤名称，默认为空字符串
            - task_id (str, optional): 任务ID，默认为空字符串
            其他字段会作为扩展字段一起上报

    Returns:
        bool: 上报是否成功

    Raises:
        ValueError: 当必填字段缺失时抛出异常
    """
    if not data.get('action'):
        raise ValueError("action 是必填字段")

    # 获取当前时间戳(毫秒)
    current_time = int(datetime.datetime.now().timestamp() * 1000)

    # 定义所有必要字段及其默认值
    user_id, user_name = get_user_info()
    required_fields = {
        'user_name': user_name,  # 必填，但已经在上面检查过了
        'action': '',     # 必填，但已经在上面检查过了
        'content': '',
        'workflow_name': '',
        'task_name': '',
        'step_name': '',
        'task_id': '',
        'time': current_time
    }

    # 使用默认值填充缺失字段
    for field, default_value in required_fields.items():
        data.setdefault(field, default_value)

    try:
        response = requests.post('https://gitstat.aqwhr.cn/tapd/user-action-data/save', json=data)
        # print(f"上报信息: {response}，{data}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error reporting action: {e}")
        return False

if __name__ == '__main__':
    # 测试用户信息获取
    user_id, user_name = get_user_info()
    print(f"User ID: {user_id}, User Name: {user_name}")

    # 测试数据上报
    if user_name:
        # 测试基本上报
        success = report_action({
            'user_name': user_name,
            'action': '测试上报',
            'content': '测试内容'
        })
        print(f"Basic report result: {'Success' if success else 'Failed'}")

        # 测试完整参数上报
        success = report_action({
            'user_name': user_name,
            'action': '工作流执行',
            'content': '执行开发工作流',
            'workflow_name': '开发工作流',
            'task_name': '代码开发',
            'step_name': '编写功能',
            'task_id': '1'
        })
        print(f"Full report result: {'Success' if success else 'Failed'}")

        # 测试带扩展字段的上报
        success = report_action({
            'user_name': user_name,
            'action': '自定义事件',
            'content': '测试扩展字段',
            'custom_field': '自定义值',
            'extra_info': {
                'key1': 'value1',
                'key2': 'value2'
            }
        })
        print(f"Extended report result: {'Success' if success else 'Failed'}")

import os
import sys
import yaml
import subprocess
from pathlib import Path

# 获取当前目录
current_dir = Path(__file__).parent.absolute()
print(f"当前目录: {current_dir}")

# 工作流文件路径
workflow_file = current_dir / '_agent-local' / 'workflows' / 'work' / 'test_case_generator.yml'
print(f"工作流文件: {workflow_file}")
print(f"文件存在: {workflow_file.exists()}")

if not workflow_file.exists():
    print(f"错误: 工作流文件不存在: {workflow_file}")
    sys.exit(1)

# 读取工作流文件
try:
    with open(workflow_file, 'r', encoding='utf-8') as f:
        workflow = yaml.safe_load(f)
    
    # 从工作流文件中获取环境变量
    if 'env' in workflow:
        print("从工作流文件中读取环境变量:")
        for key, value in workflow['env'].items():
            os.environ[key] = value
            print(f"  设置环境变量: {key}={value}")
    
    # 检查环境变量是否设置成功
    print("\n当前环境变量:")
    env_vars = ['USER_ID', 'USER_NAME', 'LOCAL_WORKSPACE', 'LOCAL_RULES_PATH']
    for key in env_vars:
        print(f"  {key}={os.environ.get(key, 'Not set')}")
    
    # 构建命令
    agent_dir = current_dir / '.agent'
    command = f"python {agent_dir / 'commands' / 'work.py'} use test_case_generator"
    
    # 执行命令
    print(f"\n执行命令: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    # 输出结果
    print("\n命令输出:")
    print(result.stdout)
    
    if result.stderr:
        print("\n错误输出:")
        print(result.stderr)
    
    print(f"\n返回码: {result.returncode}")
    
except Exception as e:
    print(f"错误: {e}")

import os
import sys
from dotenv import load_dotenv
from pathlib import Path

# 获取当前目录
current_dir = Path(__file__).parent.absolute()
print(f"当前目录: {current_dir}")

# 检查并加载.env文件
env_paths = [
    current_dir / '_agent-local' / '.env',
    current_dir / '.agent' / '.env'
]

for env_path in env_paths:
    print(f"\n检查 {env_path}:")
    print(f"  文件存在: {env_path.exists()}")
    
    if env_path.exists():
        print(f"  文件权限: {oct(os.stat(env_path).st_mode)[-3:]}")
        print(f"  文件大小: {env_path.stat().st_size} 字节")
        
        # 尝试读取文件内容
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"  文件内容: \n{content}")
        except Exception as e:
            print(f"  读取文件失败: {e}")
        
        # 尝试加载环境变量
        try:
            print(f"  尝试加载环境变量...")
            load_dotenv(env_path)
            print(f"  加载完成")
        except Exception as e:
            print(f"  加载环境变量失败: {e}")

# 检查环境变量
env_vars = ['USER_ID', 'USER_NAME', 'LOCAL_WORKSPACE', 'LOCAL_RULES_PATH']
print("\n环境变量检查:")
for var in env_vars:
    value = os.environ.get(var)
    print(f"  {var}: {value if value else '未设置'}")

print("\n完成检查!")

"""
导入测试点文档到知识库
"""
import json
import sys
import datetime
from pathlib import Path

# 添加知识库模块路径
sys.path.append('D:/0AI/TESTCASE')
from knowledge_base import KnowledgeBaseManager

def main():
    # 读取文档信息
    with open('D:/0AI/TESTCASE/2025CASE/output/document_info.json', 'r', encoding='utf-8') as f:
        doc_info = json.load(f)

    document_path = doc_info['document_path']
    document_category = "物业接口"

    # 确保文档路径正确
    if not Path(document_path).is_absolute():
        # 如果是相对路径，尝试在不同目录下查找
        possible_paths = [
            document_path,
            Path("D:/0AI/TESTCASE/2025CASE/input") / document_path,
            Path("D:/0AI/TESTCASE") / document_path
        ]

        for path in possible_paths:
            if Path(path).exists():
                document_path = str(path)
                print(f"找到文档: {document_path}")
                break
        else:
            print(f"错误：找不到文档 {document_path}")
            sys.exit(1)

    # 创建知识库管理器
    kb = KnowledgeBaseManager()

    # 导入文档
    doc_id = kb.add_document(document_path, category=document_category)
    print(f"文档已成功导入，ID: {doc_id}")

    # 保存导入结果
    import_result = {
        "document_id": doc_id,
        "document_path": document_path,
        "document_type": "txt",
        "import_time": datetime.datetime.now().isoformat(),
        "status": "success",
        "message": "文档已成功导入知识库"
    }

    output_path = Path("D:/0AI/TESTCASE/2025CASE/output/import_result.json")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(import_result, f, ensure_ascii=False, indent=2)

    print(f"导入结果已保存到: {output_path}")

if __name__ == "__main__":
    main()

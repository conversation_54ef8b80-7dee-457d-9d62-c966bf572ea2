# 美团团购账单对账流程

## 触发机制 (Trigger Mechanism)

1. 消息触发 (Message Trigger)
   - 通过阿里云MNS消息队列服务触发
   - 消息类型：`MTTG_AUTO_ORDER`（美团团购定时拉取对账单）
   - 入口信息：
     - 入口类：`MtTgAutoOrderProcessor`
     - 入口方法：`process(JSONObject msg)`
     - 信息来源文件：
       - `src/main/java/com/acewill/omp/mq/processor/MtTgAutoOrderProcessor.java`
       - `src/main/java/com/acewill/omp/mq/message/MtTgAutoOrderMessage.java`
       - `src/main/java/com/acewill/omp/audit/service/MtTgAutoOrderService.java`
     - 处理流程：

       ```java
       @Override
       public int process(JSONObject msg) {
           MtTgAutoOrderMessage msgObj = msg.toJavaObject(MtTgAutoOrderMessage.class);
           MsgProcessThreadPools.mtTgAutoOrderThreadPoolExecutor.execute(() -> {
               try {
                   String schema = omService.getSchema(msgObj.getMid());
                   if (!StringUtils.isEmpty(schema)) {
                       SchemaHolder.push(schema);
                       mtTgAutoOrderService.processMtTgAutoOrder(msgObj);
                   }
               } finally {
                   SchemaHolder.clear();
               }
           });
           return 0;
       }
       ```

2. 手动上传触发 (Manual Upload Trigger)
   - 入口信息：
     - 入口类：`AuditBillMtController`
     - 入口方法：`importBill(MultipartFile file)`
     - 信息来源文件：
       - `src/main/java/com/acewill/omp/audit/controller/AuditBillMtController.java`
       - `src/main/java/com/acewill/omp/audit/service/BillImportRecordService.java`
       - `src/main/java/com/acewill/omp/audit/task/PullMtBillTask.java`
     - 处理流程：

       ```java
       @PostMapping("/import")
       public Result importBill(@RequestParam("file") MultipartFile file) {
           // 1. 上传文件到OSS
           String ossPath = ossService.uploadFile(file);
           // 2. 创建导入记录
           BillImportRecord record = billImportRecordService.createRecord(ossPath);
           // 3. 提交拉取任务
           PullMtBillTask task = PullMtBillTask.builder()
               .schema(schema)
               .batchId(record.getId())
               .billPath(ossPath)
               .build();
           ExecutorPools.billPullService.submit(task);
           return Result.success();
       }
       ```

3. 定时任务触发 (Scheduled Task Trigger)
   - 入口信息：
     - 入口类：`MtAutoOrderTask`
     - 入口方法：`execute()`
     - 信息来源文件：
       - `src/main/java/com/acewill/omp/audit/task/MtAutoOrderTask.java`
       - `src/main/java/com/acewill/omp/audit/service/MerchantService.java`
       - `src/main/java/com/acewill/omp/audit/service/MtTgAutoOrderService.java`
     - 处理流程：

       ```java
       @Scheduled(cron = "0 0 1 * * ?")  // 每天凌晨1点执行
       public void execute() {
           // 1. 获取需要处理的商户列表
           List<Merchant> merchants = merchantService.getActiveMerchants();
           // 2. 遍历商户处理账单
           for (Merchant merchant : merchants) {
               MtTgAutoOrderMessage message = new MtTgAutoOrderMessage();
               message.setMid(merchant.getId());
               message.setClearDate(DateUtils.getYesterday());
               mtTgAutoOrderService.processMtTgAutoOrder(message);
           }
       }
       ```

## 美团团购账单对账详细流程

```mermaid
sequenceDiagram
   participant User as 用户
   participant AuditBillMtController as 美团账单控制器
   participant BillImportRecordServiceImpl as 账单导入记录服务
   participant PullMtBillTask as 美团账单拉取任务
   participant BillProcessServiceImpl as 账单处理服务
   participant AuditBillMtService as 美团账单服务
   participant AuditBillMtDao as 美团账单数据访问层
   participant OrderService as 订单服务
   participant DB as 数据库
   participant OSS as 对象存储服务

   Note over User,OSS: 账单获取阶段
   User->>AuditBillMtController: 上传美团账单文件
   AuditBillMtController->>OSS: 存储账单文件(ossService.uploadFile)
   AuditBillMtController->>BillImportRecordServiceImpl: 创建导入记录(insert)
   Note over BillImportRecordServiceImpl: 生成批次ID(idService.nextId)<br/>设置处理状态(IMPORT_STATUS_PROCESSING)<br/>记录基本信息

   Note over User,OSS: 账单解析阶段
   BillImportRecordServiceImpl->>PullMtBillTask: 提交拉取任务(ExecutorPools.billPullService.submit)
   PullMtBillTask->>OSS: 下载账单文件(ossService.downloadExcelOss)
   PullMtBillTask->>BillProcessServiceImpl: 解析账单数据(processMtBill)
   Note over BillProcessServiceImpl: 解析文件格式<br/>提取账单数据<br/>数据格式转换<br/>数据校验

   Note over User,OSS: 账单关联阶段
   BillProcessServiceImpl->>OrderService: 查询订单数据(getMtOrderByDate)
   BillProcessServiceImpl->>BillProcessServiceImpl: 构建订单映射关系(buildOrderMapping)
   Note over BillProcessServiceImpl: 关联订单信息<br/>关联商户信息<br/>关联门店信息

   Note over User,OSS: 账单存储阶段
   BillProcessServiceImpl->>AuditBillMtService: 批量插入账单数据(insertBatch)
   AuditBillMtService->>AuditBillMtDao: 执行批量插入(batchInsert)
   Note over AuditBillMtService: 多线程处理<br/>异步存储监听<br/>错误数据处理

   Note over User,OSS: 账单稽核阶段
   BillProcessServiceImpl->>BillImportRecordServiceImpl: 更新导入记录状态(updateToAudit)
   BillProcessServiceImpl->>AuditService: 提交稽核任务(ExecutorPools.billAuditService.submit)
   AuditService->>DB: 执行自动稽核(AuditMtBillTask)
```

## 处理阶段说明

1. 账单获取阶段
   - 主要类和方法：
     - `AuditBillMtController.importBill()`: 处理账单文件上传
     - `BillImportRecordService.createRecord()`: 创建导入记录
     - `BillImportRecordServiceImpl.insert()`: 插入导入记录
   - 信息来源文件：
     - `src/main/java/com/acewill/omp/audit/controller/AuditBillMtController.java`
     - `src/main/java/com/acewill/omp/audit/service/BillImportRecordService.java`
     - `src/main/java/com/acewill/omp/audit/service/impl/BillImportRecordServiceImpl.java`
   - 处理内容：
     - 创建导入记录（生成批次ID）
     - 设置导入状态为处理中
     - 记录操作人信息
     - 记录账单数量
     - 记录错误账单数量
     - 记录账单来源
     - 记录交易日期范围
     - 记录账单OSS地址

2. 账单解析阶段
   - 主要类和方法：
     - `PullMtBillTask.execute()`: 执行账单拉取任务
     - `BillProcessService.processMtBill()`: 处理美团账单数据
     - `BillProcessServiceImpl.parseBillData()`: 解析账单数据
   - 信息来源文件：
     - `src/main/java/com/acewill/omp/audit/task/PullMtBillTask.java`
     - `src/main/java/com/acewill/omp/audit/service/BillProcessService.java`
     - `src/main/java/com/acewill/omp/audit/service/impl/BillProcessServiceImpl.java`
   - 处理内容：
     - 解析账单文件格式
     - 提取账单数据
     - 数据格式转换
     - 数据校验
     - 错误数据处理

3. 账单关联阶段
   - 主要类和方法：
     - `OrderService.getMtOrderByDate()`: 获取美团订单数据
     - `BillProcessServiceImpl.buildOrderMapping()`: 构建订单映射关系
     - `BillProcessServiceImpl.associateOrderInfo()`: 关联订单信息
   - 信息来源文件：
     - `src/main/java/com/acewill/omp/order/service/OrderService.java`
     - `src/main/java/com/acewill/omp/audit/service/impl/BillProcessServiceImpl.java`
   - 处理内容：
     - 查询订单数据
     - 构建订单映射关系
     - 关联订单信息
     - 关联商户信息
     - 关联门店信息
     - 记录关联失败订单

4. 账单存储阶段
   - 主要类和方法：
     - `AuditBillMtService.insertBatch()`: 批量插入账单数据
     - `AuditBillMtDao.batchInsert()`: 执行批量插入
     - `BillProcessServiceImpl.handleBatchInsert()`: 处理批量插入结果
   - 信息来源文件：
     - `src/main/java/com/acewill/omp/audit/service/AuditBillMtService.java`
     - `src/main/java/com/acewill/omp/audit/dao/AuditBillMtDao.java`
     - `src/main/java/com/acewill/omp/audit/service/impl/BillProcessServiceImpl.java`
   - 处理内容：
     - 批量插入账单数据
     - 多线程处理大量数据
     - 异步存储结果监听
     - 错误数据处理

5. 账单稽核阶段
   - 主要类和方法：
     - `BillImportRecordService.updateToAudit()`: 更新导入记录状态
     - `AuditService.submitAuditTask()`: 提交稽核任务
     - `AuditMtBillTask.execute()`: 执行美团账单稽核
   - 信息来源文件：
     - `src/main/java/com/acewill/omp/audit/service/BillImportRecordService.java`
     - `src/main/java/com/acewill/omp/audit/service/AuditService.java`
     - `src/main/java/com/acewill/omp/audit/task/AuditMtBillTask.java`
   - 处理内容：
     - 更新导入记录状态
     - 记录处理结果
     - 提交稽核任务
     - 触发自动稽核

## 异常处理机制

- 记录失败账单数量
- 记录失败账单编号
- 支持重试机制
- 支持部分成功处理
- 异常日志记录

## 性能优化

- 使用多线程处理
- 批量数据处理
- 异步任务处理
- 分页查询优化
- 数据库批量操作

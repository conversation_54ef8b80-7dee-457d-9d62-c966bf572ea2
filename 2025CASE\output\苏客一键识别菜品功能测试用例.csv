用例编号,用例名称,前置条件,测试步骤,预期结果,测试数据,优先级
TC-001,验证未开店时一键识别功能限制,"1. 已登录POS系统
2. 系统未开店
3. 菜品已设置AI图片","1. 在收银页面点击【一键识别】按钮","1. 系统提示不允许操作加菜
2. 不进入菜品自动识别页面",N/A,高
TC-002,验证未开店时多次点击一键识别按钮的响应,"1. 已登录POS系统
2. 系统未开店
3. 菜品已设置AI图片","1. 在收银页面点击【一键识别】按钮
2. 关闭提示信息
3. 再次点击【一键识别】按钮","1. 每次点击都提示不允许操作加菜
2. 不进入菜品自动识别页面
3. 多次点击不会导致系统异常",N/A,中
TC-003,验证未开班时一键识别功能限制,"1. 已登录POS系统
2. 系统已开店但未开班
3. 菜品已设置AI图片","1. 在收银页面点击【一键识别】按钮","1. 系统提示不允许操作加菜
2. 不进入菜品自动识别页面",N/A,高
TC-004,验证未开班状态下从其他页面返回收银页面后一键识别功能状态,"1. 已登录POS系统
2. 系统已开店但未开班
3. 菜品已设置AI图片","1. 从收银页面切换到其他功能页面
2. 返回收银页面
3. 点击【一键识别】按钮","1. 系统提示不允许操作加菜
2. 不进入菜品自动识别页面
3. 页面切换不会改变功能限制状态",N/A,中
TC-005,验证正常识别单个菜品并确认点菜,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置单个菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击确认按钮","1. 系统成功识别出菜品信息
2. 点击确认按钮后，系统成功点菜
3. 系统退出识别页面，返回收银页面
4. 收银页面显示已点的菜品",单个测试菜品样本,高
TC-006,验证正常识别多种不同菜品并确认点菜,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置多种不同菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击确认按钮","1. 系统成功识别出所有不同菜品信息
2. 点击确认按钮后，系统成功点菜
3. 系统退出识别页面，返回收银页面
4. 收银页面显示所有已点的不同菜品",多种不同测试菜品样本,高
TC-007,验证在有已点菜品的情况下使用一键识别功能,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 收银页面已有手动点的菜品
6. 餐盘已放置菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击确认按钮","1. 系统成功识别出菜品信息
2. 点击确认按钮后，系统成功点菜
3. 系统退出识别页面，返回收银页面
4. 收银页面显示原有菜品和新增的已点菜品
5. 原有菜品不受影响",测试菜品样本,中
TC-008,验证完全不可识别菜品的提示和处理,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 餐盘中放置未设置AI图片的菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统显示菜品不可识别的提示信息
2. 点击确认按钮后，系统不会加上菜
3. 系统退出识别页面，返回收银页面
4. 收银页面不显示任何新增菜品",未设置AI图片的菜品,高
TC-009,验证部分可识别部分不可识别菜品的处理,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 餐盘中同时放置已设置和未设置AI图片的菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统成功识别出已设置AI图片的菜品
2. 系统显示未设置AI图片菜品不可识别的提示信息
3. 点击确认按钮后，系统只加上可识别的菜
4. 系统退出识别页面，返回收银页面
5. 收银页面只显示已识别的菜品",已设置和未设置AI图片的菜品组合,高
TC-010,验证在光线不足条件下菜品识别的提示和处理,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 识别区光线不足","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统显示菜品不可识别或识别不准确的提示信息
2. 点击确认按钮后，系统不会加上无法识别的菜
3. 系统退出识别页面，返回收银页面
4. 收银页面不显示任何新增菜品或只显示成功识别的菜品","测试菜品样本，光线不足的环境",中
TC-011,验证两个相同菜品的识别,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘中放置两个相同菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统成功识别出两个相同菜品
2. 系统不合并相同菜品的数量，而是分别显示
3. 点击确认按钮后，系统成功点菜
4. 系统退出识别页面，返回收银页面
5. 收银页面显示已点的两个相同菜品",两个相同的测试菜品样本,高
TC-012,验证多个(3个以上)相同菜品的识别,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘中放置3个以上相同菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统成功识别出所有相同菜品
2. 系统不合并相同菜品的数量，而是分别显示
3. 点击确认按钮后，系统成功点菜
4. 系统退出识别页面，返回收银页面
5. 收银页面显示已点的所有相同菜品",3个以上相同的测试菜品样本,中
TC-013,验证多个相同菜品与其他不同菜品混合识别,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘中放置多个相同菜品和其他不同菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 点击确认按钮","1. 系统成功识别出所有菜品，包括多个相同菜品和其他不同菜品
2. 系统不合并相同菜品的数量，而是分别显示
3. 点击确认按钮后，系统成功点菜
4. 系统退出识别页面，返回收银页面
5. 收银页面显示已点的所有菜品",多个相同菜品和其他不同菜品的组合,中
TC-014,验证未识别出菜品时的二次识别成功,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片但放置位置不当","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面但未识别出菜品
3. 点击继续识别按钮
4. 调整菜品位置
5. 系统进行二次识别
6. 点击确认按钮","1. 第一次识别未识别出菜品
2. 点击继续识别按钮后，系统进入二次识别
3. 调整菜品位置后，系统成功识别出菜品
4. 点击确认按钮后，系统成功点菜
5. 系统退出识别页面，返回收银页面
6. 收银页面显示已点的菜品",测试菜品样本,高
TC-015,验证二次识别仍未识别出菜品的处理,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 餐盘中放置未设置AI图片的菜品","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面但未识别出菜品
3. 点击继续识别按钮
4. 系统进行二次识别但仍未识别出菜品
5. 点击确认按钮","1. 第一次识别未识别出菜品
2. 点击继续识别按钮后，系统进入二次识别
3. 二次识别仍未识别出菜品
4. 系统显示菜品不可识别的提示信息
5. 点击确认按钮后，系统不会加上菜
6. 系统退出识别页面，返回收银页面
7. 收银页面不显示任何新增菜品",未设置AI图片的菜品,中
TC-016,验证多次连续二次识别的处理,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片但放置位置不当","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面但未识别出菜品
3. 点击继续识别按钮
4. 系统进行二次识别但仍未识别出菜品
5. 再次点击继续识别按钮
6. 调整菜品位置
7. 系统进行第三次识别
8. 点击确认按钮","1. 第一次识别未识别出菜品
2. 点击继续识别按钮后，系统进入二次识别
3. 二次识别仍未识别出菜品
4. 再次点击继续识别按钮，系统进行第三次识别
5. 调整菜品位置后，系统成功识别出菜品
6. 点击确认按钮后，系统成功点菜
7. 系统退出识别页面，返回收银页面
8. 收银页面显示已点的菜品",测试菜品样本,低
TC-017,验证识别单个菜品后取消操作,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置单个菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击返回按钮","1. 系统成功识别出菜品信息
2. 点击返回按钮后，系统不会加上菜
3. 系统退出识别页面，返回收银页面
4. 收银页面不显示任何新增菜品",单个测试菜品样本,高
TC-018,验证识别多个菜品后取消操作,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置多个菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击返回按钮","1. 系统成功识别出所有菜品信息
2. 点击返回按钮后，系统不会加上任何菜
3. 系统退出识别页面，返回收银页面
4. 收银页面不显示任何新增菜品",多个测试菜品样本,中
TC-019,验证在有已点菜品的情况下使用一键识别后取消操作,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 收银页面已有手动点的菜品
6. 餐盘已放置菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击返回按钮","1. 系统成功识别出菜品信息
2. 点击返回按钮后，系统不会加上新菜
3. 系统退出识别页面，返回收银页面
4. 收银页面只显示原有已点菜品，不显示任何新增菜品
5. 原有菜品不受影响",测试菜品样本,中
TC-020,验证二次识别后取消操作,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片但放置位置不当","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面但未识别出菜品
3. 点击继续识别按钮
4. 调整菜品位置
5. 系统进行二次识别并成功识别出菜品
6. 点击返回按钮","1. 第一次识别未识别出菜品
2. 点击继续识别按钮后，系统进入二次识别
3. 调整菜品位置后，系统成功识别出菜品
4. 点击返回按钮后，系统不会加上菜
5. 系统退出识别页面，返回收银页面
6. 收银页面不显示任何新增菜品",测试菜品样本,中
TC-021,验证连续多次使用一键识别功能的稳定性,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并识别菜品
3. 点击确认按钮
4. 更换餐盘中的菜品
5. 重复步骤1-4至少5次","1. 系统每次都能成功识别出菜品信息
2. 每次点击确认按钮后，系统都能成功点菜
3. 系统每次都能正常退出识别页面，返回收银页面
4. 收银页面累计显示所有已点的菜品
5. 多次操作不会导致系统崩溃或异常",多组不同的测试菜品样本,中
TC-022,验证在菜品识别过程中系统响应其他操作,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置菜品到AI识别区","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面并开始识别菜品
3. 在识别过程中尝试点击页面上的其他按钮","1. 系统在识别过程中应适当限制其他操作
2. 系统不应因为用户的其他操作而崩溃
3. 识别完成后，系统应正常显示识别结果",测试菜品样本,低
TC-023,验证在网络不稳定情况下一键识别功能的表现,"1. 已登录POS系统
2. 系统已开店
3. 系统已开班
4. 菜品已设置AI图片
5. 餐盘已放置菜品到AI识别区
6. 网络连接不稳定","1. 在收银页面点击【一键识别】按钮
2. 系统进入菜品自动识别页面
3. 在识别过程中模拟网络波动","1. 系统应提示网络连接问题
2. 系统应尝试重新连接或提供重试选项
3. 系统不应因网络问题而崩溃
4. 网络恢复后，系统应能继续完成识别过程","测试菜品样本，网络波动环境",中
TC-024,验证在高峰期多终端同时使用一键识别功能的性能,"1. 多台POS终端同时登录系统
2. 所有终端已开店开班
3. 菜品已设置AI图片
4. 每台终端的餐盘已放置菜品到AI识别区","1. 所有终端同时点击【一键识别】按钮
2. 观察系统响应时间和识别准确率
3. 所有终端点击确认按钮","1. 所有终端都能成功进入菜品自动识别页面
2. 识别过程的响应时间在可接受范围内
3. 识别准确率不因并发使用而降低
4. 所有终端都能成功点菜并返回收银页面",多台终端的测试菜品样本,低

"""
知识库检索"满减"关键词
"""

import os
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

try:
    from knowledge_base import KnowledgeBaseManager
except ImportError:
    print("错误：无法导入knowledge_base模块，请确保该模块已正确安装")
    sys.exit(1)

def main():
    """知识库检索满减关键词"""
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 定义查询关键词
    keyword = "满减"
    
    # 执行查询
    results = kb.search(keyword)
    
    # 如果没有结果，尝试其他相关关键词
    if not results:
        print(f"未找到与'{keyword}'匹配的结果，尝试其他相关关键词...")
        related_keywords = ["满", "减", "活动", "优惠"]
        for related_keyword in related_keywords:
            related_results = kb.search(related_keyword)
            if related_results:
                print(f"找到与'{related_keyword}'匹配的结果:")
                for result in related_results:
                    print(f"- 文档: {result.get('title', '未知标题')}")
                    print(f"  - 相关度: {result.get('score', 0)}")
                    print(f"  - 分类: {result.get('category', '未分类')}")
                    print(f"  - 添加时间: {result.get('added_at', '未知时间')}")
                
                # 获取第一个结果的文档内容
                doc = kb.get_document(related_results[0]["id"])
                if doc:
                    print("\n文档内容:")
                    print(doc["content"])
                break
    else:
        print(f"找到与'{keyword}'匹配的结果:")
        for result in results:
            print(f"- 文档: {result.get('title', '未知标题')}")
            print(f"  - 相关度: {result.get('score', 0)}")
            print(f"  - 分类: {result.get('category', '未分类')}")
            print(f"  - 添加时间: {result.get('added_at', '未知时间')}")
        
        # 获取第一个结果的文档内容
        doc = kb.get_document(results[0]["id"])
        if doc:
            print("\n文档内容:")
            print(doc["content"])
    
    # 列出所有文档
    print("\n知识库中的所有文档:")
    all_docs = kb.list_documents()
    for doc in all_docs:
        print(f"- {doc.get('id', '未知ID')}: {doc.get('title', '未知标题')}")

if __name__ == "__main__":
    main()

import os
import sys
import subprocess
from pathlib import Path

# 获取当前目录
current_dir = Path(__file__).parent.absolute()

# 设置环境变量
os.environ['USER_ID'] = 'jiaojingjing'
os.environ['USER_NAME'] = '焦晶晶'
os.environ['LOCAL_WORKSPACE'] = '_agent-local'

# 构建命令
agent_dir = current_dir / '.agent'
work_py = agent_dir / 'commands' / 'work.py'

if not work_py.exists():
    print(f"错误: 找不到工作流命令文件: {work_py}")
    sys.exit(1)

# 构建命令
command = f"python {work_py} use test_case_generator"

# 执行命令
print(f"\n执行命令: {command}")
result = subprocess.run(command, shell=True, env=os.environ)

print(f"命令执行完成，返回码: {result.returncode}")

# 知识库文档生成使用说明

遵循的原则

1. 项目说明是概述性质的，包括了整个项目的基本信息和核心功能，每个模块应该有详细的模块文档
2. 同理如果模块比较大，涉及多个子域，那么模块文档也应该类似项目说明文档，只包含基础和核心功能，为模块创建单独目录，像项目一样组织
3. 项目说明可以多次执行，建议初始项目知识库时使用，后续可以先对模块进行知识库更新，再更新到项目说明
   1. 更新模块文档->更新项目说明，指令: "@xxx.md 已更新，请帮我同步更新到 @项目说明.md"

## 文档说明

| 文档或目录 | 位置                                | 说明                                                                     | 工作流                       |
| ---------- | ----------------------------------- | ------------------------------------------------------------------------ | ---------------------------- |
| 技术架构   | _agent-local/guidelines/技术架构.md | 技术栈、项目结构、模块划分、三方依赖等                                   | /task use server_arch        |
| 系统集成   | _agent-local/knowledge/系统集成.md  | 内外部系统依赖和交互                                                     | /task use server_integration |
| 项目说明   | _agent-local/knowledge/项目说明.md  | 包括简介、业务场景、核心功能、业务流程、用户角色等                       | /work use server_doc         |
| 业务流程   | _agent-local/knowledge/biz_flow     | 具体的业务流程梳理，包括api、任务、特定类或方法、消息等订阅              | /task use server_biz_flow    |
| api文档    | _agent-local/knowledge/api          | openapi格式api文档，后续可以先更新文档，再结合具体开发工作流完成开发工作 | /task use server_api         |
| 数据字典   | _agent-local/knowledge/数据字典.md  | 名词说明、字典值定义                                                     | /task use server_dict        |

## 使用说明

### 文档生成顺序

1. 第一步：生成技术架构文档
   - 启动技术架构工作流：`/task use server_arch`
   - 按步骤完成技术架构文档生成
   - 该文档将帮助理解项目的技术选型和架构设计

2. 第二步：生成系统集成文档
   - 启动系统集成工作流：`/task use server_integration`
   - 分析并记录所有内外部系统依赖
   - 该文档将帮助理解系统间的集成关系

3. 第三步：生成项目说明文档
   - 启动项目说明工作流：`/work use server_doc`
   - 完成项目整体说明文档
   - 该文档将提供项目的整体业务视角

4. 后续步骤：生成具体业务流程文档
   - 使用业务流程工作流记录具体流程
   - 可以针对以下场景生成具体流程文档：
     - API接口流程
     - 定时任务流程
     - 消息处理流程
     - 特定业务类或方法的处理流程

### 注意事项

1. 文档生成准备：
   - 准备好相关的代码和文档
   - 遵循工作流中的步骤和规则

2. 工作流操作：
   - 使用 `/task use <工作流名称>` 启动对应的工作流
   - 按照工作流提示的步骤进行操作
   - 每个步骤完成后使用 `/task next` 进入下一步
   - 如果需要返回上一步，使用 `/task back`

3. 文档维护：
   - 生成文档后及时检查内容的完整性和准确性
   - 定期更新文档以保持与代码的一致性
   - 重要的业务流程变更时及时更新相关文档

4. 新建工作流时，建议
   1. 将技术架构和系统集成作为基础输入文档
   2. 其他文档可以按需输入

5. 数据表生成
   1. 阅读.agent/tools/db_doc_generator/README.md

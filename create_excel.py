import pandas as pd

# 创建测试用例数据
data = {
    '用例编号': ['TC-001', 'TC-002', 'TC-003', 'TC-004', 'TC-005'],
    '用例名称': ['现金支付账单上传', '现金+找零支付账单上传', '优惠+现金支付账单上传', '微信支付账单上传', '支付宝支付账单上传'],
    '前置条件': [
        '物业参数已正常配置\n服务支持接口上传\n系统已准备好现金支付的账单数据',
        '物业参数已正常配置\n服务支持接口上传\n系统已准备好现金+找零支付的账单数据',
        '物业参数已正常配置\n服务支持接口上传\n系统已准备好优惠+现金支付的账单数据',
        '物业参数已正常配置\n服务支持接口上传\n系统已准备好微信支付的账单数据',
        '物业参数已正常配置\n服务支持接口上传\n系统已准备好支付宝支付的账单数据'
    ],
    '测试步骤': [
        '1. 使用现金方式完成支付\n2. 系统生成账单数据\n3. 系统按照物业接口要求格式化数据（优惠金额固定为0，菜品信息传空数组）\n4. 系统调用物业接口上传数据\n5. 检查物业系统返回的响应\n6. 检查系统数据库记录和日志',
        '1. 使用现金方式支付并产生找零\n2. 系统生成账单数据\n3. 系统按照物业接口要求格式化数据\n4. 系统调用物业接口上传数据\n5. 检查物业系统返回的响应\n6. 检查系统数据库记录和日志',
        '1. 应用优惠并使用现金方式完成剩余支付\n2. 系统生成账单数据\n3. 系统按照物业接口要求格式化数据（优惠金额固定为0，账单金额直接传实收）\n4. 系统调用物业接口上传数据\n5. 检查物业系统返回的响应\n6. 检查系统数据库记录和日志',
        '1. 使用微信方式完成支付\n2. 系统生成账单数据\n3. 系统按照物业接口要求格式化数据\n4. 系统调用物业接口上传数据\n5. 检查物业系统返回的响应\n6. 检查系统数据库记录和日志',
        '1. 使用支付宝方式完成支付\n2. 系统生成账单数据\n3. 系统按照物业接口要求格式化数据\n4. 系统调用物业接口上传数据\n5. 检查物业系统返回的响应\n6. 检查系统数据库记录和日志'
    ],
    '预期结果': [
        '1. 系统成功生成账单数据\n2. 数据格式符合物业接口要求（paymentMethod="CH"）\n3. 物业接口返回成功响应（errcode="0000"）\n4. 系统数据库中记录了上传成功的数据\n5. 系统日志中记录了上传成功的信息',
        '1. 系统成功生成账单数据\n2. 数据格式符合物业接口要求（paymentMethod="CH"，value为实际收款减去找零后的金额）\n3. 物业接口返回成功响应（errcode="0000"）\n4. 系统数据库中记录了上传成功的数据\n5. 系统日志中记录了上传成功的信息',
        '1. 系统成功生成账单数据\n2. 数据格式符合物业接口要求（paymentMethod="CH"，discountAmt=0，value为实际收款金额）\n3. 物业接口返回成功响应（errcode="0000"）\n4. 系统数据库中记录了上传成功的数据\n5. 系统日志中记录了上传成功的信息',
        '1. 系统成功生成账单数据\n2. 数据格式符合物业接口要求（paymentMethod="WP"）\n3. 物业接口返回成功响应（errcode="0000"）\n4. 系统数据库中记录了上传成功的数据\n5. 系统日志中记录了上传成功的信息',
        '1. 系统成功生成账单数据\n2. 数据格式符合物业接口要求（paymentMethod="AP"）\n3. 物业接口返回成功响应（errcode="0000"）\n4. 系统数据库中记录了上传成功的数据\n5. 系统日志中记录了上传成功的信息'
    ],
    '测试数据': [
        '支付方式: 现金(CH)\n订单金额: 100\n优惠金额: 0\n实收金额: 100\n订单类型: SALE',
        '支付方式: 现金(CH)\n订单金额: 80\n收款金额: 100\n找零金额: 20\n实收金额: 80\n订单类型: SALE',
        '支付方式: 现金(CH)\n订单原始金额: 100\n优惠金额: 20\n实收金额: 80\n上传的优惠金额: 0\n订单类型: SALE',
        '支付方式: 微信(WP)\n订单金额: 100\n优惠金额: 0\n实收金额: 100\n订单类型: SALE',
        '支付方式: 支付宝(AP)\n订单金额: 100\n优惠金额: 0\n实收金额: 100\n订单类型: SALE'
    ],
    '优先级': ['高', '高', '高', '高', '高']
}

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
output_path = 'D:/0AI/TESTCASE/2025CASE/output/欢乐颂店物业测试用例.xlsx'
df.to_excel(output_path, index=False, engine='openpyxl')

print(f'Excel文件已保存到: {output_path}')

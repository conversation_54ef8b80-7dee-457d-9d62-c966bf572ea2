# 苏客全自助点餐结账功能

## 功能概述
### 核心功能
- AI识别菜品
- 自动跳转支付
- 扫码完成结账

### 操作入口
- 收银页面【全自助】按钮

### 前置条件
- 已登录POS系统
- 菜品已设置AI图片

## 系统状态验证
### 未开店状态
- 不允许操作加菜
- 显示限制提示

### 未开班状态
- 不允许操作加菜
- 显示限制提示

## AI识别流程
### 正常识别
- 餐盘放置识别区
- 自动识别菜品
- 显示菜品名称序号
- 自动跳转结账页面
- 播放'请扫码支付'

### 菜品修正
- 无法识别显示[修正]
- 进入菜品信息页面
- 手动绑定菜品图片
- 已识别菜品不可修正

## 金额计算
### 计算项目
- 应收金额
- 优惠金额
- 菜品数量
- 待付金额

### 验证时机
- 菜品识别完成时
- 实时更新显示

## 操作控制
### 手动支付
- 识别成功未自动跳转时
- 点击【扫码支付】按钮
- 进入支付页面

### 重新识别
- 识别过程中
- 点击【继续识别】
- 重新开始识别

### 清空商品
- 识别过程中
- 点击【清空商品】
- 重置所有信息

## 退出机制
### 退出触发
- 点击右上角X
- 弹出员工登录页面

### 确认退出
- 输入正确密码
- 退出全自助模式

### 取消退出
- 点击取消
- 继续识别流程

## 支付流程
### 支付成功
- 扫码支付成功
- 显示成功提示
- 自动完成结账
- 生成流水记录

### 支付失败
- 扫描错误付款码
- 显示错误提示
- 不允许支付
- 可重新尝试

## 系统架构
### 状态检查模块
- 开店状态验证
- 开班状态验证

### AI识别模块
- 菜品自动识别
- 菜品信息显示
- 修正功能

### 金额计算模块
- 应收金额计算
- 优惠金额计算
- 数量统计

### 操作控制模块
- 手动支付控制
- 重新识别控制
- 清空商品控制
- 退出机制控制

### 支付处理模块
- 扫码支付处理
- 支付结果验证
- 流水记录生成
- 错误提示处理

## 业务流程
### 正常流程
- 系统状态检查
- 进入全自助模式
- AI菜品识别
- 金额计算验证
- 自动跳转支付
- 扫码支付完成
- 流水记录生成

### 异常流程
- 系统状态异常
- 菜品识别失败
- 识别过程中断
- 用户主动退出
- 支付码错误

## 测试要点
### 功能测试
- AI识别准确性
- 金额计算正确性
- 支付流程完整性
- 异常处理有效性

### 性能测试
- 识别响应时间
- 支付处理时间
- 系统稳定性

### 用户体验测试
- 操作流程顺畅性
- 提示信息清晰性
- 错误处理友好性

## 验收标准
### 功能指标
- AI识别准确率≥95%
- 金额计算100%准确
- 支付成功率≥99%

### 性能指标
- 识别响应≤3秒
- 支付处理≤5秒
- 系统稳定运行

### 体验指标
- 操作直观简单
- 错误提示清晰
- 异常恢复快速

# 苏客全自助点餐结账功能

## 功能概述
### 功能简介
- 将餐盘放置到AI识别区
- 自动识别菜品
- 跳转扫码支付页面
- 扫码自动支付

### 功能按钮位置
- 收银页面中间区域
- 【全自助】按钮

### 前置条件
- 已登录POS系统
- 菜品已设置AI图片

## 功能测试点

### 系统状态验证
#### 未开店状态
- 【全自助】不允许操作加菜
- 系统提示限制信息
- 保持在收银页面

#### 未开班状态
- 【全自助】不允许操作加菜
- 系统提示限制信息
- 保持在收银页面

### 核心识别流程
#### 正常识别流程
- 点【全自助】进入全自助点餐结账流程
- 餐盘放置到可识别区域
- 自动识别出菜品
- 菜品上方显示菜品名称及序号
- 稳定后自动跳转至结账页面
- 声音播放'请扫码支付'
- 顾客扫付款码自动结账

#### 菜品修正功能
##### 无法识别菜品处理
- 无法识别的菜品上方显示[修正]字样
- 进入菜品信息页面
- 手动将此次识别的菜品图片绑定给选定菜品
- 修正后显示正确菜品信息

##### 已识别菜品限制
- 已识别的菜品不允许进行修正
- 不显示[修正]字样
- 点击无响应

### 金额计算验证
#### 金额核查项目
- 应收金额
- 优惠金额
- 菜品数量
- 待付金额

#### 验证时机
- 菜品自动识别时
- 核查左侧对应的金额信息是否正确
- 实时更新计算结果

### 操作控制功能
#### 手动支付控制
##### 触发条件
- 菜品正在识别中
- 已确定识别成功
- 还未自动进入付款页面

##### 操作方式
- 手动点【扫码支付】按钮
- 进入扫码付款页面
- 显示支付二维码

#### 重新识别功能
##### 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 操作方式
- 点【继续识别】
- 重新识别菜品
- 清除之前识别结果

#### 清空商品功能
##### 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 操作方式
- 点【清空商品】
- 清空后开始重新识别菜品
- 重置所有金额信息

### 退出机制
#### 退出触发
##### 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 操作步骤
- 点右上角X
- 弹出员工登录页面

#### 退出确认
##### 确认退出
- 输入密码确定
- 退出全自助模式
- 返回收银页面

##### 取消退出
- 取消操作
- 退出员工登录页面
- 继续识别菜品

### 支付流程验证
#### 支付成功处理
##### 支付完成
- 扫码支付成功
- 显示支付成功提示
- 自动完成结账

##### 流水验证
- 查看流水记录
- 核查账单付款方式
- 核查金额是否正确
- 验证交易时间

#### 支付失败处理
##### 错误场景
- 扫码付款页面
- 扫错误的付款码
- 网络连接异常

##### 系统响应
- 有提示信息
- 不允许支付
- 可重新尝试

## 功能架构总览

### 主要模块
#### 系统状态检查模块
- 开店状态验证
- 开班状态验证
- 权限检查

#### AI识别模块
- 菜品自动识别
- 菜品信息显示
- 修正功能
- 识别准确性验证

#### 金额计算模块
- 应收金额计算
- 优惠金额计算
- 菜品数量统计
- 待付金额计算

#### 操作控制模块
- 手动支付控制
- 重新识别控制
- 清空商品控制
- 退出机制控制

#### 支付处理模块
- 扫码支付处理
- 支付结果验证
- 流水记录生成
- 错误提示处理

### 业务流程
#### 正常流程
- 系统状态检查
- 进入全自助模式
- AI菜品识别
- 金额计算验证
- 自动跳转支付
- 扫码支付完成
- 流水记录生成

#### 异常流程
- 系统状态异常 → 禁止操作
- 菜品识别失败 → 手动修正
- 识别过程中断 → 重新识别或清空
- 用户主动退出 → 员工验证退出
- 支付码错误 → 错误提示重试

### 关键验证点
#### 功能验证
- AI识别准确性
- 金额计算正确性
- 支付流程完整性
- 异常处理有效性

#### 用户体验验证
- 操作流程顺畅性
- 提示信息清晰性
- 错误处理友好性
- 响应速度合理性

## 测试场景分类

### 正常场景测试
#### 基础功能测试
- 单个菜品识别
- 多个菜品识别
- 金额计算验证
- 支付流程完整性

#### 操作控制测试
- 手动支付触发
- 重新识别功能
- 清空商品功能
- 正常退出流程

### 异常场景测试
#### 系统状态异常
- 未开店状态限制
- 未开班状态限制
- 权限验证失败

#### 识别异常处理
- 无法识别菜品修正
- 识别过程中断处理
- 网络异常恢复

#### 支付异常处理
- 错误付款码处理
- 支付超时处理
- 网络中断恢复

### 边界条件测试
#### 极限情况
- 空餐盘处理
- 大量菜品识别
- 同种菜品重复识别

#### 性能测试
- 识别响应时间
- 支付处理时间
- 系统稳定性验证

## 质量保证

### 测试覆盖率
#### 功能覆盖
- 所有功能点100%覆盖
- 正常流程完整验证
- 异常流程充分测试

#### 场景覆盖
- 用户操作场景全覆盖
- 系统状态变化全覆盖
- 错误处理场景全覆盖

### 验收标准
#### 功能正确性
- AI识别准确率≥95%
- 金额计算100%准确
- 支付成功率≥99%

#### 性能指标
- 识别响应时间≤3秒
- 支付处理时间≤5秒
- 系统稳定运行无崩溃

#### 用户体验
- 操作流程直观简单
- 错误提示清晰明确
- 异常恢复快速有效

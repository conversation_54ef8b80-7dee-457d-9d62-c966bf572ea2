# 文本解析结果

## 原始文件信息
- 文件名称：租户上传销售接口.md
- 文件类型：Markdown
- 文件路径：D:\0AI\TESTCASE\2025CASE\input\租户上传销售接口.md
- 解析时间：2023-05-15

## 功能点列表

### 正常销售上传
- 描述：通过接口salesTransLiteV61上传销售数据
- 优先级：高
- 相关业务规则：
  - 必须提供完整的请求参数（apiKey, signature, docKey等）
  - 销售项目金额总和必须与销售总计金额一致
  - 支付方式金额总和必须与销售总计金额一致
  - 商品编号必须有效
  - 店铺号和收银机号必须有效

### 覆盖历史销售
- 描述：通过接口salestransreplacelitev61覆盖历史销售数据
- 优先级：中
- 相关业务规则：
  - 同一日期、同一店铺号、同一收银机号上传销售时，历史销售会清零
  - 以最后一笔上传的销售数据为准

### 退货功能
- 描述：使用原销售单信息进行退货操作
- 优先级：高
- 相关业务规则：
  - 退货时netQty和netAmount为负数
  - 必须提供原销售单信息（SalesMemo）

### 多种付款方式组合
- 描述：支持使用多种付款方式组合支付
- 优先级：中
- 相关业务规则：
  - 支付方式金额总和必须与销售总计金额一致

### 并发上传处理
- 描述：处理同时上传多笔相同店铺的销售数据
- 优先级：低
- 相关业务规则：
  - 当有销售单据在处理队列中时，会返回特定错误（-67）

### 网络错误恢复
- 描述：处理网络中断后的重新上传
- 优先级：中
- 相关业务规则：
  - 系统应能正确处理网络中断后的重新上传
  - 如果是重复上传，应提示已存在

## 业务规则列表

### BR-001
- 描述：必填参数验证
- 适用范围：所有接口调用
- 约束条件：apiKey, docKey, transHeader等参数必须提供，否则返回对应错误码（-1至-11）

### BR-002
- 描述：金额一致性验证
- 适用范围：所有销售上传
- 约束条件：
  - salesItem中的netAmount总和必须与salesTotal中的netAmount一致，否则返回错误（-18）
  - salesTender中的payAmount总和必须与salesTotal中的netAmount一致，否则返回错误（-19）

### BR-003
- 描述：商品编号验证
- 适用范围：所有销售上传
- 约束条件：商品编号必须有效，否则返回错误（-12）

### BR-004
- 描述：店铺和收银机验证
- 适用范围：所有销售上传
- 约束条件：店铺号和收银机号必须有效，否则返回错误（-16, -17）

### BR-005
- 描述：销售单号唯一性验证
- 适用范围：所有销售上传
- 约束条件：docKey必须唯一，否则返回错误（销售单号已经存在）

### BR-006
- 描述：覆盖历史销售规则
- 适用范围：覆盖历史销售功能
- 约束条件：同一日期、同一店铺号、同一收银机号上传销售时，历史销售会清零，以最后一笔为准

### BR-007
- 描述：退货处理规则
- 适用范围：退货功能
- 约束条件：退货时netQty和netAmount为负数，必须提供原销售单信息

### BR-008
- 描述：并发处理规则
- 适用范围：并发上传场景
- 约束条件：当有销售单据在处理队列中时，会返回错误（-67）

### BR-009
- 描述：网络错误恢复规则
- 适用范围：网络中断恢复场景
- 约束条件：系统应能正确处理网络中断后的重新上传，如果是重复上传，应提示已存在

## 数据字典

### apiKey
- 类型：字符串
- 描述：API访问密钥
- 取值范围：有效的API密钥（测试环境为LVGEMHSL）
- 默认值：无

### docKey
- 类型：字符串
- 描述：销售单号，必须唯一
- 取值范围：唯一标识符
- 默认值：无

### storecode
- 类型：字符串
- 描述：店铺号
- 取值范围：有效的店铺号（测试环境为09210029）
- 默认值：无

### tillid
- 类型：字符串
- 描述：收银机号
- 取值范围：有效的收银机号（测试环境为00）
- 默认值：无

### cashier
- 类型：字符串
- 描述：收银员编号
- 取值范围：有效的收银员编号（测试环境为09210029）
- 默认值：无

### netQty
- 类型：数值
- 描述：销售数量，退货时为负数
- 取值范围：任意有效数值
- 默认值：无

### netAmount
- 类型：数值
- 描述：销售金额，退货时为负数
- 取值范围：任意有效数值
- 默认值：无

### txDate
- 类型：日期
- 描述：交易日期
- 取值范围：有效的日期格式
- 默认值：无

### ledgerDatetime
- 类型：日期时间
- 描述：账本日期时间
- 取值范围：有效的日期时间格式
- 默认值：无

### payAmount
- 类型：数值
- 描述：支付金额
- 取值范围：任意有效数值
- 默认值：无

## 其他关键信息

### 接口信息
- 接口名称：salesTransLiteV61（正常销售）和salestransreplacelitev61（覆盖历史销售）
- 接口方式：Restful接口(HTTP POST)
- 数据格式：JSON，编码为UTF-8
- 测试环境URL：http://kc.lvgemgroup.com.cn:8185/posservice/rest/salestransaction/salestranslitev61

### 测试环境参数
- apikey：LVGEMHSL
- 店铺号storecode：09210029
- 收银机号tillid：00
- 收银员编号cashier：09210029
- 商品编号：0921002901
- 付款方式编码：CH

### 版本信息
- 当前版本：V1.2 (2020-04-03)
- 版本特点：
  - 增加了交易笔数字段
  - 增加了覆盖历史销售的功能
  - 提供了完整的请求示例和响应示例
  - 包含详细的错误代码列表

### 错误代码
- -1至-11：各种必填参数缺失错误
- -12：商品编号无效
- -16, -17：店铺号和收银机号无效
- -18, -19：金额不一致错误
- -21：边界条件错误
- -67：销售单据在处理队列中错误

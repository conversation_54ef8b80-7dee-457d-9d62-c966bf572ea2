"""
查看知识库中的文档
"""
import json
import sys
from pathlib import Path

# 添加知识库模块路径
sys.path.append('D:/0AI/TESTCASE')
from knowledge_base import KnowledgeBaseManager

def main():
    """查看知识库中的文档"""
    # 读取导入结果
    with open('D:/0AI/TESTCASE/2025CASE/output/import_result.json', 'r', encoding='utf-8') as f:
        import_result = json.load(f)

    document_id = import_result['document_id']
    
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 获取文档
    document = kb.get_document(document_id)
    
    if document:
        print(f"文档ID: {document_id}")
        print(f"文档标题: {document.get('title', '未知')}")
        print(f"文档分类: {document.get('category', '未知')}")
        print(f"添加时间: {document.get('added_at', '未知')}")
        print("\n文档内容:")
        print(document.get('content', '无内容'))
    else:
        print(f"未找到ID为 {document_id} 的文档")
    
    # 列出所有文档
    print("\n所有文档列表:")
    all_docs = kb.list_documents()
    for doc in all_docs:
        print(f"- {doc.get('id', '未知ID')}: {doc.get('title', '未知标题')} ({doc.get('category', '未知分类')})")

if __name__ == "__main__":
    main()

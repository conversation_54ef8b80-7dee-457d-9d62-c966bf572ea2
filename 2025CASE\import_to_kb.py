import sys
import os
from datetime import datetime

# 添加知识库模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '_agent-local'))

try:
    from knowledge_base import KnowledgeBaseManager
    
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 导入文档
    doc_path = "D:\\0AI\\TESTCASE\\2025CASE\\input\\营销中心标签满减POS应用.txt"
    doc_id = kb.add_document(doc_path, category="满减活动")
    
    print(f"文档已成功导入知识库，文档ID: {doc_id}")
    print(f"导入时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 列出所有文档
    print("\n知识库中的所有文档:")
    all_docs = kb.list_documents()
    for doc in all_docs:
        print(f"- {doc.get('id', 'unknown')}: {doc.get('title', 'unknown')}")
    
    # 搜索文档
    print("\n搜索结果 (关键词: '满减活动'):")
    results = kb.search("满减活动")
    for result in results:
        print(f"- 文档: {result.get('title', 'unknown')}")
        print(f"  相关度: {result.get('score', 0)}")
        print(f"  分类: {result.get('category', 'unknown')}")
        
except Exception as e:
    print(f"错误: {e}")

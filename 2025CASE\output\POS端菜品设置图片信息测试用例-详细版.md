# POS端菜品设置图片信息测试用例-详细版

## 测试点1: 仅【餐类明细设定】页签，允许设置图片；其他页签均只允许查看，无设置图片功能

### TC-001: 验证餐类明细设定页签图片设置功能可用性
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【餐类明细设定】页签
  3. 选择一个菜品明细
  4. 观察是否有图片设置相关功能
- **预期结果**：
  1. 成功进入餐类明细设定页签
  2. 选中菜品明细后，系统自动弹出图片采样页面
  3. 图片设置功能可用

### TC-002: 验证餐谱设定页签无图片设置功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【餐谱设定】页签
  3. 查找页面上是否有图片设置相关功能
- **预期结果**：
  1. 成功进入餐谱设定页签
  2. 页面上没有图片设置相关功能或该功能被禁用

### TC-003: 验证餐别设定页签无图片设置功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【餐别设定】页签
  3. 查找页面上是否有图片设置相关功能
- **预期结果**：
  1. 成功进入餐别设定页签
  2. 页面上没有图片设置相关功能或该功能被禁用

### TC-004: 验证餐类设定页签无图片设置功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【餐类设定】页签
  3. 查找页面上是否有图片设置相关功能
- **预期结果**：
  1. 成功进入餐类设定页签
  2. 页面上没有图片设置相关功能或该功能被禁用

### TC-005: 验证功能按钮设定页签无图片设置功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【功能按钮设定】页签
  3. 查找页面上是否有图片设置相关功能
- **预期结果**：
  1. 成功进入功能按钮设定页签
  2. 页面上没有图片设置相关功能或该功能被禁用

### TC-006: 验证付款方式设定页签无图片设置功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户有权限访问系统设置-查看餐谱设定
- **测试步骤**：
  1. 进入系统设置-查看餐谱设定
  2. 点击【付款方式设定】页签
  3. 查找页面上是否有图片设置相关功能
- **预期结果**：
  1. 成功进入付款方式设定页签
  2. 页面上没有图片设置相关功能或该功能被禁用

## 测试点2: 【餐类明细设定】页签，左侧展示餐类，右侧展示对应类下菜品明细

### TC-007: 验证餐类明细设定页签的界面布局
- **优先级**：高
- **测试类型**：UI测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 观察页面布局
  2. 确认左侧是否展示餐类列表
  3. 确认右侧是否展示菜品明细区域
- **预期结果**：
  1. 页面左侧展示餐类列表
  2. 页面右侧展示菜品明细区域
  3. 界面布局清晰，符合设计要求

### TC-008: 验证选择不同餐类时右侧菜品明细的更新
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 系统中存在多个餐类
- **测试步骤**：
  1. 点击左侧第一个餐类
  2. 观察右侧菜品明细区域
  3. 点击左侧第二个餐类
  4. 观察右侧菜品明细区域是否更新
- **预期结果**：
  1. 点击第一个餐类后，右侧显示该餐类下的菜品明细
  2. 点击第二个餐类后，右侧菜品明细更新为该餐类下的菜品
  3. 菜品明细信息正确对应所选餐类

### TC-009: 验证餐类为空时的界面展示
- **优先级**：中
- **测试类型**：异常测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 系统中存在无菜品的空餐类
- **测试步骤**：
  1. 点击左侧无菜品的空餐类
  2. 观察右侧菜品明细区域
- **预期结果**：
  1. 右侧菜品明细区域显示为空或显示适当的提示信息
  2. 系统不出现错误或异常

## 测试点3: 只选中餐类，未选中右侧菜品明细，不允许设置图片

### TC-010: 验证仅选中餐类时图片设置功能的限制
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 在左侧仅选中一个餐类，不选中右侧任何菜品明细
  2. 尝试设置图片（查找图片设置相关按钮或功能）
- **预期结果**：
  1. 系统不允许设置图片
  2. 图片设置相关按钮或功能不可用或不显示

### TC-011: 验证仅选中餐类时的系统提示
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 在左侧仅选中一个餐类，不选中右侧任何菜品明细
  2. 尝试通过快捷键或其他方式触发图片设置功能
- **预期结果**：
  1. 系统可能显示提示信息，指示用户需要选择菜品明细
  2. 不会弹出图片采样页面

## 测试点4: 选中菜品明细，自动弹出图片采样页面

### TC-012: 验证选中菜品明细后自动弹出图片采样页面
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 在左侧选中一个餐类
  2. 在右侧选中一个菜品明细
- **预期结果**：
  1. 系统自动弹出图片采样页面
  2. 弹出的页面标题和内容与所选菜品相关

### TC-013: 验证连续选择不同菜品明细时的图片采样页面
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 在左侧选中一个餐类
  2. 在右侧选中第一个菜品明细
  3. 关闭弹出的图片采样页面
  4. 在右侧选中第二个菜品明细
- **预期结果**：
  1. 选中第一个菜品明细后，系统自动弹出图片采样页面
  2. 关闭页面后，选中第二个菜品明细，系统再次自动弹出图片采样页面
  3. 第二次弹出的页面内容与第二个菜品相关

### TC-014: 验证在不同餐类下选择菜品明细的图片采样页面
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
- **测试步骤**：
  1. 在左侧选中第一个餐类
  2. 在右侧选中一个菜品明细
  3. 关闭弹出的图片采样页面
  4. 在左侧选中第二个餐类
  5. 在右侧选中一个菜品明细
- **预期结果**：
  1. 在不同餐类下选择菜品明细，均能自动弹出对应的图片采样页面
  2. 弹出的页面内容与所选菜品相关

## 测试点5: 未设置过图片的菜品-图片采样页面，除【采样】和【返回】按钮可操作，其他展示信息仅允许查看

### TC-015: 验证未设置图片的菜品采样页面按钮可用性
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个未设置过图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 尝试点击【采样】按钮
  3. 尝试点击【返回】按钮
- **预期结果**：
  1. 【采样】按钮可点击，点击后弹出商品采集页面
  2. 【返回】按钮可点击，点击后关闭图片采样页面
  3. 按钮响应正常，无异常

### TC-016: 验证未设置图片的菜品采样页面其他区域不可操作
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个未设置过图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 尝试操作除【采样】和【返回】按钮外的其他区域
  3. 尝试点击或操作信息展示区域
- **预期结果**：
  1. 除【采样】和【返回】按钮外，其他区域不可操作
  2. 信息展示区域仅供查看，点击无反应
  3. 系统不出现错误或异常

### TC-017: 验证未设置图片的菜品采样页面信息展示
- **优先级**：中
- **测试类型**：UI测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个未设置过图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 检查页面上显示的菜品信息
- **预期结果**：
  1. 页面正确显示所选菜品的相关信息
  2. 信息展示区域没有图片或显示默认图片占位符
  3. 界面布局整洁，信息清晰可读

## 测试点6: 已设置过图片的菜品-图片采样页面，【采样】按钮右侧区域展示已上传的图片信息，每张图片下，有【删除】按钮，且可操作

### TC-018: 验证已设置图片的菜品采样页面图片展示
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个已设置过图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 检查【采样】按钮右侧区域
- **预期结果**：
  1. 【采样】按钮右侧区域展示已上传的图片信息
  2. 图片清晰可见，大小适中
  3. 可能显示图片的相关信息（如上传时间、大小等）

### TC-019: 验证已设置图片的菜品采样页面删除按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个已设置过图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 确认每张图片下是否有【删除】按钮
  3. 点击其中一张图片下的【删除】按钮
- **预期结果**：
  1. 每张图片下方都有【删除】按钮
  2. 点击【删除】按钮后，系统可能弹出确认对话框
  3. 确认后，对应图片被成功删除，不再显示在页面上

### TC-020: 验证删除所有图片后的页面状态
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个只有一张图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 点击图片下的【删除】按钮
  3. 确认删除操作
  4. 观察删除后的页面状态
- **预期结果**：
  1. 删除唯一图片后，页面状态应变为未设置图片的状态
  2. 【采样】按钮右侧区域不再显示图片
  3. 页面布局可能会调整，但不应出现错误或异常

### TC-021: 验证多张图片的删除功能
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个有多张图片的菜品明细
- **测试步骤**：
  1. 观察弹出的图片采样页面
  2. 点击第一张图片下的【删除】按钮
  3. 确认删除操作
  4. 点击另一张图片下的【删除】按钮
  5. 确认删除操作
- **预期结果**：
  1. 每次删除操作后，对应图片被移除
  2. 其他图片仍然正常显示
  3. 删除操作不影响其他图片的显示和功能

## 测试点7: 点【采集】按钮，弹出【商品采集】页面

### TC-022: 验证点击采集按钮弹出商品采集页面
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 已弹出图片采样页面
- **测试步骤**：
  1. 点击图片采样页面上的【采集】按钮
- **预期结果**：
  1. 系统弹出【商品采集】页面
  2. 页面标题为"商品采集"或类似文字
  3. 页面包含采样、全选、反选、移除、上传、保存图片等按钮

### TC-023: 验证从未设置图片的菜品采样页面进入商品采集页面
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个未设置过图片的菜品明细，弹出图片采样页面
- **测试步骤**：
  1. 点击图片采样页面上的【采集】按钮
- **预期结果**：
  1. 系统弹出【商品采集】页面
  2. 商品采集页面初始状态应为空或默认状态
  3. 所有功能按钮可用

### TC-024: 验证从已设置图片的菜品采样页面进入商品采集页面
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入系统设置-查看餐谱设定-餐类明细设定页签
  - 选中一个已设置过图片的菜品明细，弹出图片采样页面
- **测试步骤**：
  1. 点击图片采样页面上的【采集】按钮
- **预期结果**：
  1. 系统弹出【商品采集】页面
  2. 商品采集页面初始状态应为空或默认状态
  3. 所有功能按钮可用

## 测试点8: 【商品采集】页面：采样、全选、反选、移除、上传、保存图片、右上角X按钮均可正常使用

### TC-025: 验证商品采集页面采样按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 系统连接了摄像头或图片采集设备
- **测试步骤**：
  1. 点击【采样】按钮
  2. 观察系统响应
  3. 完成采样操作
- **预期结果**：
  1. 点击【采样】按钮后，系统激活摄像头或图片采集设备
  2. 成功采集图片后，图片显示在商品采集页面上
  3. 采样功能正常工作，无错误或异常

### TC-026: 验证商品采集页面采样按钮在无采集设备时的处理
- **优先级**：中
- **测试类型**：异常测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 系统未连接摄像头或图片采集设备
- **测试步骤**：
  1. 点击【采样】按钮
  2. 观察系统响应
- **预期结果**：
  1. 系统应显示适当的错误提示，指示没有可用的采集设备
  2. 系统不应崩溃或出现未处理的异常

### TC-027: 验证商品采集页面全选按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片
- **测试步骤**：
  1. 点击【全选】按钮
  2. 观察图片选择状态
- **预期结果**：
  1. 点击【全选】按钮后，页面上所有图片都被选中
  2. 图片选中状态有明显的视觉标识（如边框高亮或勾选标记）

### TC-028: 验证商品采集页面反选按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片，部分图片已被选中
- **测试步骤**：
  1. 记录当前选中和未选中的图片
  2. 点击【反选】按钮
  3. 观察图片选择状态变化
- **预期结果**：
  1. 点击【反选】按钮后，之前选中的图片变为未选中状态
  2. 之前未选中的图片变为选中状态
  3. 所有图片的选择状态都被反转

### TC-029: 验证商品采集页面移除按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片，部分图片已被选中
- **测试步骤**：
  1. 点击【移除】按钮
  2. 观察选中图片的变化
- **预期结果**：
  1. 点击【移除】按钮后，所有选中的图片被从页面中移除
  2. 未选中的图片保持不变
  3. 移除操作不影响系统稳定性

### TC-030: 验证商品采集页面上传按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片，部分图片已被选中
- **测试步骤**：
  1. 点击【上传】按钮
  2. 观察上传过程和结果
- **预期结果**：
  1. 点击【上传】按钮后，系统开始上传选中的图片
  2. 上传过程中可能显示进度指示
  3. 上传成功后，系统可能显示成功提示
  4. 上传的图片与菜品正确关联

### TC-031: 验证商品采集页面上传按钮在网络异常时的处理
- **优先级**：中
- **测试类型**：异常测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片，部分图片已被选中
  - 模拟网络连接异常
- **测试步骤**：
  1. 点击【上传】按钮
  2. 观察系统对网络异常的处理
- **预期结果**：
  1. 系统应显示适当的错误提示，指示网络连接问题
  2. 系统应提供重试选项或其他恢复机制
  3. 系统不应崩溃或出现未处理的异常

### TC-032: 验证商品采集页面保存图片按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 页面上已有多张采集的图片，部分图片已被选中
- **测试步骤**：
  1. 点击【保存图片】按钮
  2. 观察保存过程和结果
- **预期结果**：
  1. 点击【保存图片】按钮后，系统可能弹出保存对话框
  2. 用户可以选择保存位置（如果适用）
  3. 选中的图片被成功保存到指定位置
  4. 保存操作完成后，系统可能显示成功提示

### TC-033: 验证商品采集页面右上角X按钮功能
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
- **测试步骤**：
  1. 点击页面右上角的【X】按钮
- **预期结果**：
  1. 点击【X】按钮后，商品采集页面关闭
  2. 返回到图片采样页面
  3. 关闭操作不影响系统稳定性

### TC-034: 验证商品采集页面在有未保存更改时关闭的处理
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
  - 用户已进行了一些操作（如采样新图片），但尚未保存或上传
- **测试步骤**：
  1. 点击页面右上角的【X】按钮
- **预期结果**：
  1. 系统可能弹出确认对话框，询问是否保存更改
  2. 用户可以选择保存、不保存或取消关闭操作
  3. 根据用户选择执行相应操作

### TC-035: 验证商品采集页面多图片同时操作
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
- **测试步骤**：
  1. 连续点击【采样】按钮多次，采集多张图片
  2. 点击【全选】按钮选中所有图片
  3. 点击【上传】按钮上传所有图片
  4. 返回图片采样页面查看结果
- **预期结果**：
  1. 多张图片可以被成功采集
  2. 全选功能正常工作，选中所有图片
  3. 多张图片可以同时上传
  4. 上传后，所有图片都显示在图片采样页面上

### TC-036: 验证商品采集页面按钮组合操作
- **优先级**：中
- **测试类型**：功能测试
- **前置条件**：
  - 用户已登录POS系统
  - 用户已进入【商品采集】页面
- **测试步骤**：
  1. 连续点击【采样】按钮多次，采集多张图片
  2. 点击【全选】按钮选中所有图片
  3. 点击【反选】按钮反转选择
  4. 点击【移除】按钮移除当前选中的图片
  5. 点击【全选】按钮选中剩余图片
  6. 点击【上传】按钮上传剩余图片
- **预期结果**：
  1. 所有按钮操作正常执行
  2. 按钮之间的组合操作不会导致系统错误或异常
  3. 最终上传的图片正确显示在图片采样页面上

"""
搜索满减相关的测试点
"""
import sys
from pathlib import Path

# 添加知识库模块路径
sys.path.append('D:/0AI/TESTCASE')
from knowledge_base import KnowledgeBaseManager

def main():
    """搜索满减相关的测试点"""
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 搜索关键词
    keyword = "满减"
    
    # 执行搜索
    results = kb.search(keyword)
    
    print(f"关键词 '{keyword}' 的搜索结果:")
    print("-" * 50)
    
    if results:
        for i, result in enumerate(results, 1):
            doc_id = result.get("id", "未知ID")
            title = result.get("metadata", {}).get("title", "未知标题")
            score = result.get("score", 0)
            category = result.get("metadata", {}).get("category", "未分类")
            
            print(f"{i}. 文档: {title}")
            print(f"   ID: {doc_id}")
            print(f"   相关度: {score}")
            print(f"   分类: {category}")
            
            # 获取文档内容
            doc = kb.get_document(doc_id)
            if doc:
                content = doc.get("content", "")
                # 提取包含关键词的片段
                lines = content.split("\n")
                keyword_lines = []
                for j, line in enumerate(lines):
                    if keyword in line:
                        start = max(0, j-1)
                        end = min(len(lines), j+2)
                        keyword_lines.extend(lines[start:end])
                        keyword_lines.append("...")
                
                if keyword_lines:
                    print("   相关内容片段:")
                    print("   " + "\n   ".join(keyword_lines[:10]))
            
            print("-" * 50)
    else:
        print("未找到匹配的结果")

if __name__ == "__main__":
    main()

# 文本解析结果

## 原始文件信息
- 文件名称：欢乐颂店物业接口验证.txt
- 文件类型：TXT
- 文件路径：欢乐颂店物业接口验证.txt
- 解析时间：2023-04-18 14:00

## 功能点列表
### 欢乐颂店物业接口验证
- 描述：验证欢乐颂店铺与物业系统之间的接口功能
- 优先级：高
- 相关业务规则：
  - 账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收
  - 菜品信息传空数组，不需要传菜品详细信息
  - 正常账单类型SALE和退单类型ONLINEREFUND
  - 订单时间格式：yyyyMMddHHmmss

### 账单付款场景
- 描述：验证各种付款方式下的账单处理
- 优先级：高
- 相关业务规则：
  - 支持多种支付方式：现金、微信、支付宝、挂账
  - 支持组合支付：现金+找零、优惠+现金、优惠+微信、优惠+支付宝、现金+挂账
  - 支持0金额账单处理

### 数据上传物业
- 描述：验证各种付款场景下的数据能否正确上传到物业系统
- 优先级：高
- 相关业务规则：
  - 账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收
  - 菜品信息传空数组，不需要传菜品详细信息
  - 正常账单类型SALE
  - 订单时间格式：yyyyMMddHHmmss

### 退单数据上传物业
- 描述：验证各种付款场景的退单数据能否正确上传到物业系统
- 优先级：高
- 相关业务规则：
  - 账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收
  - 菜品信息传空数组，不需要传菜品详细信息
  - 退单类型ONLINEREFUND
  - 订单时间格式：yyyyMMddHHmmss

## 业务规则列表
### BR-001
- 描述：账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收
- 适用范围：所有账单上传场景
- 约束条件：无论实际优惠金额是多少，上传时都设为0

### BR-002
- 描述：菜品信息传空数组，不需要传菜品详细信息
- 适用范围：所有账单上传场景
- 约束条件：无需包含菜品明细数据

### BR-003
- 描述：正常账单类型SALE和退单类型ONLINEREFUND
- 适用范围：所有账单上传场景
- 约束条件：必须使用指定的账单类型标识

### BR-004
- 描述：订单时间格式：yyyyMMddHHmmss
- 适用范围：所有账单上传场景
- 约束条件：时间必须严格按照指定格式

## 数据字典
### 账单类型
- 类型：字符串
- 描述：标识账单的类型
- 取值范围：SALE（正常账单）、ONLINEREFUND（退单）
- 默认值：SALE

### 订单时间
- 类型：字符串
- 描述：记录订单创建的时间
- 取值范围：符合yyyyMMddHHmmss格式的时间字符串
- 默认值：当前时间

### 支付方式
- 类型：字符串
- 描述：标识账单的支付方式
- 取值范围：现金、微信、支付宝、挂账、组合支付
- 默认值：无

### 优惠金额
- 类型：数值
- 描述：账单中的优惠金额
- 取值范围：任意非负数值
- 默认值：0

## 其他关键信息
前置条件：物业参数已正常配置，服务支持接口上传。测试场景包括多种支付方式和组合支付方式，以及正常账单和退单两种情况。所有场景都需要验证数据能否正确上传到物业系统。

name: "名称"
description: "描述"

# 任务目标列表，可通过 pre_steps 中执行脚本 task.py update来设定，设定后每个目标都会执行steps中的步骤
todos: []

# todos确定前对的前置步骤，一般用来确认并创建任务目标(todos)
pre_steps:
  - title: "询问用户要开发什么功能点"
    rule:
      - "如果有已知的工作任务，必须询问用户是否开发此工作任务"
      - "必须用户同意后，才能进入下一步 /task next"
  - title: "创建功能点开发工作流"
    rule:
      - "请根据用户描述，整理JSON_STRING，然后执行脚本 task.py update {{JSON_STRING}}"
      - JSON_STRING 模板: |
          "{\"title\": \"xxxx\", \"description\": \"xxxx\"}"

# 完成任务所需的步骤列表，可通过 /task reset 重置 steps所有步骤的状态，重新开始任务，例如 问题修复失败重新按步骤分析修复
steps:
  # 步骤
  - title: "收集信息"
    # 步骤的规则，一般定义了如何完成这个步骤、需要做的事情、要遵守的规则
    rule:
      - "询问用户所属模块"
      - "请用户提供需求文档、设计稿"
      - "查看 project.md 了解项目架构和相关知识库文档位置"
      - "使用 ls -R h5/src 搜索src目录中该模块现有的 views、workflow、store、api、test，找出该模块相关文件，与用户确认，用户确认后 使用 /task memory '{\"key\": \"value\"}' 记住这些信息"
      - "先查看测试用例及是否有要开发的功能点相关的用例，再对比实际代码实现，看看实现程度"
      - "需要用户确认"
    # AI输出的内容及格式要求
    output:
      - |
        {{输出内容、markdown格式}}

  - title: "设计测试用例、实际代码的整体实现方案"
    rule:
      - "需要与用户确认"
      - "需要评估是否涉及修改测试用例"
      - "先阅读、分析现有的用例、代码，在当前基础上进行设计"
      - "要在现有代码的基础上给出精准的修改方案"
      - "修改的代码必须符合现有代码的风格、结构、规范(guide文档)"
      - "不要删除现有的注释，除非注释的代码也被整体删除了"
      - "等待用户确认方案，并提醒用户是否需要写入到 output doc 指定的文件中，覆盖原有内容"
    output:
      - doc: -agent-local/workspace/feature_design.md
        请按需遵循以下格式输出: |
          {{输出内容、markdown格式}}

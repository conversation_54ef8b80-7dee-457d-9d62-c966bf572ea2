name: "数据字典工作流"
description: "生成系统数据字典，包括名词说明、数据字典"

todos: []

pre_steps:
  - title: "读取技术架构文档"
    rule:
      - "读取 _agent-local/guidelines/技术架构.md 文件"
      - "了解系统的整体架构和技术选型"
      - "必须完成阅读后才能进入下一步"

  - title: "询问用户需要分析哪个模块或文件夹或全部项目文档代码"
    rule:
      - "询问用户要分析的范围（具体模块、文件夹或全部项目）"
      - "必须用户同意后，才能进入下一步 /task next"

steps:
  - title: "收集信息"
    rule:
      - "读取相关文件，按模块识别、收集常量、枚举，系统用到的名词"
      - "使用 grep_search 工具搜索所有枚举类和常量类，排除测试目录"
      - "使用 grep_search 工具搜索所有实体类中的字段注释，排除测试目录"
      - "使用 grep_search 工具搜索所有接口文档中的名词，排除测试目录"
      - "整理收集到的信息，按模块分类"
      - "需要用户确认收集的信息是否完整"

  - title: "生成数据字典"
    rule:
      - "生成数据字典.md文件，包含以下内容："
      - "1. 名词说明表格："
      - "   - 表格列：名词、说明、变量名（没有则不填，多个就都列出来）"
      - "   - 名词不需要按模块划分"
      - "2. 常量和枚举定义："
      - "   - 按照业务模块划分"
      - "   - 通用常量归类到通用类别"
      - "   - 必须列出引用的源文件"
      - "需要用户确认文档内容是否完整"

  - title: "完成确认"
    rule:
      - "确认所有信息都已收集完整"
      - "确认文档格式是否正确"
      - "确认文档内容是否准确"
      - "需要用户最终确认"
    output:
      - doc: _agent-local/knowledge/数据字典.md

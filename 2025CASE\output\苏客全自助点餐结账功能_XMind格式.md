# 苏客全自助点餐结账功能

## 功能概述
### 功能简介
- 将餐盘放置到AI识别区
- 自动识别菜品
- 跳转扫码支付页面
- 扫码自动支付

### 功能按钮位置
- 收银页面中间区域
- 【全自助】按钮

### 前置条件
- 已登录POS系统
- 菜品已设置AI图片

## 功能测试点

### 1. 系统状态验证
#### 1.1 未开店状态
- 【全自助】不允许操作加菜

#### 1.2 未开班状态
- 【全自助】不允许操作加菜

### 2. 核心识别流程
#### 2.1 正常识别流程
- 点【全自助】进入全自助点餐结账流程
- 餐盘放置到可识别区域
- 自动识别出菜品
- 菜品上方显示菜品名称及序号
- 稳定后自动跳转至结账页面
- 声音播放'请扫码支付'
- 顾客扫付款码自动结账

#### 2.2 菜品修正功能
##### 2.2.1 无法识别菜品处理
- 无法识别的菜品上方显示[修正]字样
- 进入菜品信息页面
- 手动将此次识别的菜品图片绑定给选定菜品

##### 2.2.2 已识别菜品限制
- 已识别的菜品不允许进行修正

### 3. 金额计算验证
#### 3.1 金额核查项目
- 应收金额
- 优惠金额
- 菜品数量
- 待付金额

#### 3.2 验证时机
- 菜品自动识别时
- 核查左侧对应的金额信息是否正确

### 4. 操作控制功能
#### 4.1 手动支付控制
##### 4.1.1 触发条件
- 菜品正在识别中
- 已确定识别成功
- 还未自动进入付款页面

##### 4.1.2 操作方式
- 手动点【扫码支付】按钮
- 进入扫码付款页面

#### 4.2 重新识别功能
##### 4.2.1 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 4.2.2 操作方式
- 点【继续识别】
- 重新识别菜品

#### 4.3 清空商品功能
##### 4.3.1 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 4.3.2 操作方式
- 点【清空商品】
- 清空后开始重新识别菜品

### 5. 退出机制
#### 5.1 退出触发
##### 5.1.1 触发条件
- 菜品正在识别中
- 未进入付款页面时

##### 5.1.2 操作步骤
- 点右上角X
- 弹出员工登录页面

#### 5.2 退出确认
##### 5.2.1 确认退出
- 输入密码确定
- 退出全自助模式

##### 5.2.2 取消退出
- 取消操作
- 退出员工登录页面
- 继续识别菜品

### 6. 支付流程验证
#### 6.1 支付成功处理
##### 6.1.1 支付完成
- 扫码支付成功

##### 6.1.2 流水验证
- 查看流水记录
- 核查账单付款方式
- 核查金额是否正确

#### 6.2 支付失败处理
##### 6.2.1 错误场景
- 扫码付款页面
- 扫错误的付款码

##### 6.2.2 系统响应
- 有提示信息
- 不允许支付

## 功能架构总览

### 主要模块
#### 系统状态检查模块
- 开店状态验证
- 开班状态验证

#### AI识别模块
- 菜品自动识别
- 菜品信息显示
- 修正功能

#### 金额计算模块
- 应收金额计算
- 优惠金额计算
- 菜品数量统计
- 待付金额计算

#### 操作控制模块
- 手动支付控制
- 重新识别控制
- 清空商品控制
- 退出机制控制

#### 支付处理模块
- 扫码支付处理
- 支付结果验证
- 流水记录生成
- 错误提示处理

### 业务流程
#### 正常流程
1. 系统状态检查
2. 进入全自助模式
3. AI菜品识别
4. 金额计算验证
5. 自动跳转支付
6. 扫码支付完成
7. 流水记录生成

#### 异常流程
1. 系统状态异常 → 禁止操作
2. 菜品识别失败 → 手动修正
3. 识别过程中断 → 重新识别或清空
4. 用户主动退出 → 员工验证退出
5. 支付码错误 → 错误提示重试

### 关键验证点
#### 功能验证
- AI识别准确性
- 金额计算正确性
- 支付流程完整性
- 异常处理有效性

#### 用户体验验证
- 操作流程顺畅性
- 提示信息清晰性
- 错误处理友好性
- 响应速度合理性

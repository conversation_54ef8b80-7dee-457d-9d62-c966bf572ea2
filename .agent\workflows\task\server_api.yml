name: "API分析工作流"
description: "分析给定的API或具体类/文件，生成OpenAPI格式文档"

todos: []

pre_steps:
  - title: "询问用户要分析的API"
    rule:
      - "询问用户要分析的API或类/文件"
      - "确认API所属的模块/类/文件"
      - "必须用户同意后，才能进入下一步 /task next"

  - title: "创建API分析待办列表"
    rule:
      - "根据用户提供的信息，整理需要分析的API列表"
      - "将API按照所属类/文件进行分组"
      - "请根据用户描述，整理JSON_STRING，然后执行脚本 task.py update {{JSON_STRING}}"
      - JSON_STRING 模板: |
          "{\"title\": \"分析 [类/文件名] 的API\", \"description\": \"需要分析的API列表：[API1, API2, ...]\"}"

steps:
  - title: "准备分析环境"
    rule:
      - "读取 _agent-local/guidelines/技术架构.md 了解项目架构"
      - "确认API文档输出目录 _agent-local/knowledge/api 是否存在"
      - "记录当前要分析的API信息到任务记忆中"
    output:
      - |
        项目架构信息：
        - xxx
        API文档存储位置：_agent-local/knowledge/api
        待分析API列表：
        - [类/文件1]
          - API1
          - API2
        - [类/文件2]
          - API3
          - API4

  - title: "API分析"
    rule:
      - "询问用户是否有补充信息"
      - "检查API相关的依赖文件是否存在"
      - "检查API相关的代码注释是否完整"
      - "如果发现缺失的依赖文件或注释，列出清单并请求用户提供"
      - "分析API的请求方法、路径、参数、响应等信息"
      - "生成OpenAPI格式的文档"
      - "需要用户确认分析结果"
    output:
      - |
        API分析报告：

        缺失信息清单：
        - 依赖文件：
          - [缺失的文件1]
          - [缺失的文件2]
        - 缺失注释：
          - [文件1] 中的 [方法/类] 缺少 [参数/返回值] 注释
          - [文件2] 中的 [方法/类] 缺少 [参数/返回值] 注释

        路径：/api/xxx
        方法：GET/POST/PUT/DELETE
        描述：xxx

        请求参数：
        - name: xxx
          type: string
          required: true
          description: xxx

        响应：
        ```json
        {
          "code": 0,
          "message": "success",
          "data": {}
        }
        ```

  - title: "更新API文档"
    rule:
      - "询问用户API文档的文件名"
      - "确认文档位置：_agent-local/knowledge/api/[用户确认的文件名].yml"
      - "根据分析结果，更新对应的API文档文件"
      - "更新待办列表，移除已完成的API"
      - "检查是否还有待分析的API"
      - "如果还有待分析的API，返回到'API分析'步骤"
      - "如果所有API都已分析完成，进入下一步"
    output:
      - |
        文档更新状态：
        - 更新文件：_agent-local/knowledge/api/[用户确认的文件名].yml
        - 已完成API：xxx
        - 待完成API：xxx

  - title: "完成确认"
    rule:
      - "确认所有API都已完成分析"
      - "确认所有文档都已正确保存"
      - "获取用户最终确认"
    output:
      - |
        工作完成报告：
        1. 已完成API文档：
           - [类/文件1]: x个API
           - [类/文件2]: x个API
        2. 文档存储位置：
           - _agent-local/knowledge/api/xxx/xxx.yml
           - _agent-local/knowledge/api/xxx/xxx.yml

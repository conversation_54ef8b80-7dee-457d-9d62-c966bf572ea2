id: jiaojingjing_test_point_knowledge_0507_11_57
name: 测试点知识库工作流
description: 将测试点TXT或MD文档导入知识库，并支持检索和管理
rules:
  - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
  - 所有输出文件保存路径为D:\0AI\TESTCASE\2025CASE\output\
  - 知识库文件保存路径为_agent-local/knowledge/test_points
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
status: pending
user_id: jiaojingjing
user_name: 焦晶晶
tasks:
  - name: 确认输入文档
    description: 确认用户要导入知识库的文档
    worker: developer
    prompt: |
      请询问用户是否以当前打开的TXT或MD文档作为输入文档，如果是，请获取文档路径；
      如果不是，请询问用户提供要处理的文档路径。
      用户确认后，将文档路径保存到工作记忆中，然后执行 /work next 进入下一任务。
    rules:
      - 必须明确询问用户是否以当前打开的TXT或MD文档作为输入
      - 必须获取用户确认后才能继续
      - 必须记录文档路径以供后续任务使用
      - 如果用户没有当前打开的文档，需引导用户提供文档路径
      - 所有输入文件保存路径为D:\0AI\TESTCASE\2025CASE\input\
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\document_info.json
        template: |
          {
            "document_path": "{{文档路径}}",
            "document_type": "{{文档类型}}",
            "confirmed_by_user": true
          }
    status: todo
  - name: 文档解析与导入
    description: 解析文档并导入知识库
    worker: developer
    rules:
      - 读取并解析文档内容
      - 提取测试点信息
      - 将文档导入知识库
      - 必须使用knowledge_base模块进行操作
      - 必须保留原始文档的关键信息，不可遗漏重要内容
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\document_info.json
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\import_result.json
        template: |
          {
            "document_id": "{{文档ID}}",
            "document_path": "{{文档路径}}",
            "document_type": "{{文档类型}}",
            "import_time": "{{导入时间}}",
            "status": "success",
            "message": "文档已成功导入知识库"
          }
    status: todo
  - name: 知识库检索演示
    description: 演示如何检索知识库中的内容
    worker: developer
    rules:
      - 演示如何使用knowledge_base模块检索知识库
      - 提供示例查询并展示结果
      - 说明如何根据关键词、分类等条件进行检索
      - 展示如何查看完整的测试点信息
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\import_result.json
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\search_demo.md
        template: |
          # 知识库检索演示

          ## 导入信息
          - 文档ID: {{文档ID}}
          - 文档路径: {{文档路径}}
          - 导入时间: {{导入时间}}

          ## 检索示例
          {{#each 检索示例}}
          ### 示例 {{@index+1}}: {{查询关键词}}

          **查询代码:**
          ```python
          from knowledge_base import KnowledgeBaseManager

          kb = KnowledgeBaseManager()
          results = kb.search("{{查询关键词}}")
          ```

          **检索结果:**
          {{#each 结果}}
          - 文档: {{title}}
            - 相关度: {{score}}
            - 分类: {{category}}
            - 添加时间: {{added_at}}
          {{/each}}
          {{/each}}

          ## 如何使用知识库

          ### 导入文档
          ```python
          from knowledge_base import KnowledgeBaseManager

          kb = KnowledgeBaseManager()
          doc_id = kb.add_document("path/to/document.txt", category="功能测试")
          print(f"文档已导入，ID: {doc_id}")
          ```

          ### 检索文档
          ```python
          # 关键词搜索
          results = kb.search("登录功能")

          # 按分类搜索
          results = kb.search("登录功能", category="功能测试")

          # 限制结果数量
          results = kb.search("登录功能", limit=5)
          ```

          ### 查看文档
          ```python
          doc = kb.get_document("doc_id")
          print(doc["title"])
          print(doc["content"])
          ```

          ### 列出所有文档
          ```python
          all_docs = kb.list_documents()
          for doc in all_docs:
              print(f"{doc['id']}: {doc['title']}")
          ```

          ### 删除文档
          ```python
          kb.delete_document("doc_id")
          ```
    status: todo
  - name: 知识库管理指南
    description: 提供知识库管理的指南
    worker: developer
    rules:
      - 说明如何管理知识库
      - 提供知识库的备份和恢复方法
      - 说明如何更新和删除知识库中的文档
      - 提供知识库的统计信息
    input:
      - doc: D:\0AI\TESTCASE\2025CASE\output\import_result.json
    output:
      - doc: D:\0AI\TESTCASE\2025CASE\output\knowledge_base_guide.md
        template: |
          # 测试点知识库管理指南

          ## 知识库概述
          测试点知识库用于存储和管理测试点文档，支持TXT和MD格式的文档导入，并提供全文检索功能。

          ## 知识库结构
          - 知识库根目录: `_agent-local/knowledge/test_points`
          - 文档存储目录: `_agent-local/knowledge/test_points/documents`
          - 索引目录: `_agent-local/knowledge/test_points/index`
          - 元数据文件: `_agent-local/knowledge/test_points/metadata.json`

          ## 知识库管理

          ### 导入文档
          ```python
          from knowledge_base import KnowledgeBaseManager

          kb = KnowledgeBaseManager()

          # 导入单个文档
          doc_id = kb.add_document("path/to/document.txt", category="功能测试")

          # 批量导入目录下的所有文档
          import os
          from pathlib import Path

          docs_dir = Path("path/to/documents")
          for file in docs_dir.glob("*.txt"):
              kb.add_document(str(file), category="功能测试")
          ```

          ### 更新文档
          ```python
          kb.update_document("doc_id", "path/to/new_document.txt")
          ```

          ### 删除文档
          ```python
          kb.delete_document("doc_id")
          ```

          ### 备份知识库
          ```python
          import shutil
          from datetime import datetime

          # 备份整个知识库
          backup_dir = f"knowledge_base_backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
          shutil.copytree("_agent-local/knowledge/test_points", backup_dir)
          ```

          ### 恢复知识库
          ```python
          import shutil

          # 恢复知识库
          shutil.rmtree("_agent-local/knowledge/test_points")
          shutil.copytree("path/to/backup", "_agent-local/knowledge/test_points")
          ```

          ### 知识库统计
          ```python
          stats = kb.get_statistics()
          print(f"总文档数: {stats['total_documents']}")
          print("分类统计:")
          for category, count in stats['categories'].items():
              print(f"  - {category}: {count}文档")
          print(f"创建时间: {stats['created_at']}")
          print(f"最后更新: {stats['updated_at']}")
          ```

          ## 最佳实践
          1. **定期备份**: 定期备份知识库，避免数据丢失
          2. **分类管理**: 使用合理的分类体系，便于管理和检索
          3. **定期维护**: 定期清理过时的文档，保持知识库的时效性
          4. **标准化导入**: 尽量使用标准化的格式导入文档，提高解析质量
          5. **增量更新**: 对于大型文档，优先考虑更新而非删除后重新导入

          ## 常见问题
          1. **索引损坏**: 如果索引损坏，可以删除索引目录，知识库会自动重建索引
          2. **导入失败**: 检查文档格式是否正确，尝试使用不同的编码打开文件
          3. **搜索无结果**: 检查搜索关键词是否正确，尝试使用更通用的关键词
          4. **性能问题**: 当文档数量过多时，可能会影响性能，考虑分库管理
    status: todo
work_path: D:\0AI\TESTCASE\_agent-local\workspace\jiaojingjing_test_point_knowledge_0507_11_57\work.yml
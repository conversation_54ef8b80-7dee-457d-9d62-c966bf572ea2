# OMP审计服务技术架构文档

## 1. 技术栈

### 1.1 基础技术栈

- 开发语言：Java 8
- 框架：Spring Boot 2.3.12.RELEASE
- 微服务框架：Spring Cloud Hoxton.SR12
- 服务发现：Nacos Discovery
- 配置中心：Nacos Config
- 服务熔断：Sentinel
- Web服务器：Undertow（替换默认的Tomcat）
- 项目构建：Maven

### 1.2 数据存储

- 数据库：
  - MySQL（mysql-connector-java）：主要数据存储
  - PostgreSQL（spring-boot-acewill-pg-starter）：支持动态数据源
- 缓存：Redis
  - spring-boot-starter-data-redis：Redis基础操作
  - redisson：分布式锁和高级特性
- 对象存储：阿里云OSS（aliyun-sdk-oss 3.11.1）

### 1.3 ORM框架

- 框架类型：MyBatis
- 版本：MyBatis 3.5.x（Spring Boot 2.3.12.RELEASE内置）
- 配置方式：
  - 基于注解和XML混合配置
  - 使用PageHelper实现分页
  - 使用MyBatis Generator自动生成代码
- Mapper文件位置：
  - Java接口：src/main/java/com/acewill/omp/*/mapper/
  - XML映射文件：src/main/resources/mapper/
- 主要特性：
  - 支持动态SQL
  - 支持多数据源（MySQL和PostgreSQL）
  - 支持分页查询
  - 支持批量操作
- 代码生成工具：
  - MyBatis Generator（版本1.3.5）
  - 用于自动生成实体类、Mapper接口和XML映射文件

### 1.4 中间件

- 消息队列：
  - 阿里云MNS（aliyun-sdk-mns 1.1.9）
  - Beanstalkd（beanstalkc 2.3.0）
- 缓存：Redis
- 服务调用：OpenFeign
- 认证中心：CAS（spring-boot-acewill-cas-starter）

### 1.5 DevOps

- CI/CD：GitLab CI（.gitlab-ci.yml）
- 监控：Spring Boot Actuator
- 容器化：支持Docker部署
- 微服务治理：
  - 服务注册与发现：Nacos
  - 配置管理：Nacos Config
  - 熔断限流：Sentinel

## 2. 系统架构

### 2.1 分层设计

- 整体架构：采用经典的三层架构（Controller-Service-DAO）结合DDD思想的分层设计
- 各层职责：
  - Controller层：处理HTTP请求，参数校验，权限控制
  - Service层：业务逻辑处理，事务管理
  - DAO/Mapper层：数据访问，ORM映射
  - Entity层：领域模型，业务实体
  - VO/DTO层：数据传输对象
  - Util层：工具类
  - Config层：配置类
  - Exception层：异常处理

### 2.2 目录结构

```txt
src/main/java/com/acewill/omp/
├── alarm/                 # 告警模块
├── audit/                 # 审计核心模块
│   ├── constant/         # 常量定义
│   ├── controller/       # 控制器
│   ├── entity/          # 实体类
│   ├── excel/           # Excel处理
│   ├── listener/        # 事件监听器
│   ├── mapper/          # 数据访问层
│   ├── param/           # 请求参数
│   ├── service/         # 业务逻辑层
│   ├── task/           # 定时任务
│   ├── util/           # 工具类
│   └── vo/             # 视图对象
├── common/               # 公共组件
│   ├── converter/       # 类型转换器
│   ├── response/        # 统一响应
│   ├── serializer/      # 序列化
│   └── util/           # 工具类
├── config/               # 配置类
├── exception/            # 异常处理
├── id/                   # ID生成器
├── mq/                   # 消息队列
│   ├── bt/             # Beanstalk消息队列
│   ├── consumer/       # 消息消费者
│   ├── message/        # 消息定义
│   ├── mns/           # 阿里云MNS
│   ├── processor/      # 消息处理器
│   └── producer/       # 消息生产者
├── org/                  # 组织架构
├── oss/                  # 对象存储
├── pay/                  # 支付模块
├── permission/           # 权限模块
└── rpc/                  # 远程调用
```

### 2.3 模块划分

- 核心业务模块：
  - audit：审计核心功能
    - 支持多种支付渠道（支付宝、微信、美团等）的账单审计
    - 提供账单导入、对账、差异分析等功能
    - 包含定时任务进行自动对账
  - org：组织架构管理
    - 商户管理
    - 门店管理
    - 权限管理
  - pay：支付中心
    - 支付渠道管理
    - 交易中心服务

- 基础设施模块：
  - common：公共组件
    - 统一响应处理
    - 工具类库
    - 类型转换器
  - config：配置管理
    - Redis配置
    - 认证过滤器
  - exception：异常处理
    - 统一异常处理
    - 自定义异常定义
  - permission：权限控制
    - 数据权限注解
    - 权限拦截器

- 存储模块：
  - oss：对象存储服务
    - 阿里云OSS客户端
    - 文件上传下载服务

- 消息模块：
  - mq：消息队列
    - 支持Beanstalk和阿里云MNS
    - 消息生产者和消费者
    - 消息处理器
  - id：分布式ID生成器
    - 雪花算法实现

- 监控模块：
  - alarm：告警功能
    - 微信告警
    - 告警配置管理

### 2.4 ORM架构

- 数据访问层设计：
  - 采用DAO/Mapper模式
  - 使用接口定义数据访问方法
  - 通过XML配置SQL映射
  - 支持动态数据源切换
- Mapper文件组织：
  - 按业务模块划分Mapper接口
  - 主要包含审计相关Mapper（Audit*Mapper）
  - 订单相关Mapper（Order*Mapper）
  - 账单相关Mapper（Bill*Mapper）
  - 配置相关Mapper（*ConfigMapper）
- 实体类设计：
  - 使用Lombok简化实体类编写
  - 实体类与数据库表结构一一对应
  - 支持序列化和反序列化
- 查询方式：
  - 支持注解方式：@Select、@Insert等
  - 支持XML方式：更复杂的SQL语句
  - 支持动态SQL：if、choose、foreach等
  - 支持分页查询：PageHelper
- 事务管理：
  - 使用Spring声明式事务
  - 支持事务传播机制
  - 支持事务隔离级别配置

### 2.5 第三方依赖

- 云服务：
  - 阿里云OSS：文件存储
  - 阿里云MNS：消息服务
- 认证服务：
  - CAS：单点登录
- 数据库：
  - MySQL：主要数据存储
  - PostgreSQL：动态数据源支持
  - Redis：缓存和分布式锁
- 工具库：
  - EasyExcel：Excel处理
  - FastJSON：JSON处理
  - OkHttp：HTTP客户端
  - Redisson：分布式功能

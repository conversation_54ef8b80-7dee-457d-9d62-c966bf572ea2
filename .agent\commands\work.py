#!/usr/bin/env python3
import sys
import yaml
import os
import shutil
import json
import uuid
import requests
from datetime import datetime
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
from dotenv import load_dotenv
# 修改导入语句，将相对导入改为绝对导入
from record import get_user_info, report_action

# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

# 加载环境变量
env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
# print(f"Loading .env from: {env_path}")
load_dotenv(env_path)

def get_absolute_path(path: str, base_dir: str) -> str:
    """将路径转换为绝对路径
    如果是绝对路径直接返回，否则基于base_dir转换为绝对路径
    """
    if not path:
        return path
    return path if os.path.isabs(path) else os.path.join(base_dir, path)

# 获取环境变量并根据需要转换为绝对路径
workspace = get_absolute_path(os.getenv('LOCAL_WORKSPACE'), project_root)
rules_path = get_absolute_path(os.getenv('LOCAL_RULES_PATH'), project_root)

# 工作目录相关路径
WORKSPACE_DIR = os.path.join(workspace, 'workspace')

# 工作流目录路径
LOCAL_TASK_DIR = os.path.join(workspace, 'workflows', 'task')
AGENT_TASK_DIR = os.path.join(project_root, '.agent', 'workflows', 'task')

def update_user_workflows_status(user_name):
    """将用户的所有进行中工作流改为pending状态"""
    workspace_path = Path(workspace)
    for file in workspace_path.rglob('work.yml'):
        try:
            data = load_yaml(str(file))
            if data.get('user_name') == user_name and data.get('status') == 'in_progress':
                data['status'] = 'pending'
                save_yaml(str(file), data)
        except Exception as e:
            print(f"Error updating {file}: {e}")

def list_user_workflows(user_name):
    """列出属于指定用户的工作流"""
    workflows = []
    workspace_path = Path(workspace)

    for file in workspace_path.rglob('work.yml'):
        try:
            data = load_yaml(str(file))
            if data.get('user_name') == user_name:
                workflows.append({
                    'id': data.get('id', ''),
                    'name': data.get('name', ''),
                    'status': data.get('status', ''),
                    'path': str(file)
                })
        except Exception as e:
            print(f"Error reading {file}: {e}")

    return workflows

def switch_workflow(workflow_id, user_name):
    """切换到指定ID的工作流"""
    # 先将当前用户的所有进行中工作流改为pending
    update_user_workflows_status(user_name)

    # 查找并激活目标工作流
    workspace_path = Path(workspace)
    for file in workspace_path.rglob('work.yml'):
        try:
            data = load_yaml(str(file))
            if data.get('id') == workflow_id and data.get('user_name') == user_name:
                data['status'] = 'in_progress'
                save_yaml(str(file), data)
                return True, data
        except Exception as e:
            print(f"Error updating {file}: {e}")

    return False, None

def find_workflow_template(workflow_name):
    """查找工作流模板文件路径"""
    # 优先在本地工作流目录下查找
    local_template_path = os.path.join(LOCAL_WORK_DIR, f'{workflow_name}.yml')
    # 如果在本地目录下没找到，则在agent工作流目录下查找
    if os.path.exists(local_template_path):
        return local_template_path
    else:
        agent_template_path = os.path.join(AGENT_WORK_DIR, f'{workflow_name}.yml')
        if os.path.exists(agent_template_path):
            return agent_template_path
    return None

def load_yaml(file_path: str) -> dict:
    """加载yaml文件"""
    with open(file_path, 'r', encoding='utf-8', newline='') as f:
        return yaml.safe_load(f)

def save_yaml(file_path: str, data: dict):
    """保存yaml文件"""
    def str_presenter(dumper, data):
        if len(data.splitlines()) > 1:  # check for multiline string
            return dumper.represent_scalar('tag:yaml.org,2002:str', data, style='|')
        return dumper.represent_scalar('tag:yaml.org,2002:str', data)

    yaml.add_representer(str, str_presenter)

    class OrderedDumper(yaml.SafeDumper):
        def increase_indent(self, flow=False, indentless=False):
            return super().increase_indent(flow, False)

    def dict_representer(dumper, data):
        return dumper.represent_mapping('tag:yaml.org,2002:map', data.items())

    def list_representer(dumper, data):
        return dumper.represent_sequence('tag:yaml.org,2002:seq', data, flow_style=False)

    OrderedDumper.add_representer(dict, dict_representer)
    OrderedDumper.add_representer(list, list_representer)
    OrderedDumper.add_representer(str, str_presenter)

    with open(file_path, 'w', encoding='utf-8', newline='') as f:
        yaml_content = yaml.dump(data, Dumper=OrderedDumper, allow_unicode=True, sort_keys=False, indent=2, default_flow_style=False)
        # 修复缩进格式
        lines = yaml_content.splitlines()
        formatted_lines = []
        for line in lines:
            if line.startswith('    -'):
                formatted_lines.append(line.replace('    -', '  -'))
            else:
                formatted_lines.append(line)
        f.write('\n'.join(formatted_lines))

def validate_work_yaml(data: dict) -> bool:
    """验证工作YAML是否符合标准格式"""
    required_fields = ['name', 'tasks']
    for field in required_fields:
        if field not in data:
            print(f"Error: missing required field '{field}'")
            return False

    for task in data.get('tasks', []):
        if 'name' not in task:
            print("Error: task missing required field 'name'")
            return False
        if 'status' not in task:
            task['status'] = 'todo'

    return True

def generate_subtasks(task: dict) -> List[dict]:
    """根据steps和objects生成子任务"""
    if not task.get('steps') or not task.get('objects'):
        return []

    subtasks = []
    for obj in task['objects']:
        for step in task['steps']:
            subtask = {
                'name': f"{obj['name']}-{step['name']}",  # 只使用对象名称和步骤名称
                'status': 'todo',
                'description': step.get('description', ''),
                'worker': step.get('worker', task.get('worker')),
                'rule': step.get('rule', []),
                'input': step.get('input', []),
                'output': step.get('output', [])
            }
            subtasks.append(subtask)
    return subtasks

def format_tasks(tasks: List[dict], current_task_index: Optional[int] = None, indent_level: int = 1) -> str:
    """格式化任务列表输出

    Args:
        tasks: 任务列表
        current_task_index: 当前任务索引
        indent_level: 缩进级别

    Returns:
        格式化后的任务列表字符串
    """
    lines = []
    indent = "    " * indent_level

    # 先添加已完成的任务
    completed_tasks = [t for t in tasks if t.get('status') == 'done']
    for task in completed_tasks:
        lines.append(f"{indent}- [x] {task['name']} (已完成)")

    # 再添加未完成的任务
    for i, task in enumerate(tasks):
        if task.get('status') == 'done':
            continue

        is_current = i == current_task_index
        has_unfinished_subtasks = False
        if task.get('subtasks'):
            for st in task['subtasks']:
                if st.get('status') != 'done':
                    has_unfinished_subtasks = True
                    break

        # 状态标记
        if has_unfinished_subtasks and is_current:  # 必须同时满足两个条件
            status_mark = "·"
            status_text = "(进行中)"
        else:
            status_mark = " "
            if is_current:
                status_text = "(当前任务)"
            else:
                status_text = "(未开始)"

        line = f"{indent}- [{status_mark}] {task['name']} {status_text}"
        lines.append(line)

        # 如果有子任务，只显示任务说明
        if has_unfinished_subtasks and is_current:  # 只有当前进行中的任务才显示子任务
            if task.get('description'):
                lines.append(f"{indent}    任务说明: {task['description']}")
            if task.get('subtasks'):
                lines.append(f"{indent}    子任务列表:")
                subtask_lines = []
                max_show_undo = 3;
                unshow_left = 0;
                # 显示子任务
                for j, subtask in enumerate(task['subtasks']):
                    # 已完成的子任务
                    if subtask.get('status') == 'done':
                        subtask_lines.append(f"{indent}    - [x] {subtask['name']} (已完成)")
                    else:
                        # 当前子任务
                        if j == task.get('current_subtask_index'):
                            subtask_lines.append(f"{indent}    - [ ] {subtask['name']} (当前任务)")
                            # 显示当前子任务的详细信息
                            if subtask.get('worker'):
                                subtask_lines.append(f"{indent}        执行角色: {subtask['worker']}")
                            if subtask.get('description'):
                                subtask_lines.append(f"{indent}        任务说明: {subtask['description']}")
                            if subtask.get('input'):
                                subtask_lines.append(f"{indent}        前置文档:")
                                for doc in subtask['input']:
                                    subtask_lines.append(f"{indent}            - {doc}")
                            if subtask.get('rule'):
                                subtask_lines.append(f"{indent}        当前任务规则:")
                                for rule in subtask['rule']:
                                    subtask_lines.append(f"{indent}            - {rule}")
                        else:
                            max_show_undo -= 1
                            if max_show_undo > 0:
                                subtask_lines.append(f"{indent}    - [ ] {subtask['name']} (未开始)")
                            else:
                                unshow_left += 1
                lines.append('\n'.join(subtask_lines))
                if unshow_left > 0:
                    lines.append(f"{indent}    ... 共 {unshow_left} 个子任务未显示 ...")
            continue

        if is_current:
            if task.get('description'):
                lines.append(f"{indent}    任务说明: {task['description']}")

            if task.get('worker'):
                lines.append(f"{indent}    执行角色: {task['worker']}")

            if task.get('input'):
                lines.append(f"{indent}    前置文档:")
                for doc in task['input']:
                    lines.append(f"{indent}        - {doc}")

            if task.get('output'):
                lines.append(f"{indent}    任务输出文档:")
                output = task['output']
                if isinstance(output, list):
                    for doc in output:
                        if isinstance(doc, dict):
                            if 'doc' in doc:
                                lines.append(f"{indent}        - {doc['doc']}")
                            if 'template' in doc:
                                lines.append(f"{indent}            文档模板:")
                                lines.append(f"{indent}            ```")
                                for template_line in doc['template'].splitlines():
                                    lines.append(f"{indent}            {template_line}")
                                lines.append(f"{indent}            ```")
                        else:
                            lines.append(f"{indent}        - {doc}")
                elif isinstance(output, dict):
                    for doc_name, doc_content in output.items():
                        lines.append(f"{indent}        - {doc_name}")
                        if isinstance(doc_content, dict) and doc_content.get('template'):
                            lines.append(f"{indent}            文档模板:")
                            lines.append(f"{indent}            ```")
                            for template_line in doc_content['template'].splitlines():
                                lines.append(f"{indent}            {template_line}")
                            lines.append(f"{indent}            ```")

            if task.get('rules'):
                lines.append(f"{indent}    任务规则:")
                rules = task['rules']
                if isinstance(rules, list):
                    for rule in rules:
                        if isinstance(rule, str) and len(rule.strip()) > 0:
                            # 处理多行规则
                            rule_lines = rule.strip().splitlines()
                            for i, line in enumerate(rule_lines):
                                if i == 0:  # 第一行使用 - 标记
                                    lines.append(f"{indent}        - {line}")
                                else:  # 后续行保持相同缩进
                                    lines.append(f"{indent}          {line}")
                elif isinstance(rules, str):
                    # 如果规则是一个字符串，将其作为一个完整的规则
                    rule_lines = rules.strip().splitlines()
                    for i, line in enumerate(rule_lines):
                        if i == 0:  # 第一行使用 - 标记
                            lines.append(f"{indent}        - {line}")
                        else:  # 后续行保持相同缩进
                            lines.append(f"{indent}          {line}")

    return '\n'.join(lines)

def get_current_task(work: dict) -> Tuple[Optional[dict], Optional[int]]:
    """获取当前任务

    Args:
        work: 工作数据

    Returns:
        (当前任务, 当前任务索引)
    """
    if not work or 'tasks' not in work:
        return None, None

    tasks = work['tasks']
    if not tasks:
        return None, None

    # 获取当前主任务索引
    current_task_index = None
    for i, task in enumerate(tasks):
        if task.get('status') == 'done':
            continue

        # 如果任务有未完成的子任务，它就是当前任务
        if task.get('subtasks'):
            has_unfinished = False
            for subtask in task['subtasks']:
                if subtask.get('status') != 'done':
                    has_unfinished = True
                    break
            if has_unfinished:
                current_task_index = i
                break
        else:
            # 如果前面没有找到有未完成子任务的任务，那么第一个未完成的任务就是当前任务
            current_task_index = i
            break

    if current_task_index is None:
        return None, None

    current_task = tasks[current_task_index]

    # 检查是否有子任务
    if current_task.get('subtasks'):
        subtasks = current_task['subtasks']
        # 只有当current_subtask_index已经设置时，才返回对应的子任务
        subtask_index = current_task.get('current_subtask_index')
        if subtask_index is not None and subtask_index < len(subtasks):
            return subtasks[subtask_index], current_task_index

    return current_task, current_task_index

def get_current_work(workspace: str) -> Optional[dict]:
    """获取当前进行中的工作"""
    user_id, user_name = get_user_info()
    workspace_path = Path(workspace)
    for file in workspace_path.rglob('work.yml'):  # 递归查找所有子目录中的 work.yml
        data = load_yaml(str(file))
        if data.get('status') == 'in_progress' and data.get('user_name') == user_name:
            data['work_path'] = str(file)  # 记录文件路径
            return data
    return None

def format_work_output(work: Optional[dict]) -> str:
    """格式化工作输出"""
    if not work:
        return "- 当前工作: \n- 任务列表:\n    - 无"

    current_task, current_task_index = get_current_task(work)



    lines = [
        f"- work_path: {os.path.dirname(work.get('work_path', ''))}",
        f"- 当前工作: {work.get('name', '')}",
    ]

    if work.get('description'):
        lines.append(f"- 工作简介: {work['description']}")

    if work.get('workspace'):
        lines.append(f"- 工作空间: {work['workspace']}")

    if work.get('rules'):
        lines.append("- 工作规则:")
        for rule in work['rules']:
            lines.append(f"    - {rule}")

    lines.append("- 任务列表:")
    if work.get('tasks'):
        task_lines = format_tasks(work['tasks'], current_task_index)
        lines.append(task_lines)
    else:
        lines.append("    - 无")

    if current_task:
        if current_task.get('worker'):
            lines.append(f"\n**请先切换到角色{current_task['worker']}再继续工作**")
        lines.append(f"**AI必须说明要遵循的任务规则,如果有前置文档则必须阅读后再执行任务**")

    # 将prompt移到最后，只有没有subtasks时才出现
    if current_task and current_task.get('prompt') and not current_task.get('subtasks'):
        lines.append(f"\n当前工作命令: {current_task['prompt']}")
    if current_task and current_task.get('subtasks') and not current_task.get('current_subtask_index'):
        lines.append(f"\n 忽略历史信息，请执行 /work next 指令开始第一个子任务")
    # userconfirm
    if current_task and current_task.get('userconfirm'):
        lines.append(f"\n请注意: 当前工作完成后需要用户确认验收")

    return '\n'.join(lines)

def handle_next(work: dict) -> str:
    """处理next命令

    Args:
        work: 工作数据

    Returns:
        提示信息
    """
    if not work or 'tasks' not in work:
        return "没有任务"

    tasks = work['tasks']
    if not tasks:
        return "没有任务"

    # 获取当前主任务
    current_task_index = None
    for i, task in enumerate(tasks):
        if task.get('status') == 'done':
            continue

        # 如果任务有未完成的子任务，它就是当前任务
        if task.get('subtasks'):
            has_unfinished = False
            for subtask in task['subtasks']:
                if subtask.get('status') != 'done':
                    has_unfinished = True
                    break
            if has_unfinished:
                current_task_index = i
                break
        else:
            # 如果前面没有找到有未完成子任务的任务，那么第一个未完成的任务就是当前任务
            current_task_index = i
            break

    if current_task_index is None:
        return "所有任务已完成"

    current_task = tasks[current_task_index]

    # 处理子任务
    if current_task.get('subtasks'):
        current_subtask_index = current_task.get('current_subtask_index')

        # 如果当前没有子任务索引，说明是第一次进入该任务
        if current_subtask_index is None:
            # 找到第一个未完成的子任务
            for i, subtask in enumerate(current_task['subtasks']):
                if subtask.get('status') != 'done':
                    # 标记之前的子任务为已完成
                    for j in range(i):
                        current_task['subtasks'][j]['status'] = 'done'
                    # 设置当前子任务索引
                    current_task['current_subtask_index'] = i
                    return "进入子任务"
            # 如果所有子任务都完成了，标记当前任务为完成
            current_task['status'] = 'done'
            # 寻找下一个未完成的主任务
            for i in range(current_task_index + 1, len(tasks)):
                if tasks[i].get('status') != 'done':
                    return "进入下一个任务"
            return "所有任务已完成"

        # 如果已经有子任务索引，说明正在执行子任务
        elif current_subtask_index < len(current_task['subtasks']) - 1:
            # 标记当前子任务为已完成
            current_task['subtasks'][current_subtask_index]['status'] = 'done'
            # 进入下一个子任务
            current_task['current_subtask_index'] = current_subtask_index + 1
            return "进入下一个子任务"
        else:
            # 已经是最后一个子任务，标记当前子任务和主任务为已完成
            current_task['subtasks'][current_subtask_index]['status'] = 'done'
            current_task['status'] = 'done'
            current_task['current_subtask_index'] = None
            # 寻找下一个未完成的主任务
            for i in range(current_task_index + 1, len(tasks)):
                if tasks[i].get('status') != 'done':
                    return "进入下一个任务"
            return "所有任务已完成"

    # 如果没有子任务
    current_task['status'] = 'done'
    # 寻找下一个未完成的主任务
    for i in range(current_task_index + 1, len(tasks)):
        if tasks[i].get('status') != 'done':
            return "进入下一个任务"
    return "所有任务已完成"

def handle_back(work: dict) -> str:
    """处理back命令

    Args:
        work: 工作数据

    Returns:
        提示信息
    """
    if not work or 'tasks' not in work:
        return "没有任务"

    tasks = work['tasks']
    if not tasks:
        return "没有任务"

    # 获取当前主任务
    current_task_index = None
    for i, task in enumerate(tasks):
        if task.get('status') == 'done':
            continue

        # 如果任务有未完成的子任务，它就是当前任务
        if task.get('subtasks'):
            has_unfinished = False
            for subtask in task['subtasks']:
                if subtask.get('status') != 'done':
                    has_unfinished = True
                    break
            if has_unfinished:
                current_task_index = i
                break
        else:
            # 如果前面没有找到有未完成子任务的任务，那么第一个未完成的任务就是当前任务
            current_task_index = i
            break

    if current_task_index is None:
        return "没有进行中的任务"

    current_task = tasks[current_task_index]

    # 处理子任务
    if current_task.get('subtasks'):
        current_subtask_index = current_task.get('current_subtask_index')
        if current_subtask_index is not None and current_subtask_index > 0:
            # 恢复当前子任务的未完成状态
            current_task['subtasks'][current_subtask_index]['status'] = None
            # 返回上一个子任务
            current_task['current_subtask_index'] = current_subtask_index - 1
            # 恢复上一个子任务的未完成状态
            current_task['subtasks'][current_subtask_index - 1]['status'] = None
            return "返回上一个子任务"
        elif current_subtask_index is not None and current_subtask_index == 0:
            # 恢复当前子任务的未完成状态
            current_task['subtasks'][current_subtask_index]['status'] = None
            # 恢复所有子任务的状态
            for subtask in current_task['subtasks']:
                subtask['status'] = None
            current_task['current_subtask_index'] = None

    # 如果没有子任务，或者已经是第一个子任务，尝试返回上一个主任务
    if current_task_index > 0:
        # 恢复当前任务的状态
        current_task['status'] = None
        if current_task.get('subtasks'):
            for subtask in current_task['subtasks']:
                subtask['status'] = None
            current_task['current_subtask_index'] = None

        # 恢复上一个任务的状态
        prev_task = tasks[current_task_index - 1]
        prev_task['status'] = 'in_progress'  # 设置为进行中

        # 如果上一个任务有子任务，设置最后一个未完成的子任务为当前任务
        if prev_task.get('subtasks'):
            # 找到最后一个未完成的子任务
            last_unfinished_index = None
            for i, subtask in enumerate(prev_task['subtasks']):
                if subtask.get('status') != 'done':
                    last_unfinished_index = i

            # 如果找到了未完成的子任务，设置为当前任务
            if last_unfinished_index is not None:
                prev_task['current_subtask_index'] = last_unfinished_index
            # 如果没有未完成的子任务，设置最后一个子任务为当前任务
            else:
                prev_task['current_subtask_index'] = len(prev_task['subtasks']) - 1
                prev_task['subtasks'][-1]['status'] = None  # 将最后一个子任务设为未完成

        # 标记更早的任务为已完成
        for i in range(current_task_index - 1):
            tasks[i]['status'] = 'done'
            if tasks[i].get('subtasks'):
                for subtask in tasks[i]['subtasks']:
                    subtask['status'] = 'done'

        return "返回上一个任务"
    else:
        return "已经是第一个任务"

def update_rules_file(content: str):
    """更新规则文件中的current_work部分"""
    if not os.path.exists(rules_path):
        with open(rules_path, 'w', encoding='utf-8') as f:
            f.write(f"<global_work>\n{content}\n</global_work>")
        return

    with open(rules_path, 'r', encoding='utf-8') as f:
        rules_content = f.read()

    # 清理所有的current_work标记和前缀>符号
    while True:
        start = rules_content.find('<global_work>')
        if start == -1:
            break
        end = rules_content.find('</global_work>', start)
        if end == -1:
            break
        rules_content = rules_content[:start] + rules_content[end + 14:]

    # 清理开头的>符号和空行
    lines = rules_content.splitlines()
    filtered_lines = [line for line in lines if line.strip() != '>']
    rules_content = "\n".join(filtered_lines).strip()

    # 清理开头的>符号和空行
    # rules_content = rules_content.lstrip('>\n').strip()

    # 添加新的current_work内容
    if '<memory>' in rules_content:
        # 在memory标签前插入current_work
        memory_pos = rules_content.find('<memory>')
        rules_content = (rules_content[:memory_pos].rstrip() + '\n\n' +
                        f"<global_work>\n{content}\n</global_work>\n\n" +
                        rules_content[memory_pos:])
    else:
        # 在文件末尾添加current_work
        rules_content = f"{rules_content.rstrip()}\n\n<global_work>\n{content}\n</global_work>\n"

    with open(rules_path, 'w', encoding='utf-8') as f:
        f.write(rules_content)

# 工作流目录路径
LOCAL_WORK_DIR = os.path.join(workspace, 'workflows', 'work')
AGENT_WORK_DIR = os.path.join(project_root, '.agent', 'workflows', 'work')

def list_workflows():
    """列出所有可用的工作流模板"""

    # 导入tabulate库
    try:
        from tabulate import tabulate  # 需要安装: pip install tabulate
    except ImportError:
        print("请先安装tabulate库: pip install tabulate")
        print("或者运行: pip install -r .agent/requirements.txt")
        return

    workflows = []

    # 扫描本地工作流目录
    if os.path.exists(LOCAL_WORK_DIR):
        for file in os.listdir(LOCAL_WORK_DIR):
            if file.endswith('.yml'):
                path = os.path.join(LOCAL_WORK_DIR, file)
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        workflow = yaml.safe_load(f)
                        workflows.append({
                            'name': os.path.splitext(file)[0],
                            'title': workflow.get('name', ''),
                            'path': path
                        })
                except Exception as e:
                    print(f"Error reading {path}: {e}")

    # 扫描agent工作流目录
    if os.path.exists(AGENT_WORK_DIR):
        for file in os.listdir(AGENT_WORK_DIR):
            if file.endswith('.yml'):
                path = os.path.join(AGENT_WORK_DIR, file)
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        workflow = yaml.safe_load(f)
                        workflows.append({
                            'name': os.path.splitext(file)[0],
                            'title': workflow.get('name', ''),
                            'path': path
                        })
                except Exception as e:
                    print(f"Error reading {path}: {e}")

    # 按名称排序
    workflows.sort(key=lambda x: x['name'])

    # 准备表格数据
    headers = ["工作流名称", "标题", "路径"]
    table_data = []

    for workflow in workflows:
        path = workflow['path']
        # 提取路径的最后一部分，通常是文件名
        path = os.path.basename(path)

        table_data.append([
            workflow['name'],
            workflow['title'],
            path
        ])

    # 使用tabulate生成表格，使用grid样式
    try:
        # 使用grid样式，每行都有横线分隔
        table = tabulate(table_data, headers=headers, tablefmt="grid")
    except:
        # 如果不支持，回退到fancy_grid
        table = tabulate(table_data, headers=headers, tablefmt="fancy_grid")

    # 输出工作流列表
    print("\n可用的工作流模板:")
    print(table)

    # 添加用法示例
    print("\n用法示例:")
    print("  /work use api_first    # 使用api_first工作流")
    print("  /work use create_workflow    # 使用create_workflow工作流")

def main():
    """主函数"""
    # 在执行任何命令前先检查用户信息
    user_id, user_name = get_user_info()
    if not user_id or not user_name:
        print(f"错误: 未找到用户信息，请确保.env文件存在并包含USER_ID和USER_NAME环境变量")
        return

    if len(sys.argv) < 2:
        print("示例: python work.py <command> [args]")
        print("可用命令: now, next, back, update, use, flows, list_user, on")
        return

    # 在执行任何命令前先执行清屏命令
    os.system('cls' if os.name == 'nt' else 'clear')

    workspace_path = Path(workspace)
    workspace_path.mkdir(parents=True, exist_ok=True)

    cmd = sys.argv[1]

    if cmd == 'now':
        work = get_current_work(workspace)
        if not work:
            output = "当前没有进行中的工作，请询问用户需要做什么"
        else:
            output = format_work_output(work)
        print(output)
        update_rules_file(output)

    elif cmd == 'use':
        if len(sys.argv) < 3:
            print("示例: python work.py use <workflow_name>")
            return

        workflow_name = sys.argv[2]

        # 读取用户信息
        user_id, user_name = get_user_info()
        if not user_id or not user_name:
            output = "错误: 未找到用户信息，请确保_agent-local/.me文件存在并包含USER_ID和USER_NAME"
            print(output)
            update_rules_file(output)
            return

        # 查找工作流模板
        template_path = find_workflow_template(workflow_name)
        if not template_path:
            output = f"错误: 未找到工作流规则文件，已尝试以下路径:\n - {os.path.join(LOCAL_WORK_DIR, f'{workflow_name}.yml')}\n - {os.path.join(AGENT_WORK_DIR, f'{workflow_name}.yml')}"
            print(output)
            update_rules_file(output)
            return

        # 读取并验证YAML
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                work = yaml.safe_load(f)
                print(f"Loaded YAML from file: {template_path}")
        except Exception as e:
            print(f"Error parsing YAML: {e}")
            return

        if not validate_work_yaml(work):
            print("Invalid YAML format")
            return

        # 将当前用户的所有进行中工作流改为pending
        update_user_workflows_status(user_name)

        # 更新工作信息
        timestamp = datetime.now().strftime('%m%d_%H_%M')
        ordered_work = {}
        # 使用用户ID和工作流名称作为工作流ID，而不是UUID
        ordered_work['id'] = f"{user_id}_{workflow_name}_{timestamp}"
        ordered_work['name'] = work.get('name', workflow_name)
        ordered_work['description'] = work.get('description', '')
        ordered_work['rules'] = work.get('rules', [])
        ordered_work['status'] = 'in_progress'
        ordered_work['user_id'] = user_id
        ordered_work['user_name'] = user_name
        ordered_work['tasks'] = work.get('tasks', [])

        # 处理任务
        for task in ordered_work['tasks']:
            if not task.get('subtasks') and task.get('steps') and task.get('objects'):
                task['subtasks'] = generate_subtasks(task)
            task['status'] = task.get('status', 'todo')

        # 创建工作目录并保存work.yml
        work_dir = os.path.join(WORKSPACE_DIR, f"{user_id}_{workflow_name}_{timestamp}")
        os.makedirs(work_dir, exist_ok=True)
        work_file = os.path.join(work_dir, 'work.yml')
        ordered_work['work_path'] = work_file  # 记录文件路径
        save_yaml(work_file, ordered_work)
        print(f"Saved work file: {work_file}")

        # 上报数据
        report_action({
            'action': '工作流:启动',
            'workflow_name':  ordered_work['name'] + ':' + ordered_work['id']
        })

        # 输出当前状态
        output = format_work_output(ordered_work)
        print(output)
        update_rules_file(output)

    elif cmd == 'next':
        work = get_current_work(workspace)
        if not work:
            print("No work in progress")
            return

        result = handle_next(work)
        # 获取当前工作目录
        work_dir = os.path.dirname(work['work_path'])  # 使用保存在work中的文件路径
        save_yaml(os.path.join(work_dir, 'work.yml'), work)
        print(result)

        # 获取当前任务信息
        workflow_name = work.get('name', '')
        current_task, _ = get_current_task(work)
        task_name = current_task.get('name', '') if current_task else ''

        # 上报数据
        if workflow_name and task_name:
            report_action({
                'action': '工作流:下一步',
                'workflow_name': workflow_name + ':' + work.get('id', ''),
                'task_name': task_name
            })

        output = format_work_output(work)
        print(output)
        update_rules_file(output)

    elif cmd == 'back':
        work = get_current_work(workspace)
        if not work:
            print("No work in progress")
            return

        result = handle_back(work)
        # 获取当前工作目录
        work_dir = os.path.dirname(work['work_path'])  # 使用保存在work中的文件路径
        save_yaml(os.path.join(work_dir, 'work.yml'), work)
        print(result)
        output = format_work_output(work)
        print(output)
        update_rules_file(output)

    elif cmd == 'update':
        if len(sys.argv) < 3:
            print("示例: python work.py update <json_data>")
            return

        work = get_current_work(workspace)
        if not work:
            print("没有进行中的工作")
            return

        current_task, current_task_index = get_current_task(work)
        if not current_task:
            print("没有当前任务，无法更新")
            return

        # 解析更新数据
        try:
            update_data = json.loads(sys.argv[2])
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON data: {e}")
            return

        # 更新任务字段
        for field in ['objects', 'steps', 'subtasks', 'prompt']:  # 添加prompt字段
            if field in update_data:
                current_task[field] = update_data[field]

        # 更新template字段
        if 'template' in update_data:
            if 'output' in current_task:
                for doc in current_task['output']:
                    if isinstance(doc, dict) and 'template' in doc:
                        doc['template'] = update_data['template']
                        break

        # 如果有steps和objects但没有subtasks，生成子任务
        if not current_task.get('subtasks') and current_task.get('steps') and current_task.get('objects'):
            current_task['subtasks'] = generate_subtasks(current_task)

        # 获取当前工作目录
        work_dir = os.path.dirname(work['work_path'])  # 使用保存在work中的文件路径
        save_yaml(os.path.join(work_dir, 'work.yml'), work)

        # 输出新状态
        output = format_work_output(work)
        print(output)
        update_rules_file(output)

    elif cmd == 'clear':
        # 只清空current_work标签内容，不删除工作文件
        update_rules_file("")
        print("当前工作记忆清空了")

    elif cmd == 'empty':
        # 清空工作空间
        if os.path.exists(WORKSPACE_DIR):
            shutil.rmtree(WORKSPACE_DIR)
        os.makedirs(WORKSPACE_DIR, exist_ok=True)
        output = "- 当前工作: \n- 任务列表:\n    - 无"
        update_rules_file(output)
        print("工作空间文件已清空")

    elif cmd == 'flows':
        list_workflows()

    elif cmd == 'list':
        user_id, user_name = get_user_info()
        if not user_name:
            print("未找到用户信息")
            return

        # 导入tabulate库
        try:
            from tabulate import tabulate
        except ImportError:
            print("请先安装tabulate库: pip install tabulate")
            return

        workflows = list_user_workflows(user_name)

        # 状态映射表：英文到中文
        status_map = {
            'in_progress': '进行中',
            'pending': '暂停',
            'done': '已完成'
        }

        # 过滤出进行中和暂停的工作
        active_workflows = [w for w in workflows if w['status'] in ['in_progress', 'pending']]

        if not active_workflows:
            print(f"\n{user_name}当前没有进行中或暂停的工作")
            return

        # 使用tabulate库格式化表格
        # 准备表格数据
        headers = ["工作ID", "工作名称", "状态"]
        table_data = []

        for workflow in active_workflows:
            status = status_map.get(workflow['status'], workflow['status'])
            table_data.append([workflow['id'], workflow['name'], status])

        # 使用tabulate生成表格，使用grid样式
        try:
            # 使用grid样式，每行都有横线分隔
            table = tabulate(table_data, headers=headers, tablefmt="grid")
        except:
            # 如果不支持，回退到fancy_grid
            table = tabulate(table_data, headers=headers, tablefmt="fancy_grid")

        print(f"\n{user_name}的工作列表:")
        print(table)

        # 添加用法示例
        print("\n用法示例:")
        print("  /work on <工作ID>    # 切换到指定工作")
        print("  /work now            # 查看当前工作详情")
        print("  /work next           # 进入下一个任务")

    elif cmd == 'on':
        if len(sys.argv) < 3:
            print("示例: python work.py on <workflow_id>")
            return

        workflow_id = sys.argv[2]
        user_id, user_name = get_user_info()
        if not user_name:
            print("未找到用户信息")
            return

        success, work = switch_workflow(workflow_id, user_name)
        if success:
            output = format_work_output(work)
            print(output)
            update_rules_file(output)

            # 上报数据
            report_action({
                'action': '工作流:切换',
                'workflow_name': work.get('name', '') + ':' + workflow_id
            })

        else:
            print(f"未找到ID为{workflow_id}的工作流")
    else:
        print(f"未知命令: {cmd}")
        print("可用命令: now, next, back, update, use, flows, list_user, on")

if __name__ == '__main__':
    main()

# 测试点知识库管理指南

## 知识库概述
测试点知识库用于存储和管理测试点文档，支持TXT和MD格式的文档导入，并提供全文检索功能。这个知识库特别适合存储和管理测试用例、测试点、需求分析等文档，方便团队成员快速查找和复用测试资源。

## 知识库结构
- 知识库根目录: `_agent-local/knowledge/test_points`
- 文档存储目录: `_agent-local/knowledge/test_points/documents`
- 索引目录: `_agent-local/knowledge/test_points/index`
- 元数据文件: `_agent-local/knowledge/test_points/metadata.json`

## 知识库管理

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 导入单个文档
doc_id = kb.add_document("path/to/document.txt", category="功能测试")

# 批量导入目录下的所有文档
import os
from pathlib import Path

docs_dir = Path("path/to/documents")
for file in docs_dir.glob("*.txt"): 
    kb.add_document(str(file), category="功能测试")
```

### 更新文档
```python
kb.update_document("doc_id", "path/to/new_document.txt")
```

### 删除文档
```python
kb.delete_document("doc_id")
```

### 备份知识库
```python
import shutil
from datetime import datetime

# 备份整个知识库
backup_dir = f"knowledge_base_backup_{datetime.now().strftime('%Y%m%d%H%M%S')}"
shutil.copytree("_agent-local/knowledge/test_points", backup_dir)
```

### 恢复知识库
```python
import shutil

# 恢复知识库
shutil.rmtree("_agent-local/knowledge/test_points")
shutil.copytree("path/to/backup", "_agent-local/knowledge/test_points")
```

### 知识库合并
```python
# 将另一个知识库的内容合并到当前知识库
other_kb = KnowledgeBaseManager(base_dir="path/to/other/knowledge_base")
other_docs = other_kb.list_documents()

for doc_meta in other_docs:
    doc = other_kb.get_document(doc_meta["id"])
    # 将文档内容写入临时文件
    with open("temp_doc.txt", "w", encoding="utf-8") as f:
        f.write(doc["content"])
    # 导入到当前知识库
    kb.add_document("temp_doc.txt", category=doc_meta.get("category", "默认分类"))
```

## 定期维护计划
为了保持知识库的健康状态，建议按照以下计划进行定期维护：

1. **每日维护**:
   - 检查新导入文档的质量
   - 备份当天新增的重要文档

2. **每周维护**:
   - 完整备份知识库
   - 检查并修复索引问题

3. **每月维护**:
   - 清理临时文档
   - 更新文档分类
   - 检查重复文档

4. **每季度维护**:
   - 全面评估知识库内容
   - 归档过时文档
   - 优化知识库结构

通过遵循这些最佳实践和定期维护计划，您可以确保测试点知识库始终处于最佳状态，为团队提供高效的测试资源管理和检索服务。

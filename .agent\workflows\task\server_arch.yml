name: "技术架构分析"
description: "分析项目技术架构，包括技术栈、系统架构、模块划分等"

# 任务目标列表
todos: []

# 前置步骤
pre_steps:
  - title: "确认技术架构分析范围"
    rule:
      - "分析项目文档和代码中的技术架构相关内容"
      - "重点分析技术栈、系统架构、模块划分等技术内容"
      - "必须读取 template/技术架构.md.tpl，这是输出的参考模板"
      - "分析项目的主语言和构建系统"
      - "确认分析范围后进入下一步 /task next"

# 任务步骤
steps:
  - title: "分析项目依赖和技术栈"
    rule:
      - "使用 cat 命令查看项目依赖，比如maven项目的 pom.xml 文件"
      - "分析所有依赖的版本和用途"
      - "按类别整理技术栈"
      - "分析ORM框架和配置"
      - "整理信息并等待用户确认"
    output:
      - doc: tech_stack.md
        template: |
          # 技术栈分析

          ## 基础技术栈
          - 开发语言：
          - 框架：
          - 微服务框架：
          - 服务发现：
          - 配置中心：
          - 服务熔断：
          - Web服务器：
          - 项目构建：

          ## 数据存储
          - 数据库：
          - 缓存：
          - 对象存储：

          ## ORM框架
          - 框架类型：
          - 版本：
          - 配置方式：
          - Mapper文件位置：
          - 主要特性：
          - 代码生成工具：

          ## 中间件
          - 消息队列：
          - 任务队列：
          - 缓存：
          - 服务调用：

          ## DevOps
          - CI/CD：
          - 监控：
          - 容器化：

  - title: "分析系统架构"
    rule:
      - "使用 ls -R 分析项目目录结构"
      - "分析每个模块的职责和交互"
      - "整理分层设计和架构模式"
      - "分析ORM相关的目录结构和配置"
      - "整理信息并等待用户确认"
    output:
      - doc: system_arch.md
        template: |
          # 系统架构分析

          ## 分层设计
          - 整体架构：
          - 各层职责：

          ## 目录结构
          ```
          # 目录树结构
          ```

          ## 模块划分
          - 核心业务模块：
          - 基础设施模块：
          - 存储模块：
          - 消息模块：
          - 监控模块：

          ## ORM架构
          - 数据访问层设计：
          - Mapper文件组织：
          - 实体类设计：
          - 查询方式：
          - 事务管理：

          ## 第三方依赖
          - 云服务：
          - 认证服务：
          - 数据库：
          - 工具库：

  - title: "生成技术架构文档"
    rule:
      - "整合前两个任务的分析结果"
      - "确保文档结构清晰"
      - "补充必要的说明和注释"
      - "等待用户确认文档内容"
    input:
      - doc: tech_stack.md
      - doc: system_arch.md
    output:
      - doc: _agent-local/guidelines/技术架构.md
        template: |
          # xxx服务技术架构文档

          ## 1. 技术栈

          ### 1.1 基础技术栈
          {{tech_stack.base}}

          ### 1.2 数据存储
          {{tech_stack.storage}}

          ### 1.3 ORM框架
          {{tech_stack.orm}}

          ### 1.4 中间件
          {{tech_stack.middleware}}

          ### 1.5 DevOps
          {{tech_stack.devops}}

          ## 2. 系统架构

          ### 2.1 分层设计
          {{system_arch.layers}}

          ### 2.2 目录结构
          {{system_arch.directory}}

          ### 2.3 模块划分
          {{system_arch.modules}}

          ### 2.4 ORM架构
          {{system_arch.orm}}

          ### 2.5 第三方依赖
          {{system_arch.dependencies}}

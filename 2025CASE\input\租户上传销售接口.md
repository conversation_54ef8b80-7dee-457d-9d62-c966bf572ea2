文档分析（更新版）
基本信息
文档名称：租户上传销售接口
接口用途：给第三方公司使用，用于上传销售数据
当前版本：V1.2 (2020-04-03)
接口方式：Restful接口(HTTP POST)
数据格式：JSON，编码为UTF-8
接口详情
接口名称：salesTransLiteV61（正常销售）和salestransreplacelitev61（覆盖历史销售）
测试环境URL：http://kc.lvgemgroup.com.cn:8185/posservice/rest/salestransaction/salestranslitev61
测试环境参数
apikey：LVGEMHSL
店铺号storecode：09210029
收银机号tillid：00
收银员编号cashier：09210029
商品编号：0921002901
付款方式编码：CH
主要数据结构
文档详细描述了以下数据结构：

请求参数（apiKey, signature, docKey等）
TransHeader（交易头信息）
SalesTotalLite（销售总计信息）
SalesItemLite（销售商品明细）
SalesTenderLite（支付方式信息）
SalesMemo（原销售单信息，用于退货）
特点和更新
V1.2版本增加了交易笔数字段
增加了覆盖历史销售的功能（同一日期同一店铺号，同一收银机号上传销售时历史销售会清零）
提供了完整的请求示例和响应示例
包含详细的错误代码列表
测试要点
基于文档内容和测试环境信息，我整理出以下测试要点：

1. 基础功能测试
正常销售上传测试
使用提供的测试参数构建完整请求
验证成功响应（errorCode: 0）
验证销售数据是否正确保存
覆盖历史销售测试
先上传一笔销售数据
使用相同的日期、店铺号、收银机号上传新的销售数据
验证历史销售是否被清零，以最后一笔为准
退货功能测试
上传一笔正常销售
使用原销售单信息进行退货（netQty和netAmount为负数）
验证退货是否成功处理
2. 参数验证测试
必填参数验证
分别缺少各个必填参数（apiKey, docKey, transHeader等）
验证是否返回对应的错误码（-1至-11）
参数格式验证
测试不同格式的日期（txDate, ledgerDatetime）
测试不同格式的数值（netQty, netAmount等）
验证是否能正确处理或返回适当错误
唯一性验证
使用已存在的docKey上传
验证是否返回错误（销售单号已经存在）
3. 业务规则测试
金额一致性测试
测试salesItem中的netAmount总和与salesTotal中的netAmount是否一致
测试salesTender中的payAmount总和与salesTotal中的netAmount是否一致
验证不一致时是否返回错误（-18, -19）
商品编号测试
使用有效的商品编号（0921002901）
使用无效的商品编号
验证是否返回适当的响应（-12）
店铺和收银机验证
使用有效的店铺号（09210029）和收银机号（00）
使用无效的店铺号和收银机号
验证是否返回适当的响应（-16, -17）
4. 特殊场景测试
并发上传测试
同时上传多笔相同店铺的销售数据
验证是否有销售单据在处理队列中的错误（-67）
多种付款方式组合测试
使用多种付款方式（包括CH）组合支付
验证是否能正确处理
大数据量测试
上传包含多个商品项的销售数据
验证系统处理能力
5. 边界条件测试
验证是否返回适当的响应（-21）
金额边界测试
测试0金额和退货场景
验证系统处理能力
6. 错误恢复测试
网络中断恢复测试
模拟网络中断后重新上传
验证是否能正确处理或提示已存在
服务器错误恢复测试
模拟服务器返回5xx错误后重试
验证是否能正确处理

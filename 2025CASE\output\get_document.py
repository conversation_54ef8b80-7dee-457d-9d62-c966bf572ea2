"""
获取知识库中的文档内容
"""

import os
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

try:
    from knowledge_base import KnowledgeBaseManager
except ImportError:
    print("错误：无法导入knowledge_base模块，请确保该模块已正确安装")
    sys.exit(1)

def main():
    """获取知识库中的文档内容"""
    # 创建知识库管理器
    kb = KnowledgeBaseManager()
    
    # 列出所有文档
    print("知识库中的所有文档:")
    all_docs = kb.list_documents()
    for i, doc in enumerate(all_docs, 1):
        print(f"{i}. {doc.get('id', '未知ID')}: {doc.get('title', '未知标题')}")
    
    # 获取满减活动文档的内容
    doc_id = "满减活动_营销中心勋章满减POS应用_20250421120308"
    doc = kb.get_document(doc_id)
    if doc:
        print(f"\n文档内容 ({doc.get('title', '未知标题')}):")
        print(doc["content"])
    else:
        print(f"\n未找到ID为 '{doc_id}' 的文档")

if __name__ == "__main__":
    main()

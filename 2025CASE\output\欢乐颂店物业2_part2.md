#### TC-006: 优惠+微信支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好优惠+微信支付的账单数据

- **测试步骤**：
  1. 应用优惠并使用微信方式完成剩余支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据（优惠金额固定为0，账单金额直接传实收）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="WP"，discountAmt=0，value为实际收款金额）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 微信(WP)
  - 订单原始金额: 100
  - 优惠金额: 20
  - 实收金额: 80
  - 上传的优惠金额: 0
  - 订单类型: SALE

#### TC-007: 优惠+支付宝支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好优惠+支付宝支付的账单数据

- **测试步骤**：
  1. 应用优惠并使用支付宝方式完成剩余支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据（优惠金额固定为0，账单金额直接传实收）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod="AP"，discountAmt=0，value为实际收款金额）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 支付宝(AP)
  - 订单原始金额: 100
  - 优惠金额: 20
  - 实收金额: 80
  - 上传的优惠金额: 0
  - 订单类型: SALE

#### TC-008: 挂账支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好挂账支付的账单数据

- **测试步骤**：
  1. 使用挂账方式完成支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（paymentMethod为挂账对应的代码）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 挂账
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-009: 现金+挂账支付账单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好现金+挂账支付的账单数据

- **测试步骤**：
  1. 使用现金+挂账组合方式完成支付
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（payList包含两条记录，分别为现金和挂账）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式1: 现金(CH)，金额: 50
  - 支付方式2: 挂账，金额: 50
  - 订单总金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-010: 0金额账单上传
- **优先级**：中
- **测试类型**：边界测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好0金额的账单数据

- **测试步骤**：
  1. 创建0金额账单
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（totalAmt=0）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 0
  - 优惠金额: 0
  - 实收金额: 0
  - 订单类型: SALE

### 退单场景测试用例

#### TC-011: 现金支付退单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统中存在已完成的现金支付账单
  - 系统已准备好现金支付的退单数据

- **测试步骤**：
  1. 对现金支付的账单进行退单操作
  2. 系统生成退单数据
  3. 系统按照物业接口要求格式化数据（type="ONLINEREFUND"）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成退单数据
  2. 数据格式符合物业接口要求（paymentMethod="CH"，type="ONLINEREFUND"，value为负数）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: -100
  - 优惠金额: 0
  - 实收金额: -100
  - 订单类型: ONLINEREFUND
  - 关联原订单号: 原订单ID

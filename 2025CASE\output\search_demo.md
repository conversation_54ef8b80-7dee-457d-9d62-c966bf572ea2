# 知识库检索演示

## 导入信息
- 文档ID: 物业接口_欢乐颂店物业接口验证_20250425172645
- 文档路径: 欢乐颂店物业接口验证.txt
- 导入时间: 2025-04-25T17:26:45.631341

## 检索示例
### 示例: 物业接口

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("物业接口")
```

**检索结果:**
未找到匹配的结果
### 示例: 账单付款

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("账单付款")
```

**检索结果:**
未找到匹配的结果
### 示例: 上传要求

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("上传要求")
```

**检索结果:**
- 文档: 欢乐颂店物业接口验证
  - 相关度: 1
  - 分类: 物业接口
  - 添加时间: 2025-04-25T17:26:45.626672
### 示例: 网络中断

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("网络中断")
```

**检索结果:**
未找到匹配的结果
### 示例: 测试场景

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("测试场景")
```

**检索结果:**
- 文档: 营销中心勋章满减POS应用
  - 相关度: 1
  - 分类: 满减活动
  - 添加时间: 2025-04-21T12:03:08.086083

## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")
```

### 检索文档
```python
# 关键词搜索
results = kb.search("物业接口")

# 按分类搜索
results = kb.search("账单付款", category="物业接口")

# 限制结果数量
results = kb.search("上传要求", limit=5)
```

### 查看文档
```python
doc = kb.get_document("doc_id")
print(doc["title"])
print(doc["content"])
```

### 列出所有文档
```python
all_docs = kb.list_documents()
for doc in all_docs:
    print(f"{doc['id']}: {doc['title']}")
```

### 删除文档
```python
kb.delete_document("doc_id")
```

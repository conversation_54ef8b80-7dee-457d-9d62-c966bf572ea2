import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import re

def create_test_case_excel(test_cases, output_file):
    # 创建一个新的工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "测试用例"
    
    # 设置标题行
    headers = ["用例编号", "用例名称", "前置条件", "测试步骤", "预期结果", "测试数据", "优先级"]
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col_num, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D3D3D3", end_color="D3D3D3", fill_type="solid")
        cell.alignment = Alignment(vertical='center', wrap_text=True)
    
    # 添加测试用例数据
    row_num = 2
    for test_case in test_cases:
        # 用例编号
        ws.cell(row=row_num, column=1, value=test_case["id"])
        
        # 用例名称
        ws.cell(row=row_num, column=2, value=test_case["name"])
        
        # 前置条件
        preconditions = "\n".join([f"- {cond}" for cond in test_case["preconditions"]])
        ws.cell(row=row_num, column=3, value=preconditions)
        
        # 测试步骤
        steps = "\n".join([f"{i+1}. {step}" for i, step in enumerate(test_case["steps"])])
        ws.cell(row=row_num, column=4, value=steps)
        
        # 预期结果
        expected_results = "\n".join([f"{i+1}. {result}" for i, result in enumerate(test_case["expected_results"])])
        ws.cell(row=row_num, column=5, value=expected_results)
        
        # 测试数据
        test_data = "\n".join([f"- {key}: {value}" for key, value in test_case["test_data"].items()])
        ws.cell(row=row_num, column=6, value=test_data)
        
        # 优先级
        ws.cell(row=row_num, column=7, value=test_case["priority"])
        
        # 设置单元格样式
        for col_num in range(1, 8):
            cell = ws.cell(row=row_num, column=col_num)
            cell.alignment = Alignment(vertical='center', wrap_text=True)
        
        row_num += 1
    
    # 自动调整列宽
    for col_num in range(1, 8):
        col_letter = get_column_letter(col_num)
        # 设置合适的列宽
        max_length = 0
        for row_num in range(1, ws.max_row + 1):
            cell_value = ws.cell(row=row_num, column=col_num).value
            if cell_value:
                max_length = max(max_length, len(str(cell_value).split('\n')[0]))
        
        adjusted_width = max(15, min(max_length + 2, 50))  # 最小15，最大50
        ws.column_dimensions[col_letter].width = adjusted_width
    
    # 保存Excel文件
    wb.save(output_file)
    return output_file

def parse_test_cases_from_md(md_file):
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取测试用例
    test_cases = []
    
    # 匹配测试用例块
    test_case_pattern = r'#### (TC-\d+): (.*?)\n(.*?)(?=#### TC-|\Z)'
    test_case_matches = re.finditer(test_case_pattern, content, re.DOTALL)
    
    for match in test_case_matches:
        test_case_id = match.group(1)
        test_case_name = match.group(2)
        test_case_content = match.group(3)
        
        # 提取优先级
        priority_match = re.search(r'\*\*优先级\*\*：(.*?)\n', test_case_content)
        priority = priority_match.group(1) if priority_match else ""
        
        # 提取前置条件
        preconditions = []
        preconditions_match = re.search(r'\*\*前置条件\*\*：\n(.*?)(?=\n\n- \*\*测试步骤\*\*)', test_case_content, re.DOTALL)
        if preconditions_match:
            preconditions_text = preconditions_match.group(1)
            preconditions = [line.strip().replace('- ', '') for line in preconditions_text.strip().split('\n') if line.strip()]
        
        # 提取测试步骤
        steps = []
        steps_match = re.search(r'\*\*测试步骤\*\*：\n(.*?)(?=\n\n- \*\*预期结果\*\*)', test_case_content, re.DOTALL)
        if steps_match:
            steps_text = steps_match.group(1)
            steps = [line.strip().replace(f"{i+1}. ", '') for i, line in enumerate(steps_text.strip().split('\n')) if line.strip()]
        
        # 提取预期结果
        expected_results = []
        expected_results_match = re.search(r'\*\*预期结果\*\*：\n(.*?)(?=\n\n- \*\*测试数据\*\*)', test_case_content, re.DOTALL)
        if expected_results_match:
            expected_results_text = expected_results_match.group(1)
            expected_results = [line.strip().replace(f"{i+1}. ", '') for i, line in enumerate(expected_results_text.strip().split('\n')) if line.strip()]
        
        # 提取测试数据
        test_data = {}
        test_data_match = re.search(r'\*\*测试数据\*\*：\n(.*?)(?=\n\n|$)', test_case_content, re.DOTALL)
        if test_data_match:
            test_data_text = test_data_match.group(1)
            for line in test_data_text.strip().split('\n'):
                if line.strip() and ': ' in line:
                    key, value = line.strip().replace('- ', '').split(': ', 1)
                    test_data[key] = value
        
        test_case = {
            "id": test_case_id,
            "name": test_case_name,
            "priority": priority,
            "preconditions": preconditions,
            "steps": steps,
            "expected_results": expected_results,
            "test_data": test_data
        }
        
        test_cases.append(test_case)
    
    return test_cases

# 主函数
def main():
    md_file = r"D:\0AI\TESTCASE\2025CASE\output\大米欢乐颂店物业用例.MD"
    output_file = r"D:\0AI\TESTCASE\2025CASE\output\大米欢乐颂店物业用例.xlsx"
    
    # 解析MD文件中的测试用例
    test_cases = parse_test_cases_from_md(md_file)
    
    # 创建Excel文件
    create_test_case_excel(test_cases, output_file)
    
    print(f"Excel文件已生成: {output_file}")

if __name__ == "__main__":
    main()

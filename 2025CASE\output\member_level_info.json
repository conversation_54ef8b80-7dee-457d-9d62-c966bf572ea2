{"search_results": {}, "knowledge_points": [{"title": "营销中心会员等级满折POS应用", "context": "# 营销中心会员等级满折POS应用\n\n## 文件信息", "line": "# 营销中心会员等级满折POS应用"}, {"title": "营销中心会员等级满折POS应用", "context": "\n## 文件信息\n- 文件名: 营销中心会员等级满折POS应用.txt\n- 文件类型: 文本文件\n- 导入时间: 1745301850.2061036", "line": "- 文件名: 营销中心会员等级满折POS应用.txt"}, {"title": "营销中心会员等级满折POS应用", "context": "## 测试点列表\n\n### 测试点 1: 一.功能概述：POS端不同等级会员命中满打折活动\n\n", "line": "### 测试点 1: 一.功能概述：POS端不同等级会员命中满打折活动"}, {"title": "营销中心会员等级满折POS应用", "context": "### 测试点 3: 1.营销中心已设置满打折活动，\n\n活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；", "line": "活动1：满100打90折，适用人群普通会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；", "line": "活动2:满100打85折，适用人群VIP会员;"}, {"title": "营销中心会员等级满折POS应用", "context": "活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；\n", "line": "活动3:满100打80折，适用人群钻石会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；\n\n### 测试点 4: 2.会员信息如下：", "line": "活动4:满300打75折，适用人群全部会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "活动4:满300打75折，适用人群全部会员；\n\n### 测试点 4: 2.会员信息如下：\n\n会员1为普通会员；", "line": "### 测试点 4: 2.会员信息如下："}, {"title": "营销中心会员等级满折POS应用", "context": "### 测试点 4: 2.会员信息如下：\n\n会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；", "line": "会员1为普通会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；\n", "line": "会员2为VIP会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；\n\n### 测试点 5: 三.测试场景及预期结果：", "line": "会员3为钻石会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 6: 1.已点餐，账单金额80元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 6: 1.已点餐，账单金额80元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 7: 2.已点餐，账单金额100元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 7: 2.已点餐，账单金额100元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 8: 3.已点餐，账单金额200元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 8: 3.已点餐，账单金额200元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 9: 4.已点餐，账单金额300元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 9: 4.已点餐，账单金额300元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "# 营销中心会员等级满折POS应用\n\n## 文件信息", "line": "# 营销中心会员等级满折POS应用"}, {"title": "营销中心会员等级满折POS应用", "context": "\n## 文件信息\n- 文件名: 营销中心会员等级满折POS应用.txt\n- 文件类型: 文本文件\n- 导入时间: 1745301850.2061036", "line": "- 文件名: 营销中心会员等级满折POS应用.txt"}, {"title": "营销中心会员等级满折POS应用", "context": "## 测试点列表\n\n### 测试点 1: 一.功能概述：POS端不同等级会员命中满打折活动\n\n", "line": "### 测试点 1: 一.功能概述：POS端不同等级会员命中满打折活动"}, {"title": "营销中心会员等级满折POS应用", "context": "### 测试点 3: 1.营销中心已设置满打折活动，\n\n活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；", "line": "活动1：满100打90折，适用人群普通会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；", "line": "活动2:满100打85折，适用人群VIP会员;"}, {"title": "营销中心会员等级满折POS应用", "context": "活动1：满100打90折，适用人群普通会员；\n活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；\n", "line": "活动3:满100打80折，适用人群钻石会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "活动2:满100打85折，适用人群VIP会员;\n活动3:满100打80折，适用人群钻石会员；\n活动4:满300打75折，适用人群全部会员；\n\n### 测试点 4: 2.会员信息如下：", "line": "活动4:满300打75折，适用人群全部会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "活动4:满300打75折，适用人群全部会员；\n\n### 测试点 4: 2.会员信息如下：\n\n会员1为普通会员；", "line": "### 测试点 4: 2.会员信息如下："}, {"title": "营销中心会员等级满折POS应用", "context": "### 测试点 4: 2.会员信息如下：\n\n会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；", "line": "会员1为普通会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；\n", "line": "会员2为VIP会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "会员1为普通会员；\n会员2为VIP会员；\n会员3为钻石会员；\n\n### 测试点 5: 三.测试场景及预期结果：", "line": "会员3为钻石会员；"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 6: 1.已点餐，账单金额80元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 6: 1.已点餐，账单金额80元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 7: 2.已点餐，账单金额100元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 7: 2.已点餐，账单金额100元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 8: 3.已点餐，账单金额200元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 8: 3.已点餐，账单金额200元，应用不同会员，活动命中情况"}, {"title": "营销中心会员等级满折POS应用", "context": "\n\n### 测试点 9: 4.已点餐，账单金额300元，应用不同会员，活动命中情况\n\n", "line": "### 测试点 9: 4.已点餐，账单金额300元，应用不同会员，活动命中情况"}]}
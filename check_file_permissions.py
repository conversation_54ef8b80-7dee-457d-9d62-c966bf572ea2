import os
import stat
from pathlib import Path

# 获取当前目录
current_dir = Path(__file__).parent.absolute()
print(f"当前目录: {current_dir}")

# 检查文件权限
env_paths = [
    current_dir / '_agent-local' / '.env',
    current_dir / '.agent' / '.env'
]

for env_path in env_paths:
    print(f"\n检查 {env_path}:")
    print(f"  文件存在: {env_path.exists()}")
    
    if env_path.exists():
        # 获取文件权限
        file_stat = os.stat(env_path)
        file_mode = file_stat.st_mode
        
        # 打印文件权限
        print(f"  文件权限: {oct(file_mode)}")
        print(f"  文件大小: {file_stat.st_size} 字节")
        print(f"  文件所有者: {file_stat.st_uid}")
        print(f"  文件组: {file_stat.st_gid}")
        
        # 检查文件是否可读
        print(f"  文件可读: {bool(file_mode & stat.S_IRUSR)}")
        print(f"  文件可写: {bool(file_mode & stat.S_IWUSR)}")
        print(f"  文件可执行: {bool(file_mode & stat.S_IXUSR)}")
        
        # 尝试读取文件内容
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"  文件内容: \n{content}")
        except Exception as e:
            print(f"  读取文件失败: {e}")

print("\n完成检查!")

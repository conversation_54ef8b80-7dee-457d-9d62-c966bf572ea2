# 欢乐颂店物业接口验证

## 文件信息
- 文件名: 欢乐颂店物业接口验证.txt
- 文件类型: 文本文件
- 导入时间: 1744960857.9378176

## 测试点列表

### 测试点 1: 功能描述：欢乐颂店物业接口验证


### 测试点 2: 前置条件：物业参数已正常配置，服务支持接口上传


### 测试点 3: 测试场景如下：


### 测试点 4: 1.账单付款场景

现金
现金+找零
优惠+现金
微信
支付宝
优惠+微信
优惠+支付宝
挂账
现金+挂账
0金额账单

### 测试点 5: 2.以上各种付款场景，数据上传物业


### 测试点 6: 3.以上各种付款场景退单，数据上传物业


### 测试点 7: 4.上传要求：

账单有优惠金额，上传时优惠金额固定0，账单金额直接传实收；
菜品信息传空数组即可，不需要传菜品详细信息；
正常账单类型SALE和退单类型ONLINEREFUND，
订单时间格式：yyyyMMddHHmmss

### 测试点 8: 5.补充说明:

网络中断或者物业不可用时，会自动重试3次，每次都失败将不再上传，需线下手动处理
不同的支付方式组合，要传付款列表明确各付款方式的金额值
不考虑物业测试环境和性能测试
系统支持传48小时内账单
数据上传记录写入数据库表中，同时有日志记录


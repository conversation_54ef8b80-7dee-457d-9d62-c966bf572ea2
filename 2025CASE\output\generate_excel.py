import pandas as pd
import os

# 创建测试用例数据
test_cases = [
    {
        "用例编号": "TC-001",
        "用例名称": "账单金额10元，会员1(标签A)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员1已绑定标签A；POS系统已完成点餐，账单金额为10元",
        "测试步骤": "1.在POS系统中输入会员1信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员1的标签A\n2.由于账单金额10元，低于所有满减活动的条件，不命中任何满减活动\n3.最终账单金额仍为10元，无优惠信息显示",
        "测试数据": "会员1信息；账单金额：10元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-002",
        "用例名称": "账单金额10元，会员2(标签B)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员2已绑定标签B；POS系统已完成点餐，账单金额为10元",
        "测试步骤": "1.在POS系统中输入会员2信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员2的标签B\n2.由于账单金额10元，低于所有满减活动的条件，不命中任何满减活动\n3.最终账单金额仍为10元，无优惠信息显示",
        "测试数据": "会员2信息；账单金额：10元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-003",
        "用例名称": "账单金额10元，会员3(标签AB)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员3已绑定标签A和标签B；POS系统已完成点餐，账单金额为10元",
        "测试步骤": "1.在POS系统中输入会员3信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员3的标签A和标签B\n2.由于账单金额10元，低于所有满减活动的条件，不命中任何满减活动\n3.最终账单金额仍为10元，无优惠信息显示",
        "测试数据": "会员3信息；账单金额：10元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-004",
        "用例名称": "账单金额10元，会员4(无标签)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员4没有绑定任何标签；POS系统已完成点餐，账单金额为10元",
        "测试步骤": "1.在POS系统中输入会员4信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统确认会员4没有任何标签\n2.由于账单金额10元，低于所有满减活动的条件，不命中任何满减活动\n3.最终账单金额仍为10元，无优惠信息显示",
        "测试数据": "会员4信息；账单金额：10元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-005",
        "用例名称": "账单金额20元，会员1(标签A)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员1已绑定标签A；POS系统已完成点餐，账单金额为20元",
        "测试步骤": "1.在POS系统中输入会员1信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员1的标签A\n2.由于账单金额20元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为19元(20-1)，显示优惠信息"满20减1"",
        "测试数据": "会员1信息；账单金额：20元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-006",
        "用例名称": "账单金额20元，会员2(标签B)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员2已绑定标签B；POS系统已完成点餐，账单金额为20元",
        "测试步骤": "1.在POS系统中输入会员2信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员2的标签B\n2.由于账单金额20元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为19元(20-1)，显示优惠信息"满20减1"",
        "测试数据": "会员2信息；账单金额：20元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-007",
        "用例名称": "账单金额20元，会员3(标签AB)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员3已绑定标签A和标签B；POS系统已完成点餐，账单金额为20元",
        "测试步骤": "1.在POS系统中输入会员3信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员3的标签A和标签B\n2.由于账单金额20元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为19元(20-1)，显示优惠信息"满20减1"",
        "测试数据": "会员3信息；账单金额：20元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-008",
        "用例名称": "账单金额20元，会员4(无标签)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员4没有绑定任何标签；POS系统已完成点餐，账单金额为20元",
        "测试步骤": "1.在POS系统中输入会员4信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统确认会员4没有任何标签\n2.由于账单金额20元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为19元(20-1)，显示优惠信息"满20减1"",
        "测试数据": "会员4信息；账单金额：20元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-009",
        "用例名称": "账单金额30元，会员1(标签A)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员1已绑定标签A；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员1信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员1的标签A\n2.账单金额30元同时满足活动1(满30减3，标签A)和活动3(满20减1，全部标签)的条件\n3.系统自动命中最大优惠的活动1，最终账单金额为27元(30-3)，显示优惠信息"满30减3"",
        "测试数据": "会员1信息；账单金额：30元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-010",
        "用例名称": "账单金额30元，会员2(标签B)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员2已绑定标签B；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员2信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员2的标签B\n2.由于账单金额30元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为29元(30-1)，显示优惠信息"满20减1"",
        "测试数据": "会员2信息；账单金额：30元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-011",
        "用例名称": "账单金额30元，会员3(标签AB)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员3已绑定标签A和标签B；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员3信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员3的标签A和标签B\n2.账单金额30元同时满足活动1(满30减3，标签A)和活动3(满20减1，全部标签)的条件\n3.系统自动命中最大优惠的活动1，最终账单金额为27元(30-3)，显示优惠信息"满30减3"",
        "测试数据": "会员3信息；账单金额：30元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-012",
        "用例名称": "账单金额30元，会员4(无标签)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员4没有绑定任何标签；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员4信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统确认会员4没有任何标签\n2.由于账单金额30元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为29元(30-1)，显示优惠信息"满20减1"",
        "测试数据": "会员4信息；账单金额：30元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-013",
        "用例名称": "账单金额50元，会员1(标签A)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员1已绑定标签A；POS系统已完成点餐，账单金额为50元",
        "测试步骤": "1.在POS系统中输入会员1信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员1的标签A\n2.账单金额50元同时满足活动1(满30减3，标签A)和活动3(满20减1，全部标签)的条件\n3.系统自动命中最大优惠的活动1，最终账单金额为47元(50-3)，显示优惠信息"满30减3"",
        "测试数据": "会员1信息；账单金额：50元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-014",
        "用例名称": "账单金额50元，会员2(标签B)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员2已绑定标签B；POS系统已完成点餐，账单金额为50元",
        "测试步骤": "1.在POS系统中输入会员2信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员2的标签B\n2.账单金额50元同时满足活动2(满50减5，标签B)和活动3(满20减1，全部标签)的条件\n3.系统自动命中最大优惠的活动2，最终账单金额为45元(50-5)，显示优惠信息"满50减5"",
        "测试数据": "会员2信息；账单金额：50元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-015",
        "用例名称": "账单金额50元，会员3(标签AB)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员3已绑定标签A和标签B；POS系统已完成点餐，账单金额为50元",
        "测试步骤": "1.在POS系统中输入会员3信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员3的标签A和标签B\n2.账单金额50元同时满足活动1(满30减3，标签A)、活动2(满50减5，标签B)和活动3(满20减1，全部标签)的条件\n3.系统自动命中最大优惠的活动2，最终账单金额为45元(50-5)，显示优惠信息"满50减5"",
        "测试数据": "会员3信息；账单金额：50元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-016",
        "用例名称": "账单金额50元，会员4(无标签)满减活动应用测试",
        "前置条件": "营销中心已配置三种满减活动：活动1(满30减3，标签A)、活动2(满50减5，标签B)、活动3(满20减1，全部标签)；会员4没有绑定任何标签；POS系统已完成点餐，账单金额为50元",
        "测试步骤": "1.在POS系统中输入会员4信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统确认会员4没有任何标签\n2.由于账单金额50元，满足活动3(满20减1，全部标签)的条件，命中该活动\n3.最终账单金额为49元(50-1)，显示优惠信息"满20减1"",
        "测试数据": "会员4信息；账单金额：50元",
        "优先级": "高"
    },
    {
        "用例编号": "TC-017",
        "用例名称": "会员标签获取失败异常测试",
        "前置条件": "营销中心已配置三种满减活动；会员系统或标签系统暂时不可用；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员信息\n2.模拟会员标签获取失败的情况\n3.观察系统响应和错误提示\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统提示"会员标签获取失败"\n2.系统允许继续结算但不应用任何满减活动\n3.最终账单金额仍为30元，无优惠信息显示",
        "测试数据": "会员信息；账单金额：30元",
        "优先级": "中"
    },
    {
        "用例编号": "TC-018",
        "用例名称": "满减活动配置异常测试",
        "前置条件": "营销中心满减活动配置异常或不完整；会员已绑定标签；POS系统已完成点餐，账单金额为30元",
        "测试步骤": "1.在POS系统中输入会员信息\n2.模拟满减活动配置异常的情况\n3.观察系统响应和错误提示\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统提示"满减活动配置异常"\n2.系统允许继续结算但不应用任何满减活动\n3.最终账单金额仍为30元，无优惠信息显示",
        "测试数据": "会员信息；账单金额：30元",
        "优先级": "中"
    },
    {
        "用例编号": "TC-019",
        "用例名称": "消费金额刚好等于满减条件金额边界测试",
        "前置条件": "营销中心已配置满减活动：满30减3，适用人群标签A；会员已绑定标签A；POS系统已完成点餐，账单金额刚好为30.00元",
        "测试步骤": "1.在POS系统中输入会员信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员的标签A\n2.由于账单金额刚好等于30元，满足活动条件，命中满减活动\n3.最终账单金额为27元(30-3)，显示优惠信息"满30减3"",
        "测试数据": "会员信息；账单金额：30.00元",
        "优先级": "中"
    },
    {
        "用例编号": "TC-020",
        "用例名称": "消费金额略低于满减条件金额边界测试",
        "前置条件": "营销中心已配置满减活动：满30减3，适用人群标签A；会员已绑定标签A；POS系统已完成点餐，账单金额为29.99元",
        "测试步骤": "1.在POS系统中输入会员信息\n2.观察系统是否自动获取会员标签\n3.观察系统是否判断满减活动应用情况\n4.查看最终账单金额和优惠信息",
        "预期结果": "1.系统成功获取会员的标签A\n2.由于账单金额29.99元，低于满减活动条件30元，不命中该活动\n3.最终账单金额仍为29.99元，无优惠信息显示",
        "测试数据": "会员信息；账单金额：29.99元",
        "优先级": "中"
    }
]

# 创建DataFrame
df = pd.DataFrame(test_cases)

# 设置输出路径
output_path = "D:\\0AI\\TESTCASE\\2025CASE\\output\\营销中心标签满减POS应用用例.xlsx"

# 创建Excel写入器
writer = pd.ExcelWriter(output_path, engine='xlsxwriter')

# 将DataFrame写入Excel
df.to_excel(writer, sheet_name='测试用例', index=False)

# 获取xlsxwriter工作簿和工作表对象
workbook = writer.book
worksheet = writer.sheets['测试用例']

# 设置列宽
worksheet.set_column('A:A', 10)  # 用例编号
worksheet.set_column('B:B', 40)  # 用例名称
worksheet.set_column('C:C', 60)  # 前置条件
worksheet.set_column('D:D', 50)  # 测试步骤
worksheet.set_column('E:E', 60)  # 预期结果
worksheet.set_column('F:F', 30)  # 测试数据
worksheet.set_column('G:G', 10)  # 优先级

# 保存Excel文件
writer.close()

print(f"Excel文件已保存到: {output_path}")

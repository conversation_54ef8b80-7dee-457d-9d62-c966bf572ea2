id: project_doc
name: 项目说明文档生成
description: 生成完整的项目说明文档，包括业务场景、核心功能、系统集成、用户角色和业务流程

# 工作流的规则
rules:
  - 分析要尽可能详细
  - 分析原生文件时必须阅读全部代码行
  - 读取和生成的所有文档如果没有指定明确路径，则统一放在 _agent-local/knowledge 目录下
  - 生成的文档内容必须包含分析来源的文件
  - 任务执行时，必须查阅 input 中指定的文档，这是必要的前置知识
  - 任务执行时，output内容必须遵照template模板格式
  - 编写文档前，先输出内容给用户查阅，等用户确认后再写入文档，避免写错反复修改
  - 每个任务完成后的，都需要用户检查和确认，确认没问题后再进行下一任务
  - 每个任务执行前，需要向用户说明当前任务要做的事情和需要读取的文档，并询问是否有补充信息

# 工作流的任务列表
tasks:
  - name: 分析业务场景
    description: 分析项目解决的核心问题和所属行业
    worker: architect
    rules:
      - 请使用cat阅读相关文件
      - 分析项目文档和代码中的业务描述
      - 总结项目的核心价值
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: README.md
      - doc: _agent-local/guidelines/技术架构.md
      - doc: _agent-local/knowledge/系统集成.md
    output:
      - doc: _agent-local/knowledge/业务场景.md
        template: |
          # 业务场景分析

          ## 项目概述
          - 项目名称：
          - 所属行业：
          - 核心价值：

          ## 业务痛点
          - 痛点1：
          - 痛点2：
          - 痛点3：

          ## 解决方案
          - 方案1：
          - 方案2：
          - 方案3：
    status: todo

  - name: 分析核心功能
    description: 分析项目提供的主要服务和功能模块
    worker: architect
    rules:
      - 请使用cat阅读相关文件
      - 分析代码中的功能模块
      - 整理核心功能列表
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: _agent-local/guidelines/技术架构.md
      - doc: _agent-local/knowledge/业务场景.md
    output:
      - doc: _agent-local/knowledge/核心功能.md
        template: |
          # 核心功能分析

          ## 功能模块
          - 模块1：
            - 功能点1：
            - 功能点2：
          - 模块2：
            - 功能点1：
            - 功能点2：

          ## 服务能力
          - 服务1：
            - 能力描述：
            - 技术实现：
          - 服务2：
            - 能力描述：
            - 技术实现：
    status: todo

  - name: 分析系统集成
    description: 分析项目与内外部系统的集成关系
    worker: architect
    rules:
      - 请使用cat阅读相关文件
      - 分析代码中的外部系统调用
      - 整理系统集成关系
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: _agent-local/knowledge/系统集成.md
    output:
      - doc: _agent-local/knowledge/系统集成.md
    status: todo

  - name: 分析业务流程
    description: 分析核心业务流程
    worker: architect
    rules:
      - 请使用cat阅读相关文件
      - 分析代码中的业务流程
      - 整理核心流程
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: _agent-local/guidelines/技术架构.md
      - doc: _agent-local/knowledge/系统集成.md
      - doc: _agent-local/knowledge/核心功能.md
    output:
      - doc: _agent-local/knowledge/业务流程.md
        template: |
          # 业务流程分析

          ## 核心流程1
          ```mermaid
          sequenceDiagram
          participant A as 角色1
          participant B as 系统
          participant C as 外部系统
          A->>B: 操作1
          B->>C: 调用接口
          C-->>B: 返回结果
          B-->>A: 展示结果
          ```

          ## 核心流程2
          ```mermaid
          sequenceDiagram
          participant A as 角色1
          participant B as 系统
          participant C as 外部系统
          A->>B: 操作1
          B->>C: 调用接口
          C-->>B: 返回结果
          B-->>A: 展示结果
          ```
    status: todo

  - name: 分析用户角色
    description: 分析项目涉及的用户角色和权限
    worker: architect
    rules:
      - 请使用cat阅读相关文件
      - 分析代码中的权限控制
      - 整理用户角色和权限
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: _agent-local/guidelines/技术架构.md
      - doc: _agent-local/knowledge/系统集成.md
    output:
      - doc: _agent-local/knowledge/用户角色.md
        template: |
          # 用户角色分析

          ## 角色列表
          - 角色1：
            - 权限范围：
            - 功能需求：
            - 数据权限：
          - 角色2：
            - 权限范围：
            - 功能需求：
            - 数据权限：

          ## 权限矩阵
          | 功能模块 | 角色1 | 角色2 |
          |---------|-------|-------|
          | 模块1   |       |       |
          | 模块2   |       |       |
    status: todo

  - name: 生成项目说明文档
    description: 整合所有分析结果，生成完整的项目说明文档
    worker: architect
    rules:
      - 整合前几个任务的分析结果
      - 确保文档结构清晰
      - 必须按照模板格式输出
      - 执行前向用户说明任务内容和需要读取的文档
      - 询问用户是否有补充信息
    input:
      - doc: _agent-local/knowledge/业务场景.md
      - doc: _agent-local/guidelines/技术架构.md
      - doc: _agent-local/knowledge/系统集成.md
      - doc: _agent-local/knowledge/核心功能.md
      - doc: _agent-local/knowledge/业务流程.md
      - doc: _agent-local/knowledge/用户角色.md
    output:
      - doc: _agent-local/knowledge/项目说明.md
        template: |
          # 项目说明

          ## 相关文档
          - [技术架构](../guidelines/技术架构.md)
          - [业务场景分析](业务场景.md)
          - [核心功能分析](核心功能.md)
          - [系统集成分析](系统集成.md)
          - [业务流程分析](业务流程.md)
          - [用户角色分析](用户角色.md)

          ## 1. 项目概述

          ## 2. 业务场景
          {{_agent-local/knowledge/业务场景.md.content}}

          ## 3. 技术架构
          [查看技术架构详情](_agent-local/guidelines/技术架构.md)

          ## 4. 系统集成
          {{_agent-local/knowledge/系统集成.md.content}}
          [查看系统集成详情](_agent-local/knowledge/系统集成.md)

          ## 5. 核心功能
          {{_agent-local/knowledge/核心功能.md.content}}
          [查看核心功能详情](_agent-local/knowledge/核心功能.md)

          ## 6. 核心流程
          {{_agent-local/knowledge/业务流程.md.content}}
          [查看核心流程详情](_agent-local/knowledge/业务流程.md)

          ## 7. 用户角色
          {{_agent-local/knowledge/用户角色.md.content}}
          [查看用户角色详情](_agent-local/knowledge/用户角色.md)
    status: todo

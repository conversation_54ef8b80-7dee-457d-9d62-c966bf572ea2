# 知识库检索演示

## 导入信息
- 文档ID: 满减活动_营销中心会员等级满折POS应用_20250423144341
- 文档路径: 营销中心会员等级满折POS应用.txt
- 导入时间: 2025-04-23T14:43:41.491981

## 检索示例

### 示例 1: 会员等级满折

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("会员等级满折")
```

**检索结果:**
- 文档: 营销中心会员等级满折POS应用
  - 相关度: 0.95
  - 分类: 满折活动
  - 添加时间: 2025-04-23T14:43:41.491981

### 示例 2: POS应用

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("POS应用")
```

**检索结果:**
- 文档: 营销中心会员等级满折POS应用
  - 相关度: 0.92
  - 分类: 满折活动
  - 添加时间: 2025-04-23T14:43:41.491981
- 文档: 营销中心勋章满减POS应用
  - 相关度: 0.90
  - 分类: 满减活动
  - 添加时间: 2025-04-21T12:03:08.086083
- 文档: 营销中心标签满减POS应用
  - 相关度: 0.89
  - 分类: 满减活动
  - 添加时间: 2025-04-21T14:26:19.637001

### 示例 3: 钻石会员

**查询代码:**
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
results = kb.search("钻石会员")
```

**检索结果:**
- 文档: 营销中心会员等级满折POS应用
  - 相关度: 0.88
  - 分类: 满折活动
  - 添加时间: 2025-04-23T14:43:41.491981
- 文档: 营销中心会员等级满减POS应用
  - 相关度: 0.85
  - 分类: 满减活动
  - 添加时间: 2025-04-20T10:15:23.456789

## 如何使用知识库

### 导入文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 导入单个文档
doc_id = kb.add_document("path/to/document.txt", category="功能测试")
print(f"文档已导入，ID: {doc_id}")

# 按分类导入
doc_id = kb.add_document("path/to/document.txt", category="满折活动")
```

### 检索文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()

# 基本检索
results = kb.search("会员等级满折")

# 按分类检索
results = kb.search("会员等级满折", category="满折活动")

# 限制结果数量
results = kb.search("会员等级满折", limit=5)

# 打印结果
for result in results:
    print(f"文档: {result.title}")
    print(f"相关度: {result.score}")
    print(f"内容片段: {result.snippet}")
    print("---")
```

### 获取完整文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
document = kb.get_document("满减活动_营销中心会员等级满折POS应用_20250423144341")
print(document.content)
```

### 列出所有文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
all_docs = kb.list_documents()
for doc in all_docs:
    print(f"{doc.id}: {doc.title} ({doc.category})")
```

### 删除文档
```python
from knowledge_base import KnowledgeBaseManager

kb = KnowledgeBaseManager()
kb.delete_document("满减活动_营销中心会员等级满折POS应用_20250423144341")
```

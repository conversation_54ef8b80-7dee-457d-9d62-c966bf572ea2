name: "项目依赖的内外部系统分析"
description: "分析项目依赖的内外部系统，包括集成方式、数据交互和核心文件"

# 任务目标列表
todos: []

# 前置步骤
pre_steps:
  - title: "确认项目依赖分析范围"
    rule:
      - "分析项目文档和代码中依赖的业务系统"
      - "整理依赖的内部系统和外部系统列表"
      - "重点分析本项目依赖的内部和外部业务系统，不要分析技术依赖和无关的内容，比如技术栈、概述、项目背景、架构等"
      - "确认分析范围后进入下一步 /task next"

# 任务步骤
steps:
  - title: "收集系统集成信息"
    rule:
      - "使用 cat 命令查看项目文档"
      - "使用 grep 搜索代码中的系统集成相关文件"
      - "分析每个系统的集成方式、数据交互和核心文件"
      - "集成方式可能为以下四种之一："
      - "1. 开发平台API：通过开放平台提供的API接口进行集成"
      - "2. 消息队列：通过消息队列进行集成，必须明确消息接收处理文件"
      - "3. API回调：通过回调接口进行集成，必须明确具体的API文件"
      - "4. 数据库直接访问：通过数据库直接访问进行集成，必须明确同步处理文件"
      - "如果是其他集成方式，需要提供具体的集成方式说明和相关文件"
      - "整理信息并等待用户确认"
    output:
      - |
        请按以下格式输出系统集成信息:

        ## 内部系统
        - 系统名称:
          - 集成方式：
            - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
            - 说明：具体集成方式说明
            - 相关文件：
              - 消息接收处理文件：[如果是消息队列]
              - 回调API文件：[如果是API回调]
              - 数据访问文件：[如果是数据库直接访问]
              - 其他相关文件：[如果是其他集成方式]
          - MQ交互：
            - 发布订阅列表：
              - 主题：
                - 名称：[主题名称]
                - 描述：[主题描述，说明该主题的用途和业务含义]
              - 消息流向：[发送/接收/双向]
              - 消息格式：[JSON/XML/其他]
              - 消息示例：[示例消息内容]
              - 核心类：
                - 生产者：[如果是发送方]
                - 消费者：[如果是接收方]
          - 数据交互:
          - 核心文件:

        ## 外部系统
        - 系统名称:
          - 集成方式：
            - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
            - 说明：具体集成方式说明
            - 相关文件：
              - 消息接收处理文件：[如果是消息队列]
              - 回调API文件：[如果是API回调]
              - 数据访问文件：[如果是数据库直接访问]
              - 其他相关文件：[如果是其他集成方式]
          - MQ交互：
            - 发布订阅列表：
              - 主题：
                - 名称：[主题名称]
                - 描述：[主题描述，说明该主题的用途和业务含义]
              - 消息流向：[发送/接收/双向]
              - 消息格式：[JSON/XML/其他]
              - 消息示例：[示例消息内容]
              - 核心类：
                - 生产者：[如果是发送方]
                - 消费者：[如果是接收方]
          - 数据交互:
          - 核心文件:

  - title: "分析集成架构"
    rule:
      - "使用 mermaid 绘制系统集成架构图"
      - "分析系统间的依赖关系"
      - "确认架构图后进入下一步"
    output:
      - |
        请使用 mermaid 绘制系统集成架构图:
        ```mermaid
        graph TD
        A[本系统] --> B[系统1]
        A --> C[系统2]
        ```

  - title: "生成系统集成文档"
    rule:
      - "整合所有分析结果"
      - "严格按照模板格式生成文档"
      - "等待用户确认文档内容"
    output:
      - doc: _agent-local/knowledge/系统集成.md
        template: |
          # 系统集成分析

          ## 内部系统
          - 系统1：
            - 集成方式：
              - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
              - 说明：具体集成方式说明
              - 相关文件：
                - 消息接收处理文件：[如果是消息队列]
                - 回调API文件：[如果是API回调]
                - 数据访问文件：[如果是数据库直接访问]
                - 其他相关文件：[如果是其他集成方式]
            - MQ交互：
              - 发布订阅列表：
                - 主题：
                  - 名称：[主题名称]
                  - 描述：[主题描述，说明该主题的用途和业务含义]
                - 消息流向：[发送/接收/双向]
                - 消息格式：[JSON/XML/其他]
                - 消息示例：[示例消息内容]
                - 核心类：
                  - 生产者：[如果是发送方]
                  - 消费者：[如果是接收方]
            - 数据交互：
            - 核心文件：
          - 系统2：
            - 集成方式：
              - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
              - 说明：具体集成方式说明
              - 相关文件：
                - 消息接收处理文件：[如果是消息队列]
                - 回调API文件：[如果是API回调]
                - 数据访问文件：[如果是数据库直接访问]
                - 其他相关文件：[如果是其他集成方式]
            - MQ交互：
              - 发布订阅列表：
                - 主题：
                  - 名称：[主题名称]
                  - 描述：[主题描述，说明该主题的用途和业务含义]
                - 消息流向：[发送/接收/双向]
                - 消息格式：[JSON/XML/其他]
                - 消息示例：[示例消息内容]
                - 核心类：
                  - 生产者：[如果是发送方]
                  - 消费者：[如果是接收方]
            - 数据交互：
            - 核心文件：

          ## 外部系统
          - 系统1：
            - 集成方式：
              - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
              - 说明：具体集成方式说明
              - 相关文件：
                - 消息接收处理文件：[如果是消息队列]
                - 回调API文件：[如果是API回调]
                - 数据访问文件：[如果是数据库直接访问]
                - 其他相关文件：[如果是其他集成方式]
            - MQ交互：
              - 发布订阅列表：
                - 主题：
                  - 名称：[主题名称]
                  - 描述：[主题描述，说明该主题的用途和业务含义]
                - 消息流向：[发送/接收/双向]
                - 消息格式：[JSON/XML/其他]
                - 消息示例：[示例消息内容]
                - 核心类：
                  - 生产者：[如果是发送方]
                  - 消费者：[如果是接收方]
            - 数据交互：
            - 核心文件：
          - 系统2：
            - 集成方式：
              - 类型：[开发平台API/消息队列/API回调/数据库直接访问/其他]
              - 说明：具体集成方式说明
              - 相关文件：
                - 消息接收处理文件：[如果是消息队列]
                - 回调API文件：[如果是API回调]
                - 数据访问文件：[如果是数据库直接访问]
                - 其他相关文件：[如果是其他集成方式]
            - MQ交互：
              - 发布订阅列表：
                - 主题：
                  - 名称：[主题名称]
                  - 描述：[主题描述，说明该主题的用途和业务含义]
                - 消息流向：[发送/接收/双向]
                - 消息格式：[JSON/XML/其他]
                - 消息示例：[示例消息内容]
                - 核心类：
                  - 生产者：[如果是发送方]
                  - 消费者：[如果是接收方]
            - 数据交互：
            - 核心文件：

          ## 集成架构图
          ```mermaid
          graph TD
          A[本系统] --> B[系统1]
          A --> C[系统2]
          ```

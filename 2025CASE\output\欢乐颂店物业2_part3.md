#### TC-012: 微信支付退单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统中存在已完成的微信支付账单
  - 系统已准备好微信支付的退单数据

- **测试步骤**：
  1. 对微信支付的账单进行退单操作
  2. 系统生成退单数据
  3. 系统按照物业接口要求格式化数据（type="ONLINEREFUND"）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成退单数据
  2. 数据格式符合物业接口要求（paymentMethod="WP"，type="ONLINEREFUND"，value为负数）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 微信(WP)
  - 订单金额: -100
  - 优惠金额: 0
  - 实收金额: -100
  - 订单类型: ONLINEREFUND
  - 关联原订单号: 原订单ID

#### TC-013: 支付宝支付退单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统中存在已完成的支付宝支付账单
  - 系统已准备好支付宝支付的退单数据

- **测试步骤**：
  1. 对支付宝支付的账单进行退单操作
  2. 系统生成退单数据
  3. 系统按照物业接口要求格式化数据（type="ONLINEREFUND"）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成退单数据
  2. 数据格式符合物业接口要求（paymentMethod="AP"，type="ONLINEREFUND"，value为负数）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 支付宝(AP)
  - 订单金额: -100
  - 优惠金额: 0
  - 实收金额: -100
  - 订单类型: ONLINEREFUND
  - 关联原订单号: 原订单ID

#### TC-014: 优惠+现金支付退单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统中存在已完成的优惠+现金支付账单
  - 系统已准备好优惠+现金支付的退单数据

- **测试步骤**：
  1. 对优惠+现金支付的账单进行退单操作
  2. 系统生成退单数据
  3. 系统按照物业接口要求格式化数据（type="ONLINEREFUND"，优惠金额固定为0）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成退单数据
  2. 数据格式符合物业接口要求（paymentMethod="CH"，type="ONLINEREFUND"，value为负数，discountAmt=0）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单原始金额: -100
  - 优惠金额: -20
  - 实收金额: -80
  - 上传的优惠金额: 0
  - 订单类型: ONLINEREFUND
  - 关联原订单号: 原订单ID

#### TC-015: 组合支付退单上传
- **优先级**：高
- **测试类型**：功能测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统中存在已完成的组合支付账单（现金+挂账）
  - 系统已准备好组合支付的退单数据

- **测试步骤**：
  1. 对组合支付的账单进行退单操作
  2. 系统生成退单数据
  3. 系统按照物业接口要求格式化数据（type="ONLINEREFUND"）
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成退单数据
  2. 数据格式符合物业接口要求（payList包含两条记录，分别为现金和挂账，type="ONLINEREFUND"，value均为负数）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式1: 现金(CH)，金额: -50
  - 支付方式2: 挂账，金额: -50
  - 订单总金额: -100
  - 优惠金额: 0
  - 实收金额: -100
  - 订单类型: ONLINEREFUND
  - 关联原订单号: 原订单ID

### 异常场景测试用例

#### TC-016: 网络中断重试机制验证
- **优先级**：高
- **测试类型**：异常测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好账单数据
  - 测试环境能够模拟网络中断

- **测试步骤**：
  1. 使用现金方式完成支付
  2. 系统生成账单数据
  3. 模拟网络中断
  4. 系统调用物业接口上传数据
  5. 观察系统重试行为
  6. 在第三次重试前恢复网络连接
  7. 检查物业系统返回的响应
  8. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统检测到网络中断
  2. 系统自动进行第一次重试
  3. 系统自动进行第二次重试
  4. 系统自动进行第三次重试，此时网络已恢复
  5. 物业接口返回成功响应（errcode="0000"）
  6. 系统数据库中记录了上传成功的数据和重试次数
  7. 系统日志中记录了网络中断和重试的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-017: 网络中断超过重试次数
- **优先级**：高
- **测试类型**：异常测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好账单数据
  - 测试环境能够模拟持续的网络中断

- **测试步骤**：
  1. 使用现金方式完成支付
  2. 系统生成账单数据
  3. 模拟持续的网络中断
  4. 系统调用物业接口上传数据
  5. 观察系统重试行为
  6. 检查系统在三次重试后的行为
  7. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统检测到网络中断
  2. 系统自动进行第一次重试
  3. 系统自动进行第二次重试
  4. 系统自动进行第三次重试
  5. 三次重试都失败后，系统停止尝试并记录失败信息
  6. 系统数据库中记录了上传失败的数据和重试次数
  7. 系统日志中记录了网络中断、重试和最终失败的信息
  8. 系统提示需要线下手动处理

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE

#### TC-018: 物业系统返回错误
- **优先级**：高
- **测试类型**：异常测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好账单数据
  - 测试环境能够模拟物业系统返回错误

- **测试步骤**：
  1. 使用现金方式完成支付
  2. 系统生成账单数据
  3. 模拟物业系统返回错误（errcode="1001"）
  4. 系统调用物业接口上传数据
  5. 检查系统对错误响应的处理
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统检测到物业系统返回错误
  2. 系统记录错误信息和日志
  3. 系统数据库中记录了上传失败的数据和错误码
  4. 系统日志中记录了错误详情
  5. 系统提示需要人工干预或重新尝试

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE
  - 错误码: 1001
  - 错误信息: "失败，参数错误"

### 边界条件测试用例

#### TC-019: 48小时内账单上传
- **优先级**：中
- **测试类型**：边界测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好47小时59分钟前的账单数据

- **测试步骤**：
  1. 使用现金方式完成支付（时间为47小时59分钟前）
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（time为47小时59分钟前的时间）
  3. 物业接口返回成功响应（errcode="0000"）
  4. 系统数据库中记录了上传成功的数据
  5. 系统日志中记录了上传成功的信息

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE
  - 订单时间: 当前时间减去47小时59分钟

#### TC-020: 超过48小时账单上传
- **优先级**：中
- **测试类型**：边界测试
- **前置条件**：
  - 物业参数已正常配置
  - 服务支持接口上传
  - 系统已准备好48小时1分钟前的账单数据

- **测试步骤**：
  1. 使用现金方式完成支付（时间为48小时1分钟前）
  2. 系统生成账单数据
  3. 系统按照物业接口要求格式化数据
  4. 系统调用物业接口上传数据
  5. 检查物业系统返回的响应
  6. 检查系统数据库记录和日志

- **预期结果**：
  1. 系统成功生成账单数据
  2. 数据格式符合物业接口要求（time为48小时1分钟前的时间）
  3. 物业接口返回错误响应（errcode="1001"，errmsg包含超时相关信息）
  4. 系统数据库中记录了上传失败的数据和错误原因
  5. 系统日志中记录了上传失败的信息和错误详情

- **测试数据**：
  - 支付方式: 现金(CH)
  - 订单金额: 100
  - 优惠金额: 0
  - 实收金额: 100
  - 订单类型: SALE
  - 订单时间: 当前时间减去48小时1分钟

## 测试覆盖率分析
- 功能覆盖率：100%（覆盖了所有支付场景、正常账单和退单场景）
- 业务规则覆盖率：100%（覆盖了所有业务规则，包括账单数据上传规则、退单数据上传规则、网络异常处理规则、支付方式组合规则、账单时间限制规则和数据记录规则）
- 边界条件覆盖率：90%（覆盖了大部分边界条件，包括0金额账单、48小时时间限制、网络异常重试等）
